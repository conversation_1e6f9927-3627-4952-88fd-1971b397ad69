<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 23.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="113px" height="55px" viewBox="0 0 113 55" style="enable-background:new 0 0 113 55;" xml:space="preserve">
<style type="text/css">
	.st0{fill-rule:evenodd;clip-rule:evenodd;fill:none;}
	.st1{filter:url(#Adobe_OpacityMaskFilter);}
	.st2{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
	.st3{mask:url(#mask-2_1_);fill:#FF5A5F;}
</style>
<g id="Page-1">
	<g transform="translate(-966.000000, -73.000000)">
		<g>
			<g id="Content" transform="translate(732.000000, 30.000000)">
				<g id="Logos" transform="translate(0.000000, 43.000000)">
					<g id="logo-airbnb" transform="translate(234.000000, 0.000000)">
						<g id="bg">
							<rect x="0.7" y="0" class="st0" width="111.8" height="55"/>
						</g>
						<defs>
							<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="0.5" y="9.9" width="112.6" height="34.8">
								<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
							</filter>
						</defs>
						<mask maskUnits="userSpaceOnUse" x="0.5" y="9.9" width="112.6" height="34.8" id="mask-2_1_">
							<g class="st1">
								<rect id="path-1_1_" x="0.7" y="0" class="st2" width="111.8" height="55"/>
							</g>
						</mask>
						<path id="Fill-2" class="st3" d="M59.8,18.6c0,1.3-1,2.3-2.3,2.3c-1.3,0-2.3-1-2.3-2.3s1-2.3,2.3-2.3
							C58.8,16.4,59.8,17.4,59.8,18.6z M50.4,23.2c0,0.2,0,0.6,0,0.6s-1.1-1.4-3.4-1.4c-3.8,0-6.8,2.9-6.8,6.9c0,4,3,6.9,6.8,6.9
							c2.4,0,3.4-1.4,3.4-1.4v0.6c0,0.3,0.2,0.5,0.5,0.5h2.8V22.7c0,0-2.6,0-2.8,0C50.6,22.7,50.4,23,50.4,23.2z M50.4,31.6
							c-0.5,0.8-1.6,1.4-2.8,1.4c-2.3,0-4-1.4-4-3.8c0-2.4,1.7-3.8,4-3.8c1.2,0,2.4,0.7,2.8,1.4V31.6z M55.8,35.8h3.4V22.7h-3.4
							V35.8z M106.3,22.4c-2.3,0-3.4,1.4-3.4,1.4v-7.3h-3.4v19.4c0,0,2.6,0,2.8,0c0.3,0,0.5-0.2,0.5-0.5v-0.6c0,0,1.1,1.4,3.4,1.4
							c3.8,0,6.8-2.9,6.8-6.9S110.1,22.4,106.3,22.4z M105.7,33c-1.3,0-2.3-0.7-2.8-1.4v-4.7c0.5-0.7,1.7-1.4,2.8-1.4
							c2.3,0,4,1.4,4,3.8S108,33,105.7,33z M97.7,28v7.8h-3.4v-7.4c0-2.2-0.7-3-2.6-3c-1,0-2.1,0.5-2.7,1.3v9.1h-3.4V22.7h2.7
							c0.3,0,0.5,0.2,0.5,0.5v0.6c1-1,2.3-1.4,3.6-1.4c1.5,0,2.7,0.4,3.7,1.3C97.3,24.6,97.7,25.9,97.7,28z M77.4,22.4
							c-2.3,0-3.4,1.4-3.4,1.4v-7.3h-3.4v19.4c0,0,2.6,0,2.8,0c0.3,0,0.5-0.2,0.5-0.5v-0.6c0,0,1.1,1.4,3.4,1.4
							c3.8,0,6.8-2.9,6.8-6.9C84.3,25.3,81.3,22.4,77.4,22.4z M76.9,33c-1.3,0-2.3-0.7-2.8-1.4v-4.7c0.5-0.7,1.7-1.4,2.8-1.4
							c2.3,0,4,1.4,4,3.8S79.1,33,76.9,33z M67.7,22.4c1,0,1.5,0.2,1.5,0.2v3.1c0,0-2.8-0.9-4.6,1v9.2h-3.4V22.7c0,0,2.6,0,2.8,0
							c0.3,0,0.5,0.2,0.5,0.5v0.6C65.3,23,66.7,22.4,67.7,22.4z M32.7,34.6c-0.2-0.4-0.4-0.9-0.5-1.3c-0.3-0.6-0.6-1.2-0.8-1.8l0,0
							c-2.4-5.2-5-10.5-7.8-15.7l-0.1-0.2c-0.3-0.5-0.6-1.1-0.8-1.6c-0.4-0.6-0.7-1.3-1.3-1.9c-1.1-1.4-2.7-2.2-4.5-2.2
							c-1.8,0-3.3,0.8-4.5,2.1c-0.5,0.6-0.9,1.3-1.3,1.9c-0.3,0.6-0.6,1.1-0.8,1.6l-0.1,0.2C7.4,21,4.8,26.3,2.4,31.5l0,0.1
							c-0.2,0.6-0.5,1.1-0.8,1.8c-0.2,0.4-0.4,0.8-0.5,1.3c-0.5,1.3-0.6,2.5-0.4,3.8c0.4,2.6,2.1,4.8,4.6,5.8C6,44.5,7,44.7,8,44.7
							c0.3,0,0.6,0,0.9-0.1c1.2-0.1,2.4-0.5,3.5-1.2c1.4-0.8,2.8-2,4.4-3.6c1.5,1.7,3,2.8,4.4,3.6c1.2,0.7,2.4,1,3.5,1.2
							c0.3,0,0.6,0.1,0.9,0.1c1,0,2-0.2,2.8-0.6c2.5-1,4.2-3.2,4.6-5.8C33.3,37.1,33.1,35.9,32.7,34.6z M16.8,36.4
							c-1.9-2.4-3.1-4.6-3.6-6.5c-0.2-0.8-0.2-1.5-0.1-2.1c0.1-0.6,0.3-1,0.6-1.5c0.7-0.9,1.8-1.5,3.1-1.5c1.3,0,2.5,0.6,3.1,1.5
							c0.3,0.4,0.5,0.9,0.6,1.5c0.1,0.6,0.1,1.4-0.1,2.1C19.9,31.8,18.7,34,16.8,36.4z M30.8,38.1c-0.2,1.8-1.5,3.4-3.2,4.1
							c-0.8,0.3-1.8,0.5-2.7,0.3c-0.9-0.1-1.8-0.4-2.7-0.9c-1.3-0.7-2.5-1.8-4-3.4c2.3-2.8,3.7-5.4,4.3-7.7c0.2-1.1,0.3-2.1,0.2-3
							c-0.1-0.9-0.5-1.7-0.9-2.4c-1.1-1.6-2.9-2.5-5-2.5c-2,0-3.9,0.9-5,2.5c-0.5,0.7-0.8,1.5-0.9,2.4c-0.1,0.9-0.1,1.9,0.2,3
							c0.5,2.3,2,4.9,4.3,7.7c-1.4,1.6-2.7,2.7-4,3.4c-0.9,0.5-1.8,0.8-2.7,0.9c-1,0.1-1.9,0-2.7-0.3c-1.7-0.7-3-2.3-3.2-4.1
							c-0.1-0.9,0-1.7,0.3-2.7c0.1-0.3,0.3-0.7,0.5-1.1c0.2-0.6,0.5-1.1,0.8-1.7l0-0.1c2.4-5.2,5-10.5,7.7-15.6l0.1-0.2
							c0.3-0.5,0.6-1.1,0.8-1.6c0.3-0.6,0.6-1.1,1-1.5c0.7-0.8,1.7-1.3,2.8-1.3s2.1,0.5,2.8,1.3c0.4,0.5,0.7,1,1,1.5
							c0.3,0.5,0.6,1.1,0.8,1.6l0.1,0.2c2.7,5.2,5.3,10.5,7.7,15.7v0c0.3,0.6,0.5,1.2,0.8,1.7c0.2,0.4,0.4,0.8,0.5,1.1
							C30.9,36.3,31,37.2,30.8,38.1L30.8,38.1z"/>
					</g>
				</g>
			</g>
		</g>
	</g>
</g>
</svg>
