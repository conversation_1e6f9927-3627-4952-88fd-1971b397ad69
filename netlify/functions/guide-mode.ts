import { <PERSON><PERSON> } from "@netlify/functions";
import { SYSTEM_PROMPTS } from "../../src/lib/ai-copilot/config";
import type { CopilotMode } from "../../src/lib/ai-copilot/types";
import { GoogleGenAI, HarmCategory, HarmBlockThreshold } from "@google/genai";

/**
 * Guide Mode Netlify Function
 *
 * This function handles requests for the AI Career Advisor guide mode.
 * It processes user messages and returns AI-generated responses.
 */
export const handler: Handler = async (event) => {
  // Define allowed origin and standard headers
  const allowedOrigin = "https://praxjobs.com";
  const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": allowedOrigin,
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };

  // Handle preflight requests for CORS
  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 204,
      headers,
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== "POST") {
    return {
      statusCode: 405, // Method Not Allowed
      headers: { ...headers, "Content-Type": "application/json" },
      body: JSON.stringify({ error: "Method Not Allowed" }),
    };
  }

  // --- Main Logic ---
  try {
    // Validate body first
    if (!event.body) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: "Request body is required" }),
      };
    }

    const body = JSON.parse(event.body);
    // Destructure parameters from the body, with defaults
    const {
      message,
      chatId = null, // New parameter for chat session ID
      mode = "careerDiscussion",
      stream = false,
      isNewChat = false // Flag to indicate if this is a new chat session
    } = body;

    // Validate message presence
    if (!message || typeof message !== "string") {
      return {
        statusCode: 400,
        headers: { ...headers, "Content-Type": "application/json" },
        body: JSON.stringify({
          error: "Message is required and must be a string",
        }),
      };
    }

    // Debug log to help diagnose issues
    console.log(`Guide mode request: mode=${mode}, message length=${message.length}, chatId=${chatId}, isNewChat=${isNewChat}, stream=${stream}`);

    // Get the API key from environment variables
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      console.error("Gemini API Key is missing");
      return {
        statusCode: 503, // Service Unavailable
        headers: { ...headers, "Content-Type": "application/json" },
        body: JSON.stringify({
          error: "Guide Mode service is not available - missing API key",
        }),
      };
    }

    // Initialize the Gemini API client with the new SDK
    const ai = new GoogleGenAI({ apiKey });

    // Determine the model based on the mode
    const modelName =
      mode === "heroConcise"
        ? "gemini-1.5-flash" // Use specified model for heroConcise
        : "gemini-2.0-flash-lite"; // Default model

    console.log(`Using model: ${modelName} for mode: ${mode}`);

    // Configure safety settings
    const safetySettings = [
      {
        category: HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
    ];

    // Configure generation parameters
    const generationConfig = {
      temperature: 0.9, // Adjust creativity/randomness
      topK: 1,
      topP: 1,
      maxOutputTokens: 2048, // Adjust response length limit
    };

    // Determine the system prompt based on the mode
    const systemPromptKey =
      mode === "heroConcise" ? "heroConcise" : "careerDiscussion";
    const systemPromptText =
      SYSTEM_PROMPTS[systemPromptKey as CopilotMode] ||
      SYSTEM_PROMPTS.careerDiscussion; // Fallback

    // Create a chat session with the system prompt
    const chat = ai.chats.create({
      model: modelName,
      config: {
        systemInstruction: systemPromptText,
        temperature: generationConfig.temperature,
        topK: generationConfig.topK,
        topP: generationConfig.topP,
        maxOutputTokens: generationConfig.maxOutputTokens,
        safetySettings,
      },
    });

    // Log what we're sending to help with debugging
    console.log(`Sending message to Gemini API using chat session with systemInstruction length: ${systemPromptText.length}`);
    console.log(`Message: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);
    console.log(`isNewChat: ${isNewChat}`);
    console.log(`Mode: ${mode}`);

    // Handle streaming if requested
    if (stream) {
      // For streaming, we need to use sendMessageStream
      const streamingResult = await chat.sendMessageStream({
        message,
      });

      // Process the stream and return chunks
      // Note: Streaming is not fully implemented in this version
      // This would require setting up a proper streaming response
      console.log("Streaming requested but not fully implemented");

      // For now, just get the full response and return it
      let responseText = "";
      for await (const chunk of streamingResult) {
        responseText += chunk.text;
      }

      return {
        statusCode: 200,
        headers: { ...headers, "Content-Type": "application/json" },
        body: JSON.stringify({
          success: true,
          data: responseText,
        }),
      };
    } else {
      // For non-streaming, use the standard sendMessage method
      const result = await chat.sendMessage({
        message,
      });

      if (!result) {
        console.error("Gemini API did not return a response");
        throw new Error("No response received from the AI model.");
      }

      const responseText = result.text;

      // Return the successful response
      return {
        statusCode: 200,
        headers: { ...headers, "Content-Type": "application/json" },
        body: JSON.stringify({ success: true, data: responseText }),
      };
    }
  } catch (error: any) {
    console.error("Guide Mode Error:", error);

    // Check if it's a Gemini API error (e.g., blocked due to safety)
    if (error.message && error.message.includes("response was blocked")) {
      return {
        statusCode: 400, // Bad Request might be appropriate here
        headers: { ...headers, "Content-Type": "application/json" },
        body: JSON.stringify({
          error: "Request blocked due to safety settings.",
          details: error.message,
        }),
      };
    }

    // General internal error
    return {
      statusCode: 500,
      headers: { ...headers, "Content-Type": "application/json" },
      body: JSON.stringify({
        error: "Internal server error",
        details: error.message || "An unknown error occurred",
      }),
    };
  }
};
