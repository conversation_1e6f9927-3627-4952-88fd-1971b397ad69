import { Handler } from "@netlify/functions"; // Added HandlerEvent for typing
import {
  initializeFirebaseAdmin,
  // verifyFirebaseToken removed as we're bypassing token validation
} from "../../src/lib/firebaseAdmin"; // Import necessary functions

export const handler: Handler = async (event, context) => {
  // Define allowed origin early
  const allowedOrigin = process.env.PROD ? "https://praxjobs.com" : "http://localhost:4321";

  // Define CORS headers early
  const corsHeaders = {
    "Access-Control-Allow-Origin": allowedOrigin,
    "Access-Control-Allow-Headers": "Content-Type, Authorization", // Added Authorization for token passing
    "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
  };

  console.log("[Auth Function Debug] Handler invoked for path:", event.path); // Log invocation

  // Initialize Firebase Admin SDK (ensure it's ready)
  try {
    console.log("[Auth Function Debug] Initializing Firebase Admin..."); // Log init attempt
    await initializeFirebaseAdmin();
    console.log(
      "[Auth Function Debug] Firebase Admin initialized successfully."
    ); // Log init success
  } catch (initError) {
    console.error(
      "[Auth Function Debug] Firebase Admin Init Error:",
      initError
    ); // Log init error
    // Return a generic error to avoid leaking details
    // CRITICAL: Return CORS headers even on initialization failure!
    return {
      statusCode: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" }, // Add CORS headers here
      body: JSON.stringify({ error: "Internal server configuration error." }),
    };
  }

  // Handle preflight requests (CORS)
  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 204, // No Content
      headers: corsHeaders, // Use the defined CORS headers
    };
  }

  // Simple routing based on path
  // Note: For more complex routing, consider a framework or library
  if (event.path.endsWith("/login")) {
    // --- Handle POST requests to /login ---
    if (event.httpMethod === "POST") {
      try {
        const body = JSON.parse(event.body || "{}");
        console.log("[Auth Function Debug] Handling /login POST request."); // Log POST handling
        // Try to extract token from Authorization header first (like upload-resume)
        let token: string | undefined = undefined;
        const authHeader = event.headers.authorization;
        console.log(
          "[Auth Function Debug] Authorization Header:",
          authHeader ? authHeader.substring(0, 15) + "..." : "Not present"
        ); // Log header (truncated)
        if (authHeader && authHeader.startsWith("Bearer ")) {
          token = authHeader.substring(7);
          console.log(
            "[Auth Function Debug] Token extracted from Header:",
            token ? token.substring(0, 20) + "..." : "null"
          ); // Log token from header
        } else {
          // No fallback to body for security reasons
        }

        if (!token) {
          console.log(
            "[Auth Function Debug] No token found in header or body."
          ); // Log missing token
          return {
            statusCode: 400, // Bad Request
            headers: { ...corsHeaders, "Content-Type": "application/json" },
            body: JSON.stringify({ error: "No token provided" }),
          };
        }

        // --- Verify Firebase ID Token ---
        console.log("[Auth Function Debug] Verifying Firebase ID Token...");
        const admin = (await import("firebase-admin")).default; // Import admin here
        const decodedToken = await admin.auth().verifyIdToken(token);
        console.log("[Auth Function Debug] Token verified successfully for user:", decodedToken.uid);

        // --- Create and Set Session Cookie ---
        const expiresIn = 60 * 60 * 24 * 5 * 1000; // 5 days
        const sessionCookie = await admin.auth().createSessionCookie(token, { expiresIn });

        // Set the session cookie in the response headers
        const cookieOptions = [
          `session=${sessionCookie}`,
          `Max-Age=${expiresIn / 1000}`, // Max-Age in seconds
          'Path=/',
          'SameSite=Strict',
          'HttpOnly', // Crucial for security
        ];

        if (process.env.PROD) {
          cookieOptions.push('Secure');
        }

        return {
          statusCode: 200, // OK
          headers: {
            ...corsHeaders,
            "Content-Type": "application/json",
            "Set-Cookie": cookieOptions.join('; '),
          },
          body: JSON.stringify({
            success: true,
            message: "Login successful, session cookie set.",
          }),
        };
      } catch (error) {
        console.error("[Auth Function Debug] Login error:", error);

        // Use generic error messages for security
        if (error.code && error.code.startsWith('auth/')) {
          return {
            statusCode: 401, // Unauthorized
            headers: { ...corsHeaders, "Content-Type": "application/json" },
            body: JSON.stringify({ error: "Authentication token is invalid or expired." }),
          };
        }

        // Log errors to your monitoring service in production instead of console
        // e.g., logToSentry(error);

        return {
          statusCode: 500, // Internal Server Error
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          // Avoid sending detailed error messages to the client in production
          body: JSON.stringify({
            error: "An internal server error occurred during login.",
          }),
        };
      }
    }

    // --- Handle GET requests to /login ---
    if (event.httpMethod === "GET") {
      // Typically, a GET request to a /login endpoint might redirect or serve a page.
      // Returning JSON might be for API health checks or info.
      return {
        statusCode: 200, // OK
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        body: JSON.stringify({
          message: "Login endpoint is active. Use POST to log in.",
        }), // More informative message
      };
    }

    // --- Handle other methods to /login ---
    return {
      statusCode: 405, // Method Not Allowed
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      body: JSON.stringify({
        error: `Method ${event.httpMethod} not allowed for /login`,
      }),
    };
  } else if (event.path.endsWith("/logout")) {
    // --- Handle POST requests to /logout ---
    if (event.httpMethod === "POST") {
      // Invalidate the session cookie by setting it to an empty value and expiring it immediately
      const cookieOptions = [
        `session=`,
        `Max-Age=0`,
        'Path=/',
        'SameSite=Strict',
        'Secure',
        'HttpOnly',
      ];

      return {
        statusCode: 200,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
          "Set-Cookie": cookieOptions.join('; '),
        },
        body: JSON.stringify({
          success: true,
          message: "Logout successful, session cookie cleared.",
        }),
      };
    }

    // --- Handle other methods to /logout ---
    return {
      statusCode: 405, // Method Not Allowed
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      body: JSON.stringify({
        error: `Method ${event.httpMethod} not allowed for /logout`,
      }),
    };
  }

  // --- Handle unknown endpoints ---
  return {
    statusCode: 404, // Not Found
    headers: { ...corsHeaders, "Content-Type": "application/json" },
    body: JSON.stringify({ error: `Endpoint not found: ${event.path}` }), // More specific error
  };
};
