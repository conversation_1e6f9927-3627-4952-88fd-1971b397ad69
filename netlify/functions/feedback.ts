import { <PERSON><PERSON> } from "@netlify/functions";
import { Resend } from "resend";

// Initialize Resend with API key
const resend = new Resend(process.env.RESEND_API_KEY);

// Define expected structure of the request body
interface FeedbackRequestBody {
  feedback: string;
  rating?: number;
  category?: string;
  userId?: string;
  userEmail?: string;
  source?: string;
}

// CORS headers for cross-origin requests
const headers = {
  "Access-Control-Allow-Origin": "*", // Consider restricting this in production
  "Access-Control-Allow-Headers": "Content-Type",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Content-Type": "application/json",
};

export const handler: Handler = async (event, context) => {
  // Handle preflight OPTIONS request
  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 204, // No content
      headers,
      body: "",
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== "POST") {
    return {
      statusCode: 405, // Method Not Allowed
      headers,
      body: JSON.stringify({ error: "Method not allowed" }),
    };
  }

  try {
    // Check if event.body exists
    if (!event.body) {
      return {
        statusCode: 400, // Bad Request
        headers,
        body: JSON.stringify({ error: "Request body is required" }),
      };
    }

    // Parse request body safely
    let parsedBody: FeedbackRequestBody;
    try {
      parsedBody = JSON.parse(event.body);
    } catch (parseError) {
      return {
        statusCode: 400, // Bad Request
        headers,
        body: JSON.stringify({ error: "Invalid JSON in request body" }),
      };
    }

    const { feedback, rating, category, userId, userEmail, source } = parsedBody;

    // Validate input fields
    if (!feedback) {
      return {
        statusCode: 400, // Bad Request
        headers,
        body: JSON.stringify({
          error: "Missing required field: feedback",
        }),
      };
    }

    // Prepare email content
    const emailContent = `
      <h2>New Feedback Received</h2>
      <p><strong>Feedback:</strong> ${feedback}</p>
      ${rating !== undefined ? `<p><strong>Rating:</strong> ${rating}/5</p>` : ''}
      ${category ? `<p><strong>Category:</strong> ${category}</p>` : ''}
      ${source ? `<p><strong>Source:</strong> ${source}</p>` : ''}
      <p><strong>User ID:</strong> ${userId || 'Anonymous'}</p>
      <p><strong>User Email:</strong> ${userEmail || 'Not provided'}</p>
      <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
    `;

    // Send email using Resend
    const { data, error } = await resend.emails.send({
      from: 'PraxJobs Feedback <<EMAIL>>',
      to: '<EMAIL>', // Your receiving email address
      subject: `New PraxJobs Feedback${category ? ` - ${category}` : ''}`,
      html: emailContent,
      reply_to: userEmail || '<EMAIL>',
    });

    if (error) {
      console.error('Error sending feedback email:', error);
      return {
        statusCode: 500, // Internal Server Error
        headers,
        body: JSON.stringify({
          error: "Failed to send feedback email",
        }),
      };
    }

    // Return success response
    return {
      statusCode: 200, // OK
      headers,
      body: JSON.stringify({
        message: "Feedback submitted successfully. Thank you for your input!",
        id: data?.id,
      }),
    };
  } catch (error) {
    // Log the detailed error to your monitoring service
    console.error('Error submitting feedback:', error);

    // Return a generic internal server error message to the client
    return {
      statusCode: 500, // Internal Server Error
      headers,
      body: JSON.stringify({
        error: "An internal server error occurred while submitting feedback.",
      }),
    };
  }
};
