import { <PERSON><PERSON> } from "@netlify/functions";
import { Resend } from "resend";

// Initialize Resend with API key
const resend = new Resend(process.env.RESEND_API_KEY);

// Define expected structure of the request body
interface ContactRequestBody {
  name: string;
  email: string;
  subject: string;
  message: string;
}

// CORS headers for cross-origin requests
const headers = {
  "Access-Control-Allow-Origin": "*", // Consider restricting this in production
  "Access-Control-Allow-Headers": "Content-Type",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Content-Type": "application/json",
};

export const handler: Handler = async (event, context) => {
  // Handle preflight OPTIONS request
  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 204, // No content
      headers,
      body: "",
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== "POST") {
    return {
      statusCode: 405, // Method Not Allowed
      headers,
      body: JSON.stringify({ error: "Method not allowed" }),
    };
  }

  try {
    // Check if event.body exists
    if (!event.body) {
      return {
        statusCode: 400, // Bad Request
        headers,
        body: JSON.stringify({ error: "Request body is required" }),
      };
    }

    // Parse request body safely
    let parsedBody: ContactRequestBody;
    try {
      parsedBody = JSON.parse(event.body);
    } catch (parseError) {
      return {
        statusCode: 400, // Bad Request
        headers,
        body: JSON.stringify({ error: "Invalid JSON in request body" }),
      };
    }

    const { name, email, subject, message } = parsedBody;

    // Validate input fields
    if (!name || !email || !subject || !message) {
      return {
        statusCode: 400, // Bad Request
        headers,
        body: JSON.stringify({
          error: "Missing required fields: name, email, subject, and message",
        }),
      };
    }

    // Validate email format (basic check)
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return {
        statusCode: 400, // Bad Request
        headers,
        body: JSON.stringify({ error: "Invalid email format" }),
      };
    }

    // Prepare email content
    const emailContent = `
      <h2>New Contact Request</h2>
      <p><strong>Name:</strong> ${name}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Subject:</strong> ${subject}</p>
      <p><strong>Message:</strong></p>
      <div style="padding: 15px; background-color: #f5f5f5; border-radius: 5px; margin-top: 10px;">
        ${message.replace(/\n/g, '<br>')}
      </div>
      <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
    `;

    // Send email using Resend
    const { data, error } = await resend.emails.send({
      from: 'PraxJobs Contact <<EMAIL>>',
      to: '<EMAIL>', // Your receiving email address
      subject: `New PraxJobs Contact: ${subject}`,
      html: emailContent,
      reply_to: email,
    });

    if (error) {
      console.error('Error sending contact email:', error);
      return {
        statusCode: 500, // Internal Server Error
        headers,
        body: JSON.stringify({
          error: "Failed to send contact email",
        }),
      };
    }

    // Return success response
    return {
      statusCode: 200, // OK
      headers,
      body: JSON.stringify({
        message: "Contact request submitted successfully. We will get back to you soon!",
        id: data?.id,
      }),
    };
  } catch (error) {
    // Log the detailed error to your monitoring service
    console.error('Error submitting contact request:', error);

    // Return a generic internal server error message to the client
    return {
      statusCode: 500, // Internal Server Error
      headers,
      body: JSON.stringify({
        error: "An internal server error occurred while sending the message.",
      }),
    };
  }
};
