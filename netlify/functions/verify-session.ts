import { <PERSON><PERSON> } from "@netlify/functions";
import { initializeFirebaseAdmin } from "../../src/lib/firebaseAdmin";
import admin from "firebase-admin";

export const handler: Handler = async (event, context) => {
  const allowedOrigin = process.env.PROD ? "https://praxjobs.com" : "http://localhost:4321";
  const corsHeaders = {
    "Access-Control-Allow-Origin": allowedOrigin,
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
  };

  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 204,
      headers: corsHeaders,
    };
  }

  try {
    await initializeFirebaseAdmin();
    const { sessionCookie } = JSON.parse(event.body || "{}");

    if (!sessionCookie) {
      return {
        statusCode: 401,
        body: JSON.stringify({ error: "No session cookie provided." }),
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      };
    }

    const decodedToken = await admin.auth().verifySessionCookie(sessionCookie, true);
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, uid: decodedToken.uid }),
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    };
  } catch (error) {
    return {
      statusCode: 401,
      body: JSON.stringify({ error: "Invalid or expired session." }),
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    };
  }
};