import type { <PERSON><PERSON> } from '@netlify/functions';
import mammoth from 'mammoth';
import pdf from 'pdf-parse';
import { <PERSON><PERSON><PERSON> } from 'buffer';

// Helper function to parse multipart form data
// This is a simplified parser. For production, a robust library is recommended.
const parseMultipartFormData = (body: Buffer, boundary: string) => {
  const parts = body.toString('binary').split(`--${boundary}`).filter(part => part.trim() !== '' && part !== '--');
  for (const part of parts) {
    if (part.includes('filename="')) {
      const headerEnd = part.indexOf('\r\n\r\n');
      const headers = part.substring(0, headerEnd);
      const content = part.substring(headerEnd + 4, part.length - 2); // Adjust for \r\n at the end

      const filenameMatch = headers.match(/filename="([^"]+)"/);
      const contentTypeMatch = headers.match(/Content-Type: ([^\r\n]+)/);
      
      if (filenameMatch && content) {
        return {
          filename: filenameMatch[1],
          type: contentTypeMatch ? contentTypeMatch[1] : 'application/octet-stream',
          data: Buffer.from(content, 'binary'),
        };
      }
    }
  }
  return null;
};

const parseResumeText = (text: string) => {
    const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    
    const structuredData: any = {
        contactInfo: {},
        summary: '',
        workExperience: [],
        education: [],
        skills: [],
    };

    // Very basic parsing logic, can be improved with more sophisticated NLP
    structuredData.contactInfo.fullName = lines[0] || '';
    
    const emailRegex = /\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}\b/i;
    const phoneRegex = /(\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}/;
    const linkedinRegex = /linkedin\.com\/in\/[a-zA-Z0-9_-]+/;

    structuredData.contactInfo.email = text.match(emailRegex)?.[0] || '';
    structuredData.contactInfo.phone = text.match(phoneRegex)?.[0] || '';
    structuredData.contactInfo.linkedin = text.match(linkedinRegex)?.[0] || '';
    
    let currentSection = '';
    lines.forEach(line => {
        const lowerLine = line.toLowerCase();
        if (['summary', 'profile', 'objective'].some(keyword => lowerLine.startsWith(keyword))) {
            currentSection = 'summary';
        } else if (['experience', 'work history', 'employment'].some(keyword => lowerLine.startsWith(keyword))) {
            currentSection = 'workExperience';
        } else if (['education', 'academic background'].some(keyword => lowerLine.startsWith(keyword))) {
            currentSection = 'education';
        } else if (['skills', 'technical skills'].some(keyword => lowerLine.startsWith(keyword))) {
            currentSection = 'skills';
        } else {
            switch (currentSection) {
                case 'summary':
                    structuredData.summary += line + ' ';
                    break;
                case 'skills':
                    structuredData.skills.push(...line.split(/, |; | \/ /));
                    break;
                // More complex sections like experience and education would need more detailed parsing
            }
        }
    });

    return structuredData;
};


export const handler: Handler = async (event) => {
  if (event.httpMethod !== 'POST') {
    return { statusCode: 405, body: 'Method Not Allowed' };
  }

  if (!event.body) {
    return { statusCode: 400, body: 'No body found in request' };
  }

  try {
    const contentType = event.headers['content-type'];
    if (!contentType || !contentType.includes('multipart/form-data')) {
        return { statusCode: 400, body: 'Content-Type must be multipart/form-data' };
    }
    
    const boundary = contentType.split('boundary=')[1];
    if (!boundary) {
        return { statusCode: 400, body: 'Multipart boundary not found' };
    }

    const bodyBuffer = Buffer.from(event.body, event.isBase64Encoded ? 'base64' : 'binary');
    const file = parseMultipartFormData(bodyBuffer, boundary);

    if (!file) {
      return { statusCode: 400, body: 'File not found in request' };
    }

    let text = '';
    if (file.type === 'application/pdf' || file.filename.endsWith('.pdf')) {
      const data = await pdf(file.data);
      text = data.text;
    } else if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' || file.filename.endsWith('.docx')) {
      const { value } = await mammoth.extractRawText({ buffer: file.data });
      text = value;
    } else {
      return { statusCode: 415, body: 'Unsupported file type' };
    }

    const structuredData = parseResumeText(text);

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(structuredData),
    };
  } catch (error) {
    console.error('Error parsing resume:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Failed to parse resume', details: errorMessage }),
    };
  }
};