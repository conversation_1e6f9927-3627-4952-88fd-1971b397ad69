import type { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@netlify/functions";
import { GoogleGenAI } from "@google/genai";
import OpenA<PERSON> from "openai";
import { verifyFirebaseToken } from "../../src/lib/firebaseAdmin"; // Import verification function

// Define types for our analysis
interface ProfileAnalysisResult {
  overallScore: number;
  sectionScores: SectionScore[];
  keywordOptimization: KeywordOptimization;
  optimizedSummary: string;
  headlineSuggestions: HeadlineSuggestion[];
  experienceEnhancements: string[];
  prioritySkills: string[];
  profileCompleteness: ProfileCompleteness;
  atsOptimization: ATSOptimization;
  skillEndorsements: SkillCategory[];
  contentStrategy: ContentStrategyItem[];
  networkingRecommendations: string[];
  competitiveAnalysis: CompetitiveAnalysis;
  analysisMetadata?: AnalysisMetadata;
}

interface SectionScore {
  section: string;
  score: number;
  recommendations: string[];
  priority?: 'high' | 'medium' | 'low';
}

interface KeywordOptimization {
  missingKeywords: string[];
  keywordSuggestions: KeywordSuggestion[];
}

interface KeywordSuggestion {
  keyword: string;
  placement: string;
}

interface HeadlineSuggestion {
  headline: string;
  explanation: string;
}

interface ProfileCompleteness {
  score: number;
  missingSections: string[];
  incompleteSection: string[];
}

interface ATSOptimization {
  score: number;
  recommendations: string[];
}

interface SkillCategory {
  category: string;
  skills: string[];
}

interface ContentStrategyItem {
  contentType: string;
  description: string;
  example: string;
}

interface CompetitiveAnalysis {
  industryStandards: string;
  differentiators: string[];
}

interface AnalysisMetadata {
  processingTimeMs: number;
  modelUsed: string;
  analysisDate: string;
  documentStats: {
    linkedinContentLength: number;
    resumeContentLength: number;
    keywordsExtracted: number;
  };
}

// Utility functions for document analysis
const extractKeywords = (text: string): string[] => {
  // Simple keyword extraction based on frequency and relevance
  // This is a basic implementation - in production, use NLP libraries
  const words = text.toLowerCase().match(/\b[a-z]{3,}\b/g) || [];
  const stopWords = new Set(['and', 'the', 'for', 'with', 'that', 'this', 'from', 'have', 'has', 'had', 'was', 'were', 'are', 'our', 'your']);

  // Count word frequency
  const wordCounts: Record<string, number> = {};
  words.forEach(word => {
    if (!stopWords.has(word)) {
      wordCounts[word] = (wordCounts[word] || 0) + 1;
    }
  });

  // Sort by frequency and return top keywords
  return Object.entries(wordCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 20)
    .map(entry => entry[0]);
};

const calculateBasicProfileScore = (linkedinContent: string): number => {
  // Basic heuristics for profile scoring
  const lengthScore = Math.min(100, linkedinContent.length / 100);
  const keywordDensity = extractKeywords(linkedinContent).length;
  const keywordScore = Math.min(100, keywordDensity * 5);

  // Calculate weighted average
  return Math.round((lengthScore * 0.4) + (keywordScore * 0.6));
};

// Helper to extract token from Authorization header
const extractToken = (event: HandlerEvent): string | null => {
  const authHeader = event.headers.authorization;
  if (authHeader && authHeader.startsWith("Bearer ")) {
    return authHeader.substring(7); // Remove "Bearer " prefix
  }
  return null;
};

// Moved Groq client initialization inside processOptimization

// Function to perform quick analysis without AI
async function performQuickAnalysis(params: {
  linkedinContent: string;
  resumeContent: string;
  jobTitle?: string;
  industry?: string;
}): Promise<Partial<ProfileAnalysisResult>> {
  const startTime = Date.now();

  // Extract basic information
  const linkedinKeywords = extractKeywords(params.linkedinContent);
  const resumeKeywords = extractKeywords(params.resumeContent);

  // Find keywords in resume but not in LinkedIn
  const missingKeywords = resumeKeywords.filter(
    keyword => !params.linkedinContent.toLowerCase().includes(keyword)
  ).slice(0, 10);

  // Calculate basic scores
  const overallScore = calculateBasicProfileScore(params.linkedinContent);

  // Generate basic section scores
  const sectionScores: SectionScore[] = [
    {
      section: "Profile Completeness",
      score: Math.min(100, params.linkedinContent.length / 50),
      recommendations: ["Complete all sections of your LinkedIn profile"],
      priority: 'high'
    },
    {
      section: "Keyword Optimization",
      score: Math.min(100, (linkedinKeywords.length / 20) * 100),
      recommendations: ["Add more industry-specific keywords to your profile"],
      priority: 'medium'
    }
  ];

  // Return quick analysis results
  return {
    overallScore,
    sectionScores,
    keywordOptimization: {
      missingKeywords,
      keywordSuggestions: missingKeywords.slice(0, 5).map(keyword => ({
        keyword,
        placement: "Add to your profile summary and experience sections"
      }))
    },
    analysisMetadata: {
      processingTimeMs: Date.now() - startTime,
      modelUsed: "basic-analysis",
      analysisDate: new Date().toISOString(),
      documentStats: {
        linkedinContentLength: params.linkedinContent.length,
        resumeContentLength: params.resumeContent.length,
        keywordsExtracted: linkedinKeywords.length + resumeKeywords.length
      }
    }
  };
}

// Increase the function timeout for Netlify Functions
export const handler: Handler = async (event) => {
  // Define allowed origin and standard headers
  const allowedOrigin = "https://praxjobs.com";
  const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": allowedOrigin,
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
    "Cache-Control": "no-cache"
  };

  // Handle OPTIONS request for CORS
  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 200,
      headers,
      body: "",
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== "POST") {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ message: "Method not allowed" }),
    };
  }

  try {
    // Extract and verify the Firebase ID token
    const token = extractToken(event);
    if (!token) {
      return {
        statusCode: 401, // Unauthorized
        headers,
        body: JSON.stringify({ error: "Authentication token missing" }),
      };
    }

    const decodedToken = await verifyFirebaseToken(token);
    if (!decodedToken) {
      return {
        statusCode: 403, // Forbidden
        headers,
        body: JSON.stringify({ error: "Invalid or expired authentication token" }),
      };
    }

    const userId = decodedToken.uid; // Extract user ID

    // Validate request body
    if (!event.body) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: "Request body is required" }),
      };
    }

    // Validate Content-Type
    if (event.headers["content-type"] !== "application/json") {
      return {
        statusCode: 415, // Unsupported Media Type
        headers,
        body: JSON.stringify({
          error: "Content-Type must be application/json",
        }),
      };
    }

    const body = JSON.parse(event.body);

    // Validate required fields
    if (!body.linkedinContent || !body.resumeContent) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          error: "LinkedIn content and resume content are required",
        }),
      };
    }

    // Check if this is a progressive analysis request
    const isProgressiveRequest = body.progressive === true;

    if (isProgressiveRequest) {
      // Perform quick analysis first
      const quickAnalysis = await performQuickAnalysis({
        linkedinContent: body.linkedinContent,
        resumeContent: body.resumeContent,
        jobTitle: body.jobTitle || "",
        industry: body.industry || "",
      });

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          ...quickAnalysis,
          analysisStatus: "partial",
          message: "Quick analysis complete. Full analysis in progress."
        }),
      };
    } else {
      // Process full optimization with AI
      const optimizedProfile = await processOptimization({
        linkedinContent: body.linkedinContent,
        resumeContent: body.resumeContent,
        jobTitle: body.jobTitle || "",
        industry: body.industry || "",
        careerGoals: body.careerGoals || "",
      });

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          ...optimizedProfile,
          analysisStatus: "complete"
        }),
      };
    }
  } catch (error: any) {
    console.error(`Error processing linkedin-optimize request:`, error);

    // Try to return a fallback response even on error
    try {
      const body = JSON.parse(event.body || "{}");
      const fallbackResponse = createFallbackResponse({
        linkedinContent: body.linkedinContent || "",
        resumeContent: body.resumeContent || "",
        jobTitle: body.jobTitle || "",
        industry: body.industry || "",
      });

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          ...fallbackResponse,
          analysisStatus: "fallback",
          message: "An error occurred, but we've provided some basic recommendations."
        }),
      };
    } catch (fallbackError) {
      // If even the fallback fails, return an error
      return {
        statusCode: error.statusCode || 500,
        headers,
        body: JSON.stringify({
          error: error.message || "Failed to optimize profile",
          analysisStatus: "error"
        }),
      };
    }
  }
};

async function processOptimization(params: {
  linkedinContent: string;
  resumeContent: string;
  jobTitle?: string;
  industry?: string;
  careerGoals?: string;
  modelPreference?: string;
  userId?: string;
}): Promise<ProfileAnalysisResult> {
  const startTime = Date.now();

  try {
    // Start with basic analysis for immediate results - we'll use this in a future version
    // for progressive loading, but for now we'll just use it for timing
    await performQuickAnalysis(params);

    // Always use Gemini 2.5 Flash model
    const modelToUse = "gemini-2.5-flash-preview-05-20";
    const useOpenAI = false;

    // Create a comprehensive prompt that matches our expected output format
    const prompt = createOptimizationPrompt(params);

    // Call the appropriate AI model with timeout
    let aiResponse: any;

    if (useOpenAI) {
      aiResponse = await callOpenAIWithTimeout(prompt);
    } else {
      aiResponse = await callGeminiWithTimeout(prompt, modelToUse);
    }

    // Process the AI response
    const optimizationResult = processAIResponse(aiResponse, useOpenAI);

    // Create a fallback response to use as a base
    const fallback = createFallbackResponse(params);

    // Merge the AI response with fallback data to ensure all fields have values
    const mergedResult = mergeWithFallback(optimizationResult, fallback);

    // Add priority levels to section scores based on score values
    const enhancedSectionScores = mergedResult.sectionScores.map(section => ({
      ...section,
      priority: section.score < 50 ? 'high' as const : section.score < 75 ? 'medium' as const : 'low' as const
    }));

    // Add metadata about the analysis
    const analysisMetadata: AnalysisMetadata = {
      processingTimeMs: Date.now() - startTime,
      modelUsed: useOpenAI ? 'openai-gpt-4.1-mini' : modelToUse,
      analysisDate: new Date().toISOString(),
      documentStats: {
        linkedinContentLength: params.linkedinContent.length,
        resumeContentLength: params.resumeContent.length,
        keywordsExtracted: mergedResult.keywordOptimization.missingKeywords.length
      }
    };

    // Return the enhanced result
    return {
      ...mergedResult,
      sectionScores: enhancedSectionScores,
      analysisMetadata
    };
  } catch (error) {
    console.error('Error in AI processing:', error);

    // Return a comprehensive fallback response
    const fallback = createFallbackResponse(params);

    return {
      ...fallback,
      analysisMetadata: {
        processingTimeMs: Date.now() - startTime,
        modelUsed: 'fallback',
        analysisDate: new Date().toISOString(),
        documentStats: {
          linkedinContentLength: params.linkedinContent.length,
          resumeContentLength: params.resumeContent.length,
          keywordsExtracted: fallback.keywordOptimization.missingKeywords.length
        }
      }
    };
  }
}

// Helper function to create the optimization prompt
function createOptimizationPrompt(params: any): string {
  // Debug logging to verify content received by the backend
  console.log(`Backend processing - LinkedIn content length: ${params.linkedinContent.length} characters`);
  console.log(`Backend processing - Resume content length: ${params.resumeContent.length} characters`);
  console.log(`LinkedIn content preview: ${params.linkedinContent.substring(0, 100)}...`);
  console.log(`Resume content preview: ${params.resumeContent.substring(0, 100)}...`);

  return `
You are a LinkedIn profile optimization expert with deep knowledge of personal branding, recruiter preferences, and ATS systems.
Analyze this LinkedIn profile and resume to provide actionable recommendations that will help the user stand out.
You have access to a google_search tool that you should use to find current, factual information about LinkedIn best practices, industry standards, and in-demand skills.

LinkedIn Profile: ${params.linkedinContent.substring(0, 1200)}...

Resume: ${params.resumeContent.substring(0, 1200)}...
${params.jobTitle ? `Job Title: ${params.jobTitle}` : ""}
${params.industry ? `Industry: ${params.industry}` : ""}
${params.careerGoals ? `Career Goals: ${params.careerGoals}` : ""}

For each section of your analysis, use the google_search tool to find the most current information. For example:
- Search for "latest LinkedIn profile optimization best practices 2024" to get current standards
- Search for "in-demand skills for ${params.industry || 'professionals'} 2024" to find current skill trends
- Search for "LinkedIn algorithm changes 2024" to understand current visibility factors

Provide a comprehensive analysis with ALL of these elements:
1. Overall score (0-100) based on current recruiter preferences and industry standards
2. Section-by-section scoring with specific, actionable recommendations based on current LinkedIn best practices
3. Missing keywords from resume that should be in LinkedIn (focus on industry-relevant terms that are currently in demand)
4. Headline suggestions that will grab attention and improve searchability according to current LinkedIn algorithm preferences
5. Optimized summary suggestion that highlights achievements and value proposition using current LinkedIn best practices
6. Experience enhancement tips to make accomplishments stand out based on what recruiters currently look for
7. Priority skills to highlight based on current industry demand
8. Content strategy suggestions to increase visibility based on current LinkedIn engagement patterns
9. Networking recommendations to expand professional connections using current LinkedIn networking features
10. Competitive analysis comparing to current industry standards

Format as JSON with ALL these fields:
{
  "overallScore": 75,
  "sectionScores": [
    {"section": "Profile Photo & Background", "score": 70, "recommendations": ["Add professional background image", "Ensure clear profile photo"]},
    {"section": "Headline", "score": 65, "recommendations": ["Include key industry terms"]},
    {"section": "About/Summary", "score": 60, "recommendations": ["Add quantifiable achievements"]},
    {"section": "Experience", "score": 75, "recommendations": ["Add metrics to demonstrate impact"]},
    {"section": "Education", "score": 90, "recommendations": ["Add relevant coursework"]},
    {"section": "Skills", "score": 80, "recommendations": ["Prioritize relevant skills"]},
    {"section": "Recommendations", "score": 50, "recommendations": ["Request recommendations"]}
  ],
  "keywordOptimization": {
    "missingKeywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"],
    "keywordSuggestions": [
      {"keyword": "Leadership", "placement": "Add to headline and experience section"},
      {"keyword": "Project Management", "placement": "Add to skills and summary"}
    ]
  },
  "optimizedSummary": "Brief optimized summary text",
  "headlineSuggestions": [
    {"headline": "Option 1", "explanation": "This works because..."},
    {"headline": "Option 2", "explanation": "This is effective because..."},
    {"headline": "Option 3", "explanation": "This stands out because..."}
  ],
  "experienceEnhancements": ["Add metrics to first bullet point", "Use action verbs", "Highlight achievements"],
  "prioritySkills": ["Skill 1", "Skill 2", "Skill 3", "Skill 4", "Skill 5"],
  "contentStrategy": [
    {"contentType": "Industry Insights", "description": "Share analysis of trends", "example": "Example post about industry trends"}
  ],
  "networkingRecommendations": [
    "Connect with alumni from your university",
    "Join industry-specific LinkedIn groups"
  ],
  "competitiveAnalysis": {
    "industryStandards": "Brief description of industry standards",
    "differentiators": ["Unique skill 1", "Unique experience 2"]
  }
}
`;
}

// Helper function to call Gemini API with timeout and Google Search as a tool
async function callGeminiWithTimeout(prompt: string, model: string, timeoutMs = 25000): Promise<any> {
  // Initialize Google GenAI client
  const ai = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY || "" });

  return Promise.race([
    ai.models.generateContent({
      model: model,
      contents: prompt,
      config: {
        temperature: 0.2, // Lower temperature for more factual responses
        maxOutputTokens: 1000,
        responseMimeType: "application/json",
        // Using any to bypass type checking for the thinking budget parameter
        ...(model.includes('2.5-flash') ? { thinking_budget: 0 } : {}),
        // Enable Google Search as a tool for more grounded responses
        tools: [{googleSearch:{}}]
      },
    }),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error("API request timed out")), timeoutMs)
    )
  ]);
}

// Helper function to call OpenAI API with timeout
async function callOpenAIWithTimeout(prompt: string, timeoutMs = 10000): Promise<any> {
  // Initialize OpenAI client
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

  return Promise.race([
    openai.chat.completions.create({
      model: "gpt-4.1-mini",
      messages: [
        { role: "system", content: "You are a LinkedIn profile optimization expert." },
        { role: "user", content: prompt }
      ],
      temperature: 0.4,
      max_tokens: 1000,
      response_format: { type: "json_object" },
    }),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error("API request timed out")), timeoutMs)
    )
  ]);
}

// Helper function to process AI response based on the model used
function processAIResponse(response: any, isOpenAI: boolean): any {
  try {
    let responseText = "{}";

    if (isOpenAI) {
      responseText = response.choices[0]?.message?.content || "{}";
    } else {
      // Check if the response contains function calls (Google Search tool)
      if (response.functionCalls && response.functionCalls.length > 0) {
        console.log("Function calls detected in response:", response.functionCalls);
        // The model is using the Google Search tool, but we'll still process the text response
      }

      responseText = response.text || "{}";
    }

    return JSON.parse(responseText);
  } catch (error) {
    console.error("Error processing AI response:", error);
    return {};
  }
}

// Helper function to merge AI result with fallback data
function mergeWithFallback(result: any, fallback: any): ProfileAnalysisResult {
  return {
    overallScore: result.overallScore || fallback.overallScore,
    sectionScores: result.sectionScores?.length > 0 ? result.sectionScores : fallback.sectionScores,

    keywordOptimization: {
      missingKeywords: result.keywordOptimization?.missingKeywords?.length > 0
        ? result.keywordOptimization.missingKeywords
        : fallback.keywordOptimization.missingKeywords,
      keywordSuggestions: result.keywordOptimization?.keywordSuggestions?.length > 0
        ? result.keywordOptimization.keywordSuggestions
        : fallback.keywordOptimization.keywordSuggestions
    },

    optimizedSummary: result.optimizedSummary || fallback.optimizedSummary,

    headlineSuggestions: result.headlineSuggestions?.length > 0
      ? result.headlineSuggestions
      : fallback.headlineSuggestions,

    experienceEnhancements: result.experienceEnhancements?.length > 0
      ? result.experienceEnhancements
      : fallback.experienceEnhancements,

    prioritySkills: result.prioritySkills?.length > 0
      ? result.prioritySkills
      : fallback.prioritySkills,

    profileCompleteness: result.profileCompleteness || fallback.profileCompleteness,

    atsOptimization: result.atsOptimization?.recommendations?.length > 0
      ? result.atsOptimization
      : fallback.atsOptimization,

    skillEndorsements: result.skillEndorsements?.length > 0
      ? result.skillEndorsements
      : fallback.skillEndorsements,

    contentStrategy: result.contentStrategy?.length > 0
      ? result.contentStrategy
      : fallback.contentStrategy,

    networkingRecommendations: result.networkingRecommendations?.length > 0
      ? result.networkingRecommendations
      : fallback.networkingRecommendations,

    competitiveAnalysis: (result.competitiveAnalysis?.industryStandards || result.competitiveAnalysis?.differentiators?.length > 0)
      ? result.competitiveAnalysis
      : fallback.competitiveAnalysis
  };
}

// Function to get industry-specific recommendations
function getIndustrySpecificRecommendations(industry: string): {
  skills: string[];
  keywords: string[];
  contentIdeas: string[];
  headlineFormats: string[];
} {
  // Normalize industry name
  const normalizedIndustry = industry.toLowerCase().trim();

  // Default recommendations
  const defaultRecs = {
    skills: ["Leadership", "Communication", "Project Management", "Problem Solving", "Teamwork"],
    keywords: ["professional", "experienced", "skilled", "expert", "knowledgeable"],
    contentIdeas: ["Industry trends", "Professional development", "Career advice", "Project showcases"],
    headlineFormats: ["[Title] with [X] years of experience in [Industry]", "[Title] specializing in [Skill] | [Industry] Professional"]
  };

  // Industry-specific recommendations
  const industryMap: Record<string, any> = {
    "technology": {
      skills: ["Software Development", "Cloud Computing", "Data Analysis", "Cybersecurity", "Agile Methodology"],
      keywords: ["tech", "innovation", "digital transformation", "solution", "architecture"],
      contentIdeas: ["Tech trends", "Product launches", "Coding tips", "Tech stack discussions"],
      headlineFormats: ["[Tech Role] building [Product Type] | [Programming Language] Expert", "Tech Leader specializing in [Technology] Solutions"]
    },
    "finance": {
      skills: ["Financial Analysis", "Risk Management", "Investment Strategy", "Regulatory Compliance", "Financial Modeling"],
      keywords: ["analysis", "portfolio", "investment", "compliance", "strategy"],
      contentIdeas: ["Market analysis", "Investment trends", "Regulatory updates", "Financial planning tips"],
      headlineFormats: ["[Finance Title] with expertise in [Specialty] | [Certification]", "Financial [Specialty] Professional | [Industry] Expert"]
    },
    "healthcare": {
      skills: ["Patient Care", "Clinical Research", "Healthcare Management", "Medical Records", "Regulatory Compliance"],
      keywords: ["patient", "care", "clinical", "health", "medical"],
      contentIdeas: ["Healthcare innovations", "Patient care best practices", "Medical research", "Healthcare policy"],
      headlineFormats: ["[Healthcare Role] specializing in [Specialty] | [Certification]", "Healthcare Professional with [X] years in [Specialty]"]
    },
    "marketing": {
      skills: ["Digital Marketing", "Content Strategy", "Social Media Management", "SEO/SEM", "Brand Development"],
      keywords: ["brand", "campaign", "strategy", "audience", "engagement"],
      contentIdeas: ["Marketing campaigns", "Brand strategies", "Social media trends", "Content marketing tips"],
      headlineFormats: ["[Marketing Title] driving [Result Type] | [Specialty] Strategist", "Marketing Professional specializing in [Channel] & [Channel]"]
    }
  };

  // Find matching industry or use default
  for (const [key, value] of Object.entries(industryMap)) {
    if (normalizedIndustry.includes(key)) {
      return value;
    }
  }

  return defaultRecs;
}

// Helper function to create a comprehensive fallback response with industry-specific recommendations
function createFallbackResponse(params: any) {
  // Extract job title or use a generic one
  const jobTitle = params.jobTitle || "Professional";
  const industry = params.industry || "your industry";

  // Get industry-specific recommendations
  const industryRecs = getIndustrySpecificRecommendations(industry);

  // Create headline suggestions using industry formats
  const createHeadlineSuggestions = () => {
    return industryRecs.headlineFormats.map((format, index) => {
      const headline = format.replace('[Title]', jobTitle)
                            .replace('[Industry]', industry)
                            .replace('[Skill]', industryRecs.skills[0] || 'Leadership');

      return {
        headline,
        explanation: index === 0
          ? "Highlights your role and industry expertise"
          : "Emphasizes your specialization and professional focus"
      };
    });
  };

  // Add a third custom headline suggestion
  const headlineSuggestions = [
    ...createHeadlineSuggestions(),
    {
      headline: `${jobTitle} Specializing in Driving Business Results in ${industry}`,
      explanation: "Focuses on your impact and value proposition"
    }
  ].slice(0, 3); // Ensure we only have 3 suggestions

  // Create optimized summary using industry keywords
  const optimizedSummary = `Experienced ${jobTitle} with a proven track record of delivering results in ${industry}. Skilled in ${industryRecs.skills.slice(0, 3).join(', ')}, with a focus on driving business outcomes. Passionate about leveraging expertise to create value and achieve organizational goals.`;

  // Create content strategy based on industry
  const contentStrategy = industryRecs.contentIdeas.map(idea => ({
    contentType: idea,
    description: `Share insights about ${idea.toLowerCase()} in your industry`,
    example: `Example post: 'Just explored the latest ${idea.toLowerCase()} in ${industry}. Here are my key takeaways for professionals in our field...'`
  }));

  return {
    overallScore: 65,
    sectionScores: [
      {section: "Profile Photo & Background", score: 70, recommendations: ["Add a professional background image related to your industry", "Ensure your profile photo shows your face clearly with professional attire"]},
      {section: "Headline", score: 65, recommendations: ["Include key industry terms like " + industryRecs.keywords.slice(0, 2).join(' and '), "Highlight your specialization in " + industryRecs.skills[0]]},
      {section: "About/Summary", score: 60, recommendations: ["Add more quantifiable achievements", "Incorporate industry keywords like " + industryRecs.keywords.slice(0, 3).join(', ')]},
      {section: "Experience", score: 75, recommendations: ["Add metrics to demonstrate impact", "Use more action verbs"]},
      {section: "Education", score: 80, recommendations: ["Add relevant coursework", "Mention academic achievements"]},
      {section: "Skills", score: 70, recommendations: ["Prioritize relevant skills like " + industryRecs.skills.slice(0, 3).join(', '), "Remove outdated skills"]},
      {section: "Recommendations", score: 50, recommendations: ["Request recommendations from supervisors", "Provide specific recommendation requests"]},
      {section: "Accomplishments", score: 60, recommendations: ["Add relevant projects", "Include certifications"]},
      {section: "Activity", score: 40, recommendations: ["Increase posting frequency", "Engage with industry content related to " + industryRecs.contentIdeas[0]]}
    ],
    keywordOptimization: {
      missingKeywords: [...industryRecs.keywords, ...industryRecs.skills.slice(0, 5)],
      keywordSuggestions: industryRecs.keywords.slice(0, 4).map(keyword => ({
        keyword,
        placement: "Add to headline and experience section"
      }))
    },
    optimizedSummary,
    headlineSuggestions,
    experienceEnhancements: [
      "Add specific metrics and quantifiable achievements to demonstrate impact",
      "Use strong action verbs at the beginning of each bullet point",
      `Incorporate industry keywords like ${industryRecs.keywords.slice(0, 3).join(', ')} throughout your experience section`,
      "Highlight leadership roles and team management experience",
      `Include specific ${industryRecs.skills[0]} methodologies you've worked with`
    ],
    prioritySkills: industryRecs.skills,

    // Ensure all fields have data for UI display
    profileCompleteness: {
      score: 70,
      missingSections: ["Recommendations", "Accomplishments"],
      incompleteSection: ["Summary could be more detailed", `Skills section needs more ${industry}-specific keywords`]
    },
    atsOptimization: {
      score: 65,
      recommendations: [
        "Include more industry-standard job titles",
        `Add more ${industry}-specific skills in the skills section`,
        "Use keywords from job descriptions in your target roles",
        "Ensure your job titles align with standard industry terminology"
      ]
    },
    skillEndorsements: [
      {category: "Technical Skills", skills: industryRecs.skills.slice(0, 3)},
      {category: "Soft Skills", skills: ["Leadership", "Communication", "Problem Solving"]},
      {category: "Industry Knowledge", skills: [`${industry} Trends`, "Best Practices", "Competitive Analysis"]}
    ],
    contentStrategy: contentStrategy.length > 0 ? contentStrategy : [
      {contentType: "Industry Insights", description: "Share analysis of trends in your industry", example: "Example post: 'Just read the latest report on [industry trend]. Here are my three key takeaways for professionals in our field...'"},
      {contentType: "Project Showcases", description: "Highlight successful projects with measurable outcomes", example: "Example post: 'Excited to share that our team just completed [project] resulting in [specific metric improvement]...'"},
      {contentType: "Thought Leadership", description: "Share your unique perspective on industry challenges", example: "Example post: 'Many professionals struggle with [common challenge]. Here's my approach to solving this...'"}
    ],
    networkingRecommendations: [
      `Connect with professionals in ${industry} who work in target companies`,
      `Join and actively participate in 2-3 ${industry}-specific LinkedIn groups`,
      `Follow and engage with thought leaders in ${industry}`,
      "Reach out to professionals in roles you aspire to for informational interviews",
      `Attend virtual ${industry} events and connect with speakers and participants`,
      "Engage with content from companies you're interested in working for"
    ],
    competitiveAnalysis: {
      industryStandards: `Most professionals in ${industry} highlight specific technical skills like ${industryRecs.skills.slice(0, 3).join(', ')}, certifications, and quantifiable achievements. Successful profiles typically include detailed project descriptions with measurable outcomes.`,
      differentiators: [
        `Emphasize your unique experience with ${industryRecs.skills[0]}`,
        `Highlight your expertise in emerging trends within ${industry}`,
        "Showcase your ability to drive business results through innovative approaches",
        `Feature your experience with specific ${industry} methodologies that are in high demand`
      ]
    }
  };
}
