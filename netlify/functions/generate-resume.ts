import { Hand<PERSON>, HandlerEvent } from "@netlify/functions"; // Import HandlerEvent
import OpenAI from "openai";
import { verifyFirebaseToken } from "../../src/lib/firebaseAdmin"; // Import verifyFirebaseToken

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export const handler: Handler = async (event: HandlerEvent) => { // Add HandlerEvent type
  // Define allowed origin and standard headers
  const allowedOrigin = event.headers.origin || "https://praxjobs.com";
  const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": allowedOrigin,
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };

  // Handle preflight requests
  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 204,
      headers,
    };
  }

  if (event.httpMethod !== "POST") {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: "Method not allowed" }),
    };
  }

  // --- Authentication Check ---
  const authHeader = event.headers.authorization;
  const token = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : null;
  let authenticatedUserId: string | null = null;

  if (!token) {
    console.warn("Authentication failed for generate-resume: No token provided.");
    return {
      statusCode: 401,
      headers,
      body: JSON.stringify({ error: "Authentication required: No token provided" }),
    };
  }

  try {
    const decodedToken = await verifyFirebaseToken(token);
    authenticatedUserId = decodedToken.uid;
    console.log(`Authentication successful for generate-resume user: ${authenticatedUserId}`);
  } catch (error) {
    console.error("Authentication failed for generate-resume: Invalid token.", error);
    return {
      statusCode: 401,
      headers,
      body: JSON.stringify({ error: "Authentication failed: Invalid token" }),
    };
  }
  // --- End Authentication Check ---

  try {
    // Parse request body
    if (!event.body) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: "Request body is required" }),
      };
    }

    const body = JSON.parse(event.body);
    const { jobDescription, resumeContent, customInstructions } = body;

    // Validate required fields
    if (!jobDescription || !resumeContent) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          error: "Job description and resume content are required",
        }),
      };
    }

    // Check API key
    if (!process.env.OPENAI_API_KEY) {
      console.error("OpenAI API Key is missing");
      return {
        statusCode: 503,
        headers,
        body: JSON.stringify({
          error: "Resume generation service is temporarily unavailable",
        }),
      };
    }

    // Create system prompt
    const systemPrompt = `Generate a professional, ATS-optimized resume by tailoring the provided resume content to match the given job description. Follow these strict guidelines:
1. **Output Requirements (IMPORTANT):**
   - **Return ONLY a JSON object.**
   - **Do NOT include explanations, comments, or markdown outside the JSON.**
   - **Do NOT remove any experiences, achievements, or sections from the original resume.**
   - The JSON object should have the following structure, with fields populated by tailoring the provided resume content to the job description:
     \`\`\`json
     {
       "name": "Your Name",
       "contact": {
         "title": "Your Title",
         "phone": "Your Phone",
         "email": "Your Email",
         "portfolio": "Your Portfolio URL",
         "linkedin": "Your LinkedIn URL",
         "location": "Your Location"
       },
       "summary": "Optimized summary based on job description.",
       "workExperience": [
         {
           "title": "Job Title, Company Name",
           "date": "Start Date – End Date",
           "details": [
             "Optimized bullet point 1",
             "Optimized bullet point 2"
           ]
         }
       ],
       "skills": {
         "product": "Product skills list",
         "tech": "Tech stack list"
       },
       "certifications": [
         "Certification 1",
         "Certification 2"
       ],
       "education": [
         "Degree, University – Dates",
         "Degree, University – Dates"
       ],
       "projects": [
         "Project 1 description",
         "Project 2 description"
       ]
     }
     \`\`\`
   - **Ensure all text content within the JSON is plain text.**
   - **For work experience, education, certifications, and projects, if the original resume has multiple entries, include all of them in the JSON array.**

2. **Content Optimization:**
   - **Align resume wording closely with the job description while preserving original content.**
   - **Keep all metrics, achievements, and experiences intact.**
   - **Do NOT add, fabricate, or exaggerate skills or experiences.**
   - Use the **STAR method (Situation, Task, Action, Result)**—focus on **Action & Result** for impact.

3. **Writing Style:**
   - Use **clear, action-driven language**.
   - Prioritize **specific, measurable impact**.
   - Avoid generic statements—highlight **tangible contributions**.

4. **ATS Compliance (CRITICAL):**
   - **Integrate keywords naturally** into bullet points (no keyword stuffing).
   - Use **simple, professional language** for clarity and readability.

5. **Additional Custom Instructions:**
${customInstructions ? customInstructions : "None"}`;

    // Create messages array with proper types
    const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
      {
        role: "system",
        content: systemPrompt,
      },
      {
        role: "user",
        content: `Job Description:\n${jobDescription}\n\nExisting Resume:\n${resumeContent}`,
      },
    ];

    // Create a streaming response
    const stream = await openai.chat.completions.create({
      model: "gpt-4.1-nano", // Using the same model as the client-side implementation
      messages: messages,
      stream: true,
      max_tokens: 3000,
      temperature: 0.3,
      top_p: 1,
    });

    // Set up streaming response headers
    const streamHeaders = {
      ...headers,
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      "Connection": "keep-alive",
    };

    // Create a response stream
    let fullContent = "";
    let responseBody = ""; // Initialize responseBody
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || "";
      fullContent += content;
      // Stream each chunk as it arrives, wrapped in a data: field
      // This allows the client to progressively build the JSON string
      // The client expects 'content' to be a string that can be accumulated
      // and then parsed as a single JSON object at the end.
      // So, we send the raw content as a string.
      responseBody += `data: ${content}\n\n`;
    }
    responseBody += `data: [DONE]\n\n`;

    // Return the response
    return {
      statusCode: 200,
      headers: streamHeaders,
      body: responseBody,
    };
  } catch (error) {
    console.error("Resume generation error:", error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: "Failed to generate resume",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
    };
  }
};
