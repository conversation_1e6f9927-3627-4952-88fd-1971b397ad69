import { HandlerEvent, HandlerResponse } from "@netlify/functions";
import { PerformJobAnalysis } from "../../src/components/tools/tools_api/JobAnalysisAPI";
import { verifyFirebaseToken } from "../../src/lib/firebaseAdmin";

// Helper to extract token from Authorization header
const extractToken = (event: HandlerEvent): string | null => {
  const authHeader = event.headers.authorization;
  if (authHeader && authHeader.startsWith("Bearer ")) {
    return authHeader.substring(7);
  }
  return null;
};

const handler = async (event, context): Promise<HandlerResponse> => { // Always return HandlerResponse
  const allowedOrigin = "https://praxjobs.com";
  const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": allowedOrigin,
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS", // Allow GET for SSE
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };

  console.log(`Incoming ${event.httpMethod} request to jobAnalysis`);

  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 200,
      headers,
      body: "",
    };
  }

  // Allow both GET (for SSE) and POST (if needed)
  if (event.httpMethod !== "GET" && event.httpMethod !== "POST") {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ message: "Method not allowed" }),
    };
  }

  // Try to extract token from Authorization header first
  let token = extractToken(event);

  // If not found in header, check query parameters (for SSE)
  if (!token && event.httpMethod === "GET" && event.queryStringParameters?.token) {
      token = event.queryStringParameters.token;
      console.log("Token found in query parameters.");
  }

  let authenticatedUserId: string | null = null;

  if (!token) {
    console.warn("Authentication failed: No token provided in header or query.");
    return {
      statusCode: 401,
      headers,
      body: JSON.stringify({ error: "Authentication required: No token provided" }),
    };
  }

  try {
    const decodedToken = await verifyFirebaseToken(token);
    authenticatedUserId = decodedToken.uid;
    console.log(`Authentication successful for user: ${authenticatedUserId}`);
  } catch (error) {
    console.error("Authentication failed: Invalid token.", error);
    return {
      statusCode: 401,
      headers,
      body: JSON.stringify({ error: "Authentication failed: Invalid token" }),
    };
  }

  let requestBody;
  if (event.httpMethod === "POST") {
    if (!event.body) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: "Request body is required for POST" }),
      };
    }
    if (event.headers["content-type"] !== "application/json") {
      return {
        statusCode: 415,
        headers,
        body: JSON.stringify({ error: "Content-Type must be application/json for POST" }),
      };
    }
    requestBody = JSON.parse(event.body);
  } else if (event.httpMethod === "GET") {
     // For GET, data is in query parameters
     requestBody = event.queryStringParameters;
     if (!requestBody || !requestBody.companyName || !requestBody.jobPosition) {
        return {
           statusCode: 400,
           headers,
           body: JSON.stringify({ error: "companyName and jobPosition query parameters are required for GET" }),
        };
     }
  }

  try {
    // Extract companyName and jobPosition based on HTTP method
    let companyName: string;
    let jobPosition: string;

    if (event.httpMethod === "POST") {
      const requestBody = JSON.parse(event.body);
      companyName = requestBody.companyName;
      jobPosition = requestBody.jobPosition;
    } else { // GET
      const queryParams = event.queryStringParameters;
      companyName = queryParams.companyName;
      jobPosition = queryParams.jobPosition;
    }

    // Call PerformJobAnalysis with direct parameters
    const analysisStream = await PerformJobAnalysis({
      companyName,
      jobPosition,
      userId: authenticatedUserId,
    });

    // --- Manually construct SSE from the stream ---
    let sseBody = "";
    const decoder = new TextDecoder("utf-8");
    const reader = analysisStream.getReader();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      const chunk = decoder.decode(value, { stream: true });
      // Each chunk from PerformJobAnalysis is expected to be a complete JSON object
      // or a partial JSON that can be completed by the client.
      // For SSE, we wrap each chunk in a 'data:' event.
      sseBody += `data: ${chunk}\n\n`;
    }
    sseBody += decoder.decode(); // Final decode for any remaining buffered data

    return {
      statusCode: 200,
      headers: {
        "Content-Type": "text/event-stream", // Set Content-Type for SSE
        "Access-Control-Allow-Origin": allowedOrigin,
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
        "Cache-Control": "no-cache", // Important for SSE
        "Connection": "keep-alive", // Important for SSE
      },
      body: sseBody, // Return the accumulated SSE string
    };
  } catch (error) {
    console.error(`API Error during job research:`, error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      }),
    };
  }
};

export { handler };
