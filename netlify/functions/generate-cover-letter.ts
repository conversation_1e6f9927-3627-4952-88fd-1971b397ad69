import { <PERSON><PERSON> } from "@netlify/functions";
import OpenAI from "openai";

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export const handler: Handler = async (event) => {
  // Define allowed origin and standard headers
  const allowedOrigin = event.headers.origin || "https://praxjobs.com";
  const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": allowedOrigin,
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };

  // Handle preflight requests
  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 204,
      headers,
    };
  }

  if (event.httpMethod !== "POST") {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: "Method not allowed" }),
    };
  }

  try {
    // Parse request body
    if (!event.body) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: "Request body is required" }),
      };
    }

    const body = JSON.parse(event.body);
    const { jobDescription, resumeContent, template, customInstructions } = body;

    // Validate required fields
    if (!jobDescription || !resumeContent) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          error: "Job description and resume content are required",
        }),
      };
    }

    // Check API key
    if (!process.env.OPENAI_API_KEY) {
      console.error("OpenAI API Key is missing");
      return {
        statusCode: 503,
        headers,
        body: JSON.stringify({
          error: "Cover letter generation service is temporarily unavailable",
        }),
      };
    }

    // Create system prompt based on template
    const systemPrompt = createSystemPrompt(template, customInstructions);

    // Create messages array with proper types
    const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
      {
        role: "system",
        content: systemPrompt,
      },
      {
        role: "user",
        content: `Job Description:\n${jobDescription}\n\nResume:\n${resumeContent}`,
      },
    ];

    // Create a streaming response
    const stream = await openai.chat.completions.create({
      model: "gpt-4.1-nano", // Using the same model as the client-side implementation
      messages: messages,
      stream: true,
      max_tokens: 3000,
      temperature: 0.5,
      top_p: 1,
    });

    // Set up streaming response headers
    const streamHeaders = {
      ...headers,
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      "Connection": "keep-alive",
    };

    // Create a response stream
    let responseBody = "";
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || "";
      if (content) {
        responseBody += `data: ${JSON.stringify({ content })}\n\n`;
      }
    }
    responseBody += `data: [DONE]\n\n`;

    // Return the response
    return {
      statusCode: 200,
      headers: streamHeaders,
      body: responseBody,
    };
  } catch (error) {
    console.error("Cover letter generation error:", error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: "Failed to generate cover letter",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
    };
  }
};

/**
 * Creates a system prompt based on the selected template and custom instructions
 * @param template The selected template
 * @param customInstructions Custom instructions provided by the user
 * @returns The system prompt
 */
function createSystemPrompt(
  template: string = "professional",
  customInstructions?: string
): string {
  let templateSpecifications = "";

  // Define template specifications
  switch (template) {
    case "professional":
      templateSpecifications = `- Tone: Formal and polished
- Language: Corporate and precise
- Structure: Clear, concise paragraphs
- Emphasis: Professional achievements and skills
- Avoid overly casual language or personal anecdotes`;
      break;

    case "creative":
      templateSpecifications = `- Tone: Engaging and innovative
- Language: Dynamic and passionate
- Structure: More narrative-driven paragraphs
- Emphasis: Unique personal brand and creative problem-solving
- Use storytelling techniques to highlight professional journey`;
      break;

    case "startup":
      templateSpecifications = `- Tone: Contemporary and forward-thinking
- Language: Tech-savvy and adaptable
- Structure: Concise, impact-driven sections
- Emphasis: Recent achievements, technological skills
- Incorporate industry-specific jargon and current trends`;
      break;

    default:
      templateSpecifications = `- Default to a professional, balanced approach
- Maintain clarity and professionalism
- Adapt tone to match the industry and role`;
      break;
  }

  // Create the system prompt
  const systemPrompt = `Generate a professional cover letter based on the following job description and resume. Follow these guidelines:

1. Format:
   - Use plain text without markup or special formatting strictly
   - Standard sections: Greeting, Opening Paragraph, Body Paragraphs, Closing Paragraph
   - Use standard business letter formatting
   - Avoid tables, columns, or complex layouts
   - Do NOT include any explanatory text at the end of the cover letter
   - Return ONLY the cover letter text without any additional commentary

2. Content:
   - Address the letter to the hiring manager (or "Dear Hiring Manager" if name unknown)
   - Tailor the content to match specific job requirements
   - Highlight relevant skills and experiences from the resume
   - Use keywords from the job description
   - Demonstrate enthusiasm and fit for the role
   - Dont lie at all and stick to truth from the resume

3. Style:
   - Maintain professional and engaging language
   - Be specific and results-oriented
   - Show how your skills solve the employer's needs
   - Keep the tone conversational yet professional
   - Limit to one page

4. Template Specifications:
   ${templateSpecifications}

Additional Context:
- Template Style: ${template}
- Custom Instructions: ${customInstructions || "None"}`;

  return systemPrompt;
}
