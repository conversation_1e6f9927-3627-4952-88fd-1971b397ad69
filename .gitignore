# build output
dist/
.output/
.astro/

# dependencies
node_modules/

# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
*.log

# environment variables
.env
.env.*
!.env.example

# macOS-specific files
.DS_Store

# Google Cloud Credentials
*.json.key
google-credentials.json
firebase-adminsdk-*.json
astro-ai-professional-firebase-adminsdk-fbsvc-fc715c8e4b.json

# Secrets and sensitive files
/secrets/*
!/secrets/firebase-admin-key.json
*.key
*.pem
*.p12

# Editor directories and files
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml
bun.lock

# Local Netlify folder
.netlify/

# Vercel
.vercel/

# Build cache
.cache/
