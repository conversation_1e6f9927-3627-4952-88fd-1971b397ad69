# Praxjobs Documentation

## Why Praxjobs?

PraxJobs empowers job seekers to land their dream roles faster and with less stress. Our suite of AI-powered tools helps you research jobs, optimize your resume and LinkedIn, generate tailored cover letters, track every application, and ace interviews—no guesswork, no writer's block, just results.

This document overviews the Praxjobs application's structure, including its backend APIs (Netlify Functions), frontend pages and key components.

## 1. Praxjobs Tool Descriptions & Key Differentiators

### Job Tracker Component

The Job Tracker is your command center for managing every aspect of your job search. With an intuitive dashboard, you can add, edit, and organize all your job applications in one place, tracking statuses, deadlines, and notes for each opportunity. Unlike generic spreadsheets or other trackers, Praxjobs integrates directly with its suite of AI tools—meaning you can jump from tracking a job to analyzing its description, tailoring your resume, or prepping for an interview with a single click. Reminders and smart filters ensure you never miss a follow-up or deadline, keeping your job search streamlined and stress-free.

**Key Differentiators:**

- Deep integration with AI-powered tools (resume, cover letter, analysis, interview prep) for a seamless workflow.
- Context-aware: Each job entry can pre-fill other tools with relevant information, saving time and reducing manual entry.
- Designed specifically for job seekers, not adapted from generic project management templates.

### Job research Tool

The Job research tool leverages advanced AI to break down any job description and compare it to your resume or LinkedIn profile. It highlights essential skills, must-have keywords, and responsibilities, then provides actionable suggestions to close any gaps. This empowers you to tailor your application materials with precision, ensuring you meet recruiter expectations and stand out in applicant tracking systems (ATS).

**Key Differentiators:**

- Real-time, side-by-side analysis of both job descriptions and your own documents.
- Actionable, plain-language recommendations—no technical jargon or generic advice.
- Market insights: See how your profile stacks up against industry standards and trends.

### Resume Builder & Resume Manager

Praxjobs' Resume Builder is more than just a template editor—it's an AI-powered writing assistant that helps you craft compelling, ATS-optimized resumes tailored to every job. Upload your current resume or start from scratch, and let the AI suggest bullet points, summaries, and achievements based on your experience and the job requirements. The Resume Manager keeps all your versions organized, so you can quickly find, edit, or download the right resume for every application.

**Key Differentiators:**

- AI-generated content suggestions based on both your experience and the specific job you're targeting.
- Multiple, modern templates designed for both human recruiters and ATS compatibility.
- Centralized management: Store, edit, and retrieve multiple resume versions with ease.

### Cover Letter Tool

The Cover Letter tool instantly generates personalized, impactful cover letters for every job application. Simply input the job description and your resume or profile, choose your preferred writing style (professional, creative, startup), and let the AI craft a unique draft that highlights your strengths and matches your qualifications to the employer's needs. Each letter is tailored, ensuring you never send a generic application.

**Key Differentiators:**

- Multiple tone/style options, so your cover letter matches both the job and your personality.
- AI ensures every letter is unique—no copy-paste templates or repetitive content.
- Deep integration with resume and job research tools for maximum relevance.

### LinkedIn Optimiser

The LinkedIn Optimiser transforms your profile into a recruiter magnet. By analyzing your resume and target job or industry, the AI provides specific, actionable suggestions for every section of your profile—headline, summary, experience, and skills. It also offers engagement and networking tips to boost your visibility and connections, helping you get discovered by the right employers.

**Key Differentiators:**

- Personalized, section-by-section improvement suggestions—not just generic tips.
- Covers engagement and networking strategy, not just profile content.
- Designed to align your LinkedIn presence with your career goals and target roles.

### Interview Preparation Tool

Prepare for interviews with confidence using Praxjobs' Interview Prep tool. Enter a job title, company, or description, and the AI generates a curated set of likely interview questions—covering company-specific, role-specific, behavioral, and technical areas. Practice your answers in a safe environment, then submit them for instant AI feedback and improvement tips. This tool helps you identify weaknesses and polish your responses before the real interview.

**Key Differentiators:**

- AI-generated questions tailored to the company, role, and industry—not just generic lists.
- Instant, actionable feedback on your answers, including clarity, relevance, and impact.
- Unlimited practice—no scheduling or waiting for a human coach.

### Guide Mode

Guide Mode is a multi-mode Copilot designed for broader career discussions. It lets users choose from various coaching modes—such as brainstorming new career paths, planning long-term goals, or discussing pivots. Powered by Gemini, it helps simulate strategic conversations you'd normally have with a mentor or coach.

**Key Differentiators:**

- Multiple guidance modes (strategy, career pivots, growth planning).
- More reflective and mentor-like tone, unlike task-based tools.
- Enables long-term planning, not just short-term job search help.

### AI Chat Copilot

The AI Chat Copilot is the user's always-available personal assistant. It can answer questions about job search, resume formatting, ATS tips, and more. Users can interact with it for quick tasks or in-depth queries, and it can guide them to the right Praxjobs tool based on the context of their query.

**Key Differentiators:**

- Natural chat interface with contextual memory.
- Acts as an intelligent entry point to all tools.
- Blends career advice with AI utility.

All APIs are secured with authentication where needed, ensuring user data privacy and safety.

## 2. User Onboarding Flow

The onboarding flow for Praxjobs ensures a smooth, motivating, and personalized start for every new user:

### Signup

User signs up via email or Google.

### Welcome Screen

A short intro explains Praxjobs' value and tools.

### Optional Setup Wizard

User is guided to:

- Upload their resume
- Pick a target role or industry
- Add their first job posting link (optional)

### Tool Suggestions

Based on the data provided, user is nudged toward one key tool to try first (e.g., Resume Builder or Job research).

### First Use Highlight

When opening the selected tool, helpful tips or a one-time walkthrough explains what it does.

### Dashboard Personalization

- Recent uploads, job research results, or AI suggestions appear.
- AI suggestions update over time.

### Encouragement & Retention

- Email reminder if a user hasn't returned after X days.
- Prompt to try another tool based on usage.

This onboarding is built to feel lightweight and optional, but it guides new users toward their first win—whether that's uploading a resume or generating their first cover letter.

## 3. Summary of Features & Benefits

- **All-in-one platform:** Everything you need for your job search in one place.
- **AI-powered:** Save time, get expert-quality results, and stand out from the crowd.
- **Personalized:** Tools adapt to your goals, experience, and target roles.
- **Secure:** Your data is protected with industry-standard authentication and privacy practices.

## 4. User Roles & Permissions

Praxjobs supports two types of users: Free and Pro users. Each user role has specific access levels to the platform's features:

### Free Users:

- Access to core tools such as Job Tracker, Resume Builder, and Job research.
- Limited to 5 uses per tool per month.
- Can upgrade to Pro for unlimited access.

### Pro Users:

- Unlock all features, including Interview Prep, LinkedIn Optimization, and full access to AI-generated feedback.
- Unlimited usage of all tools.
- Pro users enjoy priority support and access to exclusive tools.

Since there are no admins, all user data and settings are handled automatically within the platform.

### API Rate Limits & Quotas

To ensure fair usage and maintain performance, Praxjobs enforces rate limits on API calls:

**Free Users:**

- 5 API calls per tool per month (across all tools).
- Once the limit is exceeded, users will receive a prompt to upgrade to the pro version every time they try to use the feature.

**Pro Users:**

- Unlimited API calls for all tools and features.

When rate limits are exceeded for free users, they will receive a message indicating the limit has been reached, prompting them to wait until the next month or upgrade to Pro for uninterrupted access.

### Usage Examples

To help you get the most out of Praxjobs, here's a step-by-step example of how the tools work together:

**Job Application Process:**

1. Job research: Start by analyzing a job description using the Job research tool. This will compare the job requirements with your resume and highlight essential skills, keywords, and potential gaps.
2. Resume Builder: Once the analysis is complete, use the AI-generated suggestions to optimize your resume based on the job description. This will ensure your resume is tailored to the job's specific requirements.
3. Cover Letter Tool: After optimizing your resume, use the Cover Letter tool to generate a personalized cover letter. Simply input the job description and your resume, and the AI will create a letter that aligns with both.
4. Interview Prep: Finally, get ready for interviews by using the Interview Prep tool. The AI will generate a set of likely interview questions based on the job description and provide instant feedback on your responses to help you prepare better.

## 5. Netlify Functions (APIs in netlify/functions/)

These serverless APIs power the intelligence and automation behind Praxjobs. They include:

1. ai-copilot.ts: AI chat assistant for career advice, resume help, and job search Q&A. Lets users get instant, personalized support.
2. createPaymentLink.ts: Generates secure Razorpay payment links for subscription upgrades, enabling seamless payments.
3. guide-mode.ts: Handles AI-powered career guidance in different "modes" (e.g., career discussion, brainstorming) using Google Gemini.
4. interview-questions.ts: Generates tailored interview questions for any job or company. Prepares users for real-world interviews.
5. jobAnalysis.ts: Analyzes job descriptions and user resumes to highlight required skills, keywords, and gaps. Suggests improvements.
6. linkedin-optimize.ts: Provides actionable AI suggestions to improve LinkedIn profiles, including headlines, summaries, and skills and abilities.
7. review-answer.ts: Reviews user-submitted interview answers and provides AI-powered feedback and improvement tips.
8. upload-resume.ts: Handles secure upload and parsing of resumes in various formats, supporting document management and analysis.
9. auth.ts: Manages authentication, secure access, and user session handling for all APIs.
10. contact.ts: Processes contact/support form submissions.
11. extractors/: Utilities for extracting text from DOC, PDF, images (OCR), and more—enabling deep AI analysis of uploaded documents.
