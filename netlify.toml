[build]
  command = "npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "20"
  NODE_OPTIONS = "--no-warnings --experimental-modules"

[functions]
  directory = "netlify/functions"
  included_files = [
    "node_modules/textract/**",
    "node_modules/textract/lib/**",
    "node_modules/textract/lib/extractors/**",
    "secrets/firebase-admin-key.json" # Include the admin credentials file
  ]

# Firebase Auth Proxy
[[redirects]]
  from = "/__/auth/*"
  to = "https://astro-ai-professional.firebaseapp.com/__/auth/:splat"
  status = 200
  force = true

# API routes
[[redirects]]
  from = "/api/job-analysis"
  to = "/.netlify/functions/jobAnalysis-background" # Changed to background function
  status = 200

[[redirects]]
  from = "/api/job-analysis-status/:taskId"
  to = "/.netlify/functions/jobAnalysis-status/:taskId" # New function for polling status
  status = 200

[[redirects]]
  from = "/api/contact"
  to = "/.netlify/functions/contact"
  status = 200

[[redirects]]
  from = "/api/ai-copilot/*"
  to = "/.netlify/functions/ai-copilot/:splat"
  status = 200

[[redirects]]
  from = "/api/auth/*"
  to = "/.netlify/functions/auth/:splat"
  status = 200

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/api/:splat"
  status = 200

[[redirects]]
  from = "/api/create-payment-link"
  to = "/.netlify/functions/createPaymentLink"
  status = 200

[[redirects]]
  from = "/api/upload-resume"
  to = "/.netlify/functions/upload-resume"
  status = 200

[[redirects]]
  from = "/api/guide-mode"
  to = "/.netlify/functions/guide-mode"
  status = 200

# SPA fallback
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    Referrer-Policy = "strict-origin-when-cross-origin"
