{"extends": "astro/tsconfigs/strict", "compilerOptions": {"target": "ES2020", "module": "ESNext", "strict": true, "strictNullChecks": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "types": ["node"], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "moduleResolution": "node", "jsx": "react-jsx", "jsxImportSource": "react"}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/global.d.ts", "runAuthTests.mts"], "exclude": ["node_modules"]}