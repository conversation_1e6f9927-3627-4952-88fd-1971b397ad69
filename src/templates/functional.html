<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Functional Resume</title>
    <style>
        body {
            font-family: sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .job-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .job-entry, .education-entry {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .job-entry:last-child, .education-entry:last-child {
            border-bottom: none;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        h2 {
            color: #555;
            border-bottom: 2px solid #333;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        h3 {
            color: #333;
            margin-bottom: 5px;
        }
        a {
            color: #0066cc;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <header>
        <h1>{{contactInfo.fullName}}</h1>
        <p>{{contactInfo.email}} | {{contactInfo.phone}} | {{contactInfo.location}}</p>
        <p><a href="{{contactInfo.linkedin}}">LinkedIn</a> | <a href="{{contactInfo.portfolio}}">Portfolio</a></p>
    </header>
    <section>
        <h2>Summary</h2>
        <p>{{summary}}</p>
    </section>
    <section>
        <h2>Skills</h2>
        <p>{{skills}}</p>
    </section>
    <section>
        <h2>Work Experience</h2>
        {{#each workExperience}}
        <div class="job-entry">
            <div class="job-details">
                <h3>{{jobTitle}}</h3>
                <span>{{startDate}} - {{endDate}}</span>
            </div>
            <p><strong>{{company}}</strong> - {{location}}</p>
            <p>{{description}}</p>
        </div>
        {{/each}}
    </section>
    <section>
        <h2>Education</h2>
        {{#each education}}
        <div class="education-entry">
            <h3>{{institution}}</h3>
            <p><strong>{{degree}}</strong> in {{fieldOfStudy}}</p>
            <p>{{graduationDate}}</p>
            <p>{{details}}</p>
        </div>
        {{/each}}
    </section>
</body>
</html>