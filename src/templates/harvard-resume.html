<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            border: 1px solid #ccc;
        }
        h1, h2, h3 {
            margin-top: 0;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            font-size: 36px;
            margin-bottom: 5px;
        }
        .contact-info {
            font-size: 14px;
        }
        .section {
            margin-bottom: 20px;
        }
        .section h2 {
            border-bottom: 2px solid #333;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }
        .job, .education-item {
            margin-bottom: 15px;
        }
        .job-title {
            font-weight: bold;
        }
        .job-details {
            display: flex;
            justify-content: space-between;
        }
        ul {
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{contactInfo.fullName}}</h1>
            <p class="contact-info">
                {{contactInfo.email}} | {{contactInfo.phoneNumber}} | {{contactInfo.website}}
            </p>
        </div>

        <div class="section">
            <h2>Summary</h2>
            <p>{{summary.content}}</p>
        </div>

        <div class="section">
            <h2>Experience</h2>
            {{#each experience}}
            <div class="job">
                <div class="job-details">
                    <span class="job-title">{{this.jobTitle}} at {{this.company}}</span>
                    <span>{{this.startDate}} - {{this.endDate}}</span>
                </div>
                <ul>
                    {{#each this.highlights}}
                    <li>{{this}}</li>
                    {{/each}}
                </ul>
            </div>
            {{/each}}
        </div>

        <div class="section">
            <h2>Education</h2>
            {{#each education}}
            <div class="education-item">
                <div class="job-details">
                    <span class="job-title">{{this.degree}} at {{this.school}}</span>
                    <span>{{this.date}}</span>
                </div>
            </div>
            {{/each}}
        </div>

        <div class="section">
            <h2>Skills</h2>
            <p>{{skills.join(', ')}}</p>
        </div>
    </div>
</body>
</html>