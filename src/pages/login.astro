---
import Layout from "../layouts/Layout.astro";
import LoginForm from "../components/auth/LoginForm.astro";

// Note: Authentication check is handled client-side to avoid server/client conflicts
---

<Layout 
    title="Login to PraxJobs | Access Your Career Tools"
    description="Sign in to your PraxJobs account to access resume building, job research, cover letter generation, and interview preparation tools."
    image="/images/login-og-image.jpg"
    type="website"
    canonical="/login"
>
    <main class="relative min-h-screen bg-gray-50 dark:bg-gray-950 pt-16">
        <section
            class="min-h-screen flex items-center justify-center px-4 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950 dark:to-gray-950 transition-all duration-500"
        >
            <LoginForm />
        </section>
    </main>
</Layout>

<script>
    import { isAuthenticated } from "../lib/authStore";

    // Check authentication state on page load
    async function checkAuthState() {
        const authenticated = await isAuthenticated();

        if (authenticated) {
            const redirectUrl =
                new URLSearchParams(window.location.search).get("redirect") ||
                "/dashboard";
            window.location.href = decodeURIComponent(redirectUrl);
        }
    }

    // Initial auth state check
    checkAuthState();
</script>
