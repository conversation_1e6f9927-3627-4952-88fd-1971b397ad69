---
import Layout from '../layouts/Layout.astro';
import ResumeBuilderComponent from '../components/tools/ResumeBuilder.astro';
import Container from '../components/Container.astro';
---

<Layout
  title="Resume Builder | Create a Professional Resume"
  description="Build a professional resume from scratch with our easy-to-use resume builder. Choose from a variety of templates and customize your resume to land your dream job."
  image="/images/resume-og-image.jpg"
  type="website"
  canonical="/resume-builder"
  ogTitle="Resume Builder | Create a Professional Resume | PraxJobs"
  ogDescription="Build a professional resume from scratch with our easy-to-use resume builder. Choose from a variety of templates and customize your resume to land your dream job."
>
  <main class="relative min-h-screen dark:bg-gray-950 pt-16">
    <div
      aria-hidden="true"
      class="absolute inset-0 grid grid-cols-2 -space-x-52 opacity-40 dark:opacity-20"
    >
      <div
        class="blur-[106px] h-56 bg-gradient-to-br from-blue-500 to-purple-300 dark:from-blue-500 dark:to-purple-500"
      >
      </div>
      <div
        class="blur-[106px] h-56 bg-gradient-to-r from-blue-500 to-purple-300 dark:from-blue-500 dark:to-purple-500"
      >
      </div>
    </div>
    <Container>
      <div class="relative pt-36 space-y-20">
        <div class="w-full text-center mx-auto">
          <h1
            class="text-gray-900 dark:text-white font-bold text-4xl md:text-5xl lg:text-6xl xl:text-7xl"
          >
            <span class="text-primary dark:text-white"
              >Build a Resume That Gets Noticed</span
            >
          </h1>
          <p
            class="mt-8 text-gray-700 dark:text-gray-300 text-lg max-w-3xl mx-auto"
          >
            Create a professional resume in minutes with our easy-to-use resume builder. Choose from a variety of templates, customize your content, and download a polished PDF that will impress recruiters.
          </p>

          <!-- Stats -->
          <div
            class="hidden py-8 mt-16 border-y border-gray-100 dark:border-gray-800 sm:flex justify-between"
          >
            <div class="text-center">
              <h6 class="text-lg font-semibold text-gray-700 dark:text-white">
                Easy to Use
              </h6>
              <p class="mt-2 text-gray-500">Intuitive drag-and-drop interface</p>
            </div>
            <div class="text-center">
              <h6 class="text-lg font-semibold text-gray-700 dark:text-white">
                Professional Templates
              </h6>
              <p class="mt-2 text-gray-500">Designed by experts to impress</p>
            </div>
            <div class="text-center">
              <h6 class="text-lg font-semibold text-gray-700 dark:text-white">
                Instant Download
              </h6>
              <p class="mt-2 text-gray-500">Get your resume in PDF format</p>
            </div>
          </div>
        </div>
      </div>
    </Container>

    <!-- Generator Section -->
    <section id="generator" class="relative w-full py-16 sm:py-20">
      <ResumeBuilderComponent />
    </section>

    <!-- Benefits Section -->
    <Container>
      <section
        id="benefits"
        class="relative py-16 w-full overflow-hidden rounded-2xl"
      >
        <div class="container relative mx-auto p-4 sm:p-6 lg:p-8">
          <!-- Main Heading -->
          <div class="text-center mb-16">
            <h2
              class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4"
            >
              Everything You Need to Create a Winning Resume
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-400">
              Our resume builder is packed with features to help you create a professional resume that stands out.
            </p>
          </div>

          <!-- Feature Grid -->
          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
            role="list"
            aria-label="Key features of the Resume Builder"
          >
            <!-- Easy to Use Card -->
            <div
              class="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              role="listitem"
            >
              <div
                class="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center mb-4"
              >
                <svg
                  class="w-6 h-6 text-blue-600 dark:text-blue-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
              </div>
              <h3
                class="text-xl font-semibold text-gray-900 dark:text-white mb-2"
              >
                Intuitive Interface
              </h3>
              <p class="text-gray-600 dark:text-gray-400">
                Our user-friendly editor makes it easy to create a professional resume in minutes.
              </p>
            </div>

            <!-- Professional Templates Card -->
            <div
              class="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              role="listitem"
            >
              <div
                class="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center mb-4"
              >
                <svg
                  class="w-6 h-6 text-green-600 dark:text-green-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12.75L11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 01-3.296-1.043 3.745 3.745 0 011.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.746 3.746 0 013.296-1.043A3.746 3.746 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 013.296 1.043 3.746 3.746 0 011.043 3.296A3.745 3.745 0 0121 12z"
                  ></path>
                </svg>
              </div>
              <h3
                class="text-xl font-semibold text-gray-900 dark:text-white mb-2"
              >
                Expert-Designed Templates
              </h3>
              <p class="text-gray-600 dark:text-gray-400">
                Choose from a variety of professionally designed templates that are proven to get results.
              </p>
            </div>

            <!-- Customizable Sections Card -->
            <div
              class="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              role="listitem"
            >
              <div
                class="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center mb-4"
              >
                <svg
                  class="w-6 h-6 text-purple-600 dark:text-purple-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.44 7.152A50.489 50.489 0 0012 4.5c1.933 0 3.811.166 5.656.47A50.67 50.67 0 0117.556 7.15M6.44 7.152A50.487 50.487 0 004.5 12c0 1.933.166 3.811.47 5.656.26 1.646.78 3.25 1.566 4.666m0-15.482a50.487 50.487 0 00-1.566 4.666"
                  ></path>
                </svg>
              </div>
              <h3
                class="text-xl font-semibold text-gray-900 dark:text-white mb-2"
              >
                Flexible Customization
              </h3>
              <p class="text-gray-600 dark:text-gray-400">
                Add, remove, and reorder sections to create a resume that is uniquely yours.
              </p>
            </div>
          </div>
        </div>
      </section>
    </Container>
  </main>
</Layout>