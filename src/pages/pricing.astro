---
import Layout from "../layouts/Layout.astro";
import PaymentSuccessModal from "../components/PaymentSuccessModal.astro";
import ContactModal from "../components/ContactModal.astro";
import { getAuth } from "firebase/auth";
import { doc, getFirestore, setDoc } from "firebase/firestore";
import { initializeFirebase } from "../lib/firebase";
import { TierManagementService } from "../lib/tierManagement";
import type { SubscriptionTier } from '../lib/subscriptionConfig'; // Import for frontmatter

// Utility function for logging
async function safeLogUpgradeAttempt(details: {
  userId: string | null;
  success: boolean;
  tier?: string | null;
  referenceId?: string | null;
  timestamp?: string | null;
}) {
  if (!details.userId) return;

  try {
    const db = getFirestore();
    const logDocRef = doc(db, "upgradeLogs", `${details.userId}_${Date.now()}`);
    await setDoc(logDocRef, {
      ...details,
      createdAt: new Date().toISOString(),
      environment: import.meta.env.MODE,
    });
    console.log("✅ Upgrade attempt logged successfully");
  } catch (error) {
    console.error("❌ Failed to log upgrade attempt", error);
  }
}

// Initialize Firebase
initializeFirebase();

const auth = getAuth();
const user = auth.currentUser;

// Extract URL parameters
const proUpgradeSuccess =
  Astro.url.searchParams.get("proUpgradeSuccess") === "true";
const userId = Astro.url.searchParams.get("userId");
const referenceId = Astro.url.searchParams.get("referenceId");
const tier = Astro.url.searchParams.get("tier");
const timestamp = Astro.url.searchParams.get("timestamp");

// Declare razorpayPaymentId here to make it accessible later
let razorpayPaymentIdFromUrl: string | null = null;

console.log("💡 Pricing Page Upgrade Check:", {
  proUpgradeSuccess,
  userId,
  referenceId,
  fullParams: Object.fromEntries(Astro.url.searchParams),
});

// Server-side processing for payment success
if (proUpgradeSuccess && userId && tier) {
  (async () => {
    try {
      // Log initial upgrade attempt
      await safeLogUpgradeAttempt({
        userId,
        success: false,
        tier,
        referenceId,
        timestamp,
      });

      // Dynamically import services
      const razorpayService = await import("../lib/razorpayService").then(
        (m) => m.razorpayService
      );
      const { TierManagementService } = await import("../lib/tierManagement");

      // Verify payment details
      razorpayPaymentIdFromUrl = Astro.url.searchParams.get( // Assign to the hoisted variable
        "razorpay_payment_id"
      );
      const razorpayPaymentLinkId = Astro.url.searchParams.get(
        "razorpay_payment_link_id"
      );
      const razorpayPaymentLinkStatus = Astro.url.searchParams.get(
        "razorpay_payment_link_status"
      );
      const razorpaySignature =
        Astro.url.searchParams.get("razorpay_signature");

      // Prepare payment details
      const paymentDetails = {
        razorpay_payment_id: razorpayPaymentIdFromUrl || "", // Use the hoisted variable
        razorpay_payment_link_id: razorpayPaymentLinkId || undefined,
      };

      // Verify signature and payment status
      const paymentVerified =
        razorpayService.verifyPaymentSignature(
          paymentDetails,
          razorpaySignature || ""
        ) && razorpayPaymentLinkStatus === "paid";

      if (paymentVerified) {
        // Upgrade user tier using the tier from URL parameters
        const tierFromUrl = tier as SubscriptionTier; // tier is already from Astro.url.searchParams.get("tier")
        await TierManagementService.upgradeUserTier(
          userId,
          tierFromUrl, // Use the dynamic tier
          razorpayPaymentIdFromUrl || undefined // Use the hoisted variable
        );

        // Log successful upgrade
        await safeLogUpgradeAttempt({
          userId,
          success: true,
          tier,
          referenceId,
          timestamp,
        });
      }
    } catch (error) {
      // Log any errors during upgrade process
      await safeLogUpgradeAttempt({
        userId,
        success: false,
        tier,
        referenceId,
        timestamp,
      });

      console.error("Upgrade process error:", error);
    }
  })();
}

// Get Razorpay key from environment
const RAZORPAY_KEY_ID = import.meta.env.PUBLIC_RAZORPAY_KEY_ID;
if (!RAZORPAY_KEY_ID) {
  throw new Error("RAZORPAY_KEY_ID is not set in environment variables");
}

// Check for success or failure messages
const paymentFailed = Astro.url.searchParams.get("paymentFailed") === "true";

console.log("💡 Pricing Page Upgrade Check:", {
  proUpgradeSuccess,
  userId,
  referenceId,
  fullParams: Object.fromEntries(Astro.url.searchParams),
});

// Attempt to upgrade tier if conditions are met
if (proUpgradeSuccess && userId) {
  try {
    await TierManagementService.updateSubscriptionTierAfterPayment(
      tier as SubscriptionTier,
      userId,
      tier === "pro" ? (razorpayPaymentIdFromUrl || undefined) : undefined
    );
  } catch (error) {
    console.error("Automatic tier upgrade failed:", error);
  }
}
---

<Layout
  title="PraxJobs Pricing | Upgrade to Pro for Premium Career Tools"
  description="Explore PraxJobs pricing plans and upgrade to Pro for premium access to career tools, including resume building, job research, and interview preparation."
  image="/images/pricing-og-image.jpg"
  type="website"
  canonical="/pricing"
  ogTitle="PraxJobs Premium Plans | Invest in Your Career Success"
  ogDescription="Unlock the full potential of career tools with PraxJobs Pro. Compare plans and choose the best option for your job search needs."
>
  <!-- Add Razorpay Script -->
  <script src="https://checkout.razorpay.com/v1/checkout.js"></script>

  <!-- Conditionally render PaymentSuccessModal -->
  {proUpgradeSuccess && <PaymentSuccessModal isOpen={true} />}

  <!-- Contact Modal -->
  <ContactModal
    title="Get in Touch"
    subtitle="Have questions about our pricing or need a custom plan for your organization? Our team is ready to help you find the perfect solution."
  />

  {
    proUpgradeSuccess && (
      <div
        id="toast-success"
        class="fixed top-5 right-5 z-50 flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert"
      >
        <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
          <svg
            class="w-5 h-5"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
          </svg>
          <span class="sr-only">Check icon</span>
        </div>
        <div class="ml-3 text-sm font-normal">
          Congratulations! You are now a Pro member.
        </div>
        <button
          type="button"
          class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
          data-dismiss-target="#toast-success"
          aria-label="Close"
        >
          <span class="sr-only">Close</span>
          <svg
            class="w-3 h-3"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 14 14"
          >
            <path
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
            />
          </svg>
        </button>
      </div>
    )
  }

  {
    paymentFailed && (
      <div
        id="toast-error"
        class="fixed top-5 right-5 z-50 flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow dark:text-gray-400 dark:bg-gray-800"
        role="alert"
      >
        <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
          <svg
            class="w-5 h-5"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
          </svg>
          <span class="sr-only">Error icon</span>
        </div>
        <div class="ml-3 text-sm font-normal">
          Payment failed. Please try again.
        </div>
        <button
          type="button"
          class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
          data-dismiss-target="#toast-error"
          aria-label="Close"
        >
          <span class="sr-only">Close</span>
          <svg
            class="w-3 h-3"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 14 14"
          >
            <path
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
            />
          </svg>
        </button>
      </div>
    )
  }

  <main class="relative min-h-screen dark:bg-gray-950">
    <!-- Gradient Background -->
    <div
      aria-hidden="true"
      class="absolute inset-0 grid grid-cols-2 -space-x-52 opacity-40 dark:opacity-20"
    >
      <div
        class="blur-[106px] h-56 bg-gradient-to-br from-primary to-purple-400 dark:from-blue-700"
      >
      </div>
      <div
        class="blur-[106px] h-32 bg-gradient-to-r from-cyan-400 to-sky-300 dark:to-indigo-600"
      >
      </div>
    </div>

    <section
      class="relative bg-white dark:bg-gray-950 py-32 transition-colors duration-300 ease-in-out"
    >
      <div class="mx-auto max-w-5xl px-6">
        <div class="mx-auto max-w-2xl space-y-6 text-center">
          <h1
            class="text-title text-center text-4xl font-semibold lg:text-5xl bg-gradient-to-r from-gray-900 via-gray-700 to-gray-900 dark:from-white dark:via-gray-200 dark:to-white bg-clip-text text-transparent animate-gradient-x"
          >
            Pricing that Scales with You
          </h1>
          <p
            class="text-body text-gray-600 dark:text-gray-300 max-w-xl mx-auto"
          >
            Choose the plan that best fits your needs.
          </p>
        </div>

        <!-- Billing Cycle Toggle -->
        <div class="flex justify-center items-center my-8">
          <div class="relative flex w-full max-w-xs p-1 bg-gray-100 dark:bg-gray-800 rounded-xl">
            <button
              id="billingMonthlyBtn"
              type="button"
              class="relative w-1/2 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-lg focus:outline-none transition-colors"
            >
              Monthly
            </button>
            <button
              id="billingQuarterlyBtn"
              type="button"
              class="relative w-1/2 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-lg focus:outline-none transition-colors"
            >
              Quarterly <span id="savingsBadge" class="ml-1 px-2 py-0.5 bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100 text-xs font-semibold rounded-full">Save 16%</span>
            </button>
            <div
              id="billingToggleBg"
              class="absolute top-1 left-1 h-[calc(100%-0.5rem)] w-[calc(50%-0.25rem)] bg-primary dark:bg-primary rounded-lg shadow-md transition-transform duration-300 ease-in-out"
            ></div>
          </div>
        </div>
        {/* Hidden input to store toggle state if needed, or manage in JS */}
        <input type="checkbox" id="billingCycleToggle" class="hidden" />


        <div class="mt-12 grid gap-8 lg:grid-cols-2 lg:gap-12">
          <!-- Free Tier -->
          <div
            class="relative group rounded-2xl bg-white dark:bg-gray-900 p-8 lg:p-10 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 backdrop-blur-sm"
          >
            <div class="space-y-6">
              <div class="space-y-3">
                <span
                  class="inline-block text-primary dark:text-blue-400 font-medium"
                  >Starter</span
                >
                <div class="flex items-baseline">
                  <span class="text-4xl font-bold text-gray-900 dark:text-white"
                    >₹0</span
                  >
                  <span class="ml-1 text-sm text-gray-500 dark:text-gray-400"
                    >/month</span
                  >
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  Perfect for getting started
                </p>
              </div>

              <button
                class="w-full inline-flex items-center justify-center px-6 py-3 rounded-xl text-base font-medium text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200 group-hover:scale-[1.02]"
              >
                Get Started
              </button>

              <ul class="space-y-4 text-sm text-gray-600 dark:text-gray-300">
                <li class="flex items-start gap-3">
                  <svg
                    class="w-5 h-5 text-primary dark:text-blue-400 shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-black dark:text-white"
                    >5 Resume Generations</span
                  >
                </li>
                <li class="flex items-start gap-3">
                  <svg
                    class="w-5 h-5 text-primary dark:text-blue-400 shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-black dark:text-white"
                    >5 Cover Letter Generations</span
                  >
                </li>
                <li class="flex items-start gap-3">
                  <svg
                    class="w-5 h-5 text-primary dark:text-blue-400 shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-black dark:text-white"
                    >5 Job research Requests</span
                  >
                </li>
                <li class="flex items-start gap-3">
                  <svg
                    class="w-5 h-5 text-primary dark:text-blue-400 shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-black dark:text-white"
                    >5 Job Application Tracking</span
                  >
                </li>
                <li class="flex items-start gap-3">
                  <svg
                    class="w-5 h-5 text-primary dark:text-blue-400 shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-black dark:text-white"
                    >5 Job Interview Preparation</span
                  >
                </li>
                <li class="flex items-start gap-3">
                  <svg
                    class="w-5 h-5 text-primary dark:text-blue-400 shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-black dark:text-white"
                    >5 LinkedIn Profile Analysis</span
                  >
                </li>
                <li class="flex items-start gap-3">
                  <svg
                    class="w-5 h-5 text-primary dark:text-blue-400 shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-black dark:text-white">Basic Support</span>
                </li>
              </ul>
            </div>
          </div>

          <!-- Pro Tier -->
          <div
            class="relative group border border-purple-900 rounded-2xl bg-gradient-to-b from-gray-900 to-gray-800 dark:from-gray-800 dark:to-gray-900 p-8 lg:p-10 shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden"
          >
            <!-- Popular badge -->
            <div
              class="absolute -right-12 top-8 rotate-45 bg-gradient-to-r from-primary to-blue-600 text-white text-xs font-bold py-1.5 px-12 transform"
            >
              Most Popular
            </div>

            <div class="space-y-6">
              <div class="space-y-3">
                <span
                  class="inline-block text-blue-400 font-medium"
                  >Pro</span
                >
                <div class="flex items-baseline">
                  <span id="proPrice" class="text-4xl font-bold text-white"
                    >₹599</span
                  >
                  <span id="proBillingCycle" class="ml-1 text-sm text-gray-400"
                    >/month</span
                  >
                </div>
                <p class="text-sm text-gray-400">
                  For power users and professionals
                </p>
              </div>
              <button
                  id="proUpgradeButton"
                  data-tier="pro_monthly"
                  class="w-full inline-flex items-center justify-center px-6 py-3 rounded-xl text-base font-medium text-gray-900 bg-white hover:bg-gray-50 transition-colors duration-200 group-hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed mt-6"
                >
                  <span class="btn-label">Upgrade to Pro</span>
                </button>
                <div
                  id="upgradeErrorContainer"
                  class="text-red-400 text-sm text-center hidden"
                >
                </div>

              <ul class="space-y-4 text-sm text-gray-300">
                <li class="flex items-start gap-3">
                  <svg
                    class="w-5 h-5 text-blue-400 shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span>300 Resume Generation</span>
                </li>
                <li class="flex items-start gap-3">
                  <svg
                    class="w-5 h-5 text-blue-400 shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span>300 Cover Letter Generation</span>
                </li>
                <li class="flex items-start gap-3">
                  <svg
                    class="w-5 h-5 text-blue-400 shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span>300 Job Research Requests</span>
                </li>
                <li class="flex items-start gap-3">
                  <svg
                    class="w-5 h-5 text-blue-400 shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span>300 Job Application Tracking</span>
                </li>
                <li class="flex items-start gap-3">
                  <svg
                    class="w-5 h-5 text-blue-400 shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span>300 Job Interview Preparation</span>
                </li>
                <li class="flex items-start gap-3">
                  <svg
                    class="w-5 h-5 text-blue-400 shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span>300 LinkedIn Profile Analysis</span>
                </li>
                <li class="flex items-start gap-3">
                  <svg
                    class="w-5 h-5 text-blue-400 shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span>Priority Support</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <!-- FAQ and Contact Section -->
    <section
      class="relative py-24 bg-gradient-to-b from-transparent to-gray-50 dark:to-gray-900/30"
    >
      <div class="mx-auto max-w-5xl px-6">
        <div class="grid md:grid-cols-2 gap-12">
          <!-- FAQ Column -->
          <div>
            <h2 class="text-3xl font-bold mb-8 text-gray-900 dark:text-white">
              Frequently Asked Questions
            </h2>
            <div class="space-y-6">
              {
                [
                  {
                    question: "Can I upgrade my plan later?",
                    answer:
                      "Yes, you can upgrade your plan at any time. Your existing credits will carry over to the new plan.",
                  },
                  {
                    question: "How secure is my data?",
                    answer:
                      "Data is stored on secure servers and is never shared with third parties.",
                  },
                  {
                    question: "What payment methods do you accept?",
                    answer:
                      "We accept all major credit cards, debit cards, and UPI payments through the payment gateway.",
                  },
                  {
                    question: "Can I cancel my subscription?",
                    answer:
                      "Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your billing period.",
                  },
                ].map((faq) => (
                  <div class="relative group bg-white dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 hover:shadow-lg transition-all duration-300">
                    <details class="group/faq">
                      <summary class="flex justify-between items-center cursor-pointer list-none">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white pr-8">
                          {faq.question}
                        </h3>
                        <span class="text-primary dark:text-blue-400 transition-transform duration-300 group-open/faq:rotate-180">
                          <svg
                            class="w-5 h-5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </span>
                      </summary>
                      <p class="mt-4 text-gray-600 dark:text-gray-300 leading-relaxed">
                        {faq.answer}
                      </p>
                    </details>
                  </div>
                ))
              }
            </div>
          </div>

          <!-- Contact Column -->
          <div>
            <h2
              class="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white"
            >
              Contact Us
            </h2>

            <div
              class="flex flex-col items-center justify-center space-y-6 p-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700"
            >
              <p class="text-gray-600 dark:text-gray-300 text-center">
                Have questions about our pricing or need a custom plan for your
                organization? Our team is ready to help you find the perfect
                solution.
              </p>

              <button
                id="openContactButton"
                class="inline-flex items-center px-8 py-4 border border-transparent rounded-lg shadow-md text-base font-medium text-white bg-black dark:bg-white dark:text-black hover:bg-blue-700 dark:hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              >
                Contact Us
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 ml-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  ></path>
                </svg>
              </button>

              <div class="gap-4 w-full mt-6">
                <div
                  class="flex items-center p-4 bg-gray-50 dark:bg-gray-900 rounded-lg"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-blue-500 mr-3"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-sm text-gray-600 dark:text-gray-300"
                    >Response within 24 hours</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <script>
    import { getAuth } from "firebase/auth";
    import { initializeProSubscription } from "../lib/payment";
    // SubscriptionTier type is not directly used in this client-side script anymore, but keeping import doesn't hurt
    import type { SubscriptionTier } from '../lib/subscriptionConfig'; 

    const proUpgradeButton = document.getElementById("proUpgradeButton") as HTMLButtonElement | null;
    // const billingCycleToggle = document.getElementById("billingCycleToggle") as HTMLInputElement | null; // Replaced by buttons
    const proPrice = document.getElementById("proPrice");
    const proBillingCycle = document.getElementById("proBillingCycle");
    const errorContainer = document.getElementById("upgradeErrorContainer");

    const billingMonthlyBtn = document.getElementById("billingMonthlyBtn") as HTMLButtonElement | null;
    const billingQuarterlyBtn = document.getElementById("billingQuarterlyBtn") as HTMLButtonElement | null;
    const billingToggleBg = document.getElementById("billingToggleBg") as HTMLDivElement | null;
    const savingsBadge = document.getElementById("savingsBadge") as HTMLSpanElement | null;
    const billingCycleToggleInput = document.getElementById("billingCycleToggle") as HTMLInputElement | null; // Hidden input


    const monthlyPrice = "₹599";
    const quarterlyPrice = "₹1499"; 
    const monthlyBillingText = "/month";
    const quarterlyBillingText = "/quarter"; 

    function setActiveButton(isQuarterly: boolean) {
      if (!billingMonthlyBtn || !billingQuarterlyBtn || !billingToggleBg || !savingsBadge || !billingCycleToggleInput) return;

      billingCycleToggleInput.checked = isQuarterly; // Update hidden input

      // Reset all text classes first
      billingMonthlyBtn.classList.remove("text-black", "dark:text-white", "text-gray-500", "dark:text-gray-400");
      billingQuarterlyBtn.classList.remove("text-black", "dark:text-white", "text-gray-500", "dark:text-gray-400");

      if (isQuarterly) {
        billingQuarterlyBtn.classList.add("text-black", "dark:text-white"); // Active text: black in light, white in dark
        billingMonthlyBtn.classList.add("text-gray-500", "dark:text-gray-400"); // Inactive text
        billingToggleBg.style.transform = "translateX(calc(100% - 0.0rem))"; 
      } else {
        billingMonthlyBtn.classList.add("text-black", "dark:text-white"); // Active text: black in light, white in dark
        billingQuarterlyBtn.classList.add("text-gray-500", "dark:text-gray-400"); // Inactive text
        billingToggleBg.style.transform = "translateX(0%)";
      }
    }
    
    // Function to update Pro tier details based on toggle
    function updateProTierDisplay(isQuarterly: boolean) {
      if (proPrice) {
        proPrice.textContent = isQuarterly ? quarterlyPrice : monthlyPrice;
      }
      if (proBillingCycle) {
        proBillingCycle.textContent = isQuarterly ? quarterlyBillingText : monthlyBillingText;
      }
      if (proUpgradeButton) {
        const btnLabel = proUpgradeButton.querySelector('.btn-label');
        if (btnLabel) {
          // btnLabel.textContent = isQuarterly ? "Upgrade to Pro (Quarterly)" : "Upgrade to Pro (Monthly)"; // Removed as per request
          btnLabel.textContent = "Upgrade to Pro";
        }
        proUpgradeButton.dataset.tier = isQuarterly ? "pro_quarterly" : "pro_monthly";
      }
      setActiveButton(isQuarterly);
    }

    billingMonthlyBtn?.addEventListener("click", () => updateProTierDisplay(false));
    billingQuarterlyBtn?.addEventListener("click", () => updateProTierDisplay(true));

    // Initialize display (default to monthly)
    updateProTierDisplay(false);


    // Function to handle upgrade button click
    async function handleUpgradeClick(event: MouseEvent) {
      event.preventDefault();
      event.stopPropagation();

      if (!proUpgradeButton || !billingCycleToggleInput) return; // Ensure hidden input is present

      const selectedTier = proUpgradeButton.dataset.tier as 'pro_monthly' | 'pro_quarterly' | undefined;

      if (!selectedTier) {
        console.error("Selected tier not found on button.");
        if (errorContainer) {
            errorContainer.textContent = "Error: Tier not selected.";
            errorContainer.classList.remove("hidden");
        }
        return;
      }

      // Clear any previous errors
      if (errorContainer) {
        errorContainer.textContent = "";
        errorContainer.classList.add("hidden");
      }

      try {
        // Check authentication
        const auth = getAuth();
        const user = auth.currentUser;

        if (!user) {
          if (errorContainer) {
            errorContainer.textContent = "Please sign in to upgrade";
            errorContainer.classList.remove("hidden");
          }
          setTimeout(() => {
            window.location.href = "/login";
          }, 1500);
          return;
        }

        proUpgradeButton.disabled = true;
        const btnLabel = proUpgradeButton.querySelector('.btn-label');
        if (btnLabel) btnLabel.innerHTML = "Processing...";

        // Map 'pro_monthly' to 'pro' for the initializeProSubscription function
        const tierForSubscription = selectedTier === 'pro_monthly' ? 'pro' : selectedTier;
        await initializeProSubscription(tierForSubscription);

      } catch (error) {
        console.error("Upgrade error:", error);
        if (errorContainer) {
          errorContainer.textContent =
            error instanceof Error
              ? error.message
              : "Failed to initialize upgrade. Please try again.";
          errorContainer.classList.remove("hidden");
        }
      } finally {
        proUpgradeButton.disabled = false;
        // Restore button text based on current toggle state
        if (billingCycleToggleInput) { // Use hidden input's state
            updateProTierDisplay(billingCycleToggleInput.checked);
        } else {
            // Default to monthly if toggle somehow not found
             const btnLabel = proUpgradeButton.querySelector('.btn-label');
            if (btnLabel) btnLabel.innerHTML = 'Upgrade to Pro'; // Updated default
        }
      }
    }

    proUpgradeButton?.addEventListener("click", handleUpgradeClick);

  </script>

  <script>
    // Auto-dismiss toasts after 5 seconds
    document.addEventListener("DOMContentLoaded", () => {
      const successToast = document.getElementById("toast-success");
      const errorToast = document.getElementById("toast-error");
      const openContactButton = document.getElementById("openContactButton");

      if (successToast) {
        setTimeout(() => {
          successToast.classList.add("hidden");
        }, 5000);
      }

      if (errorToast) {
        setTimeout(() => {
          errorToast.classList.add("hidden");
        }, 5000);
      }

      // Setup contact button
      if (openContactButton) {
        openContactButton.addEventListener("click", function() {
          // Check if the openContactModal function exists before calling it
          if ((window as any).openContactModal && typeof (window as any).openContactModal === "function") {
            (window as any).openContactModal();
          } else {
            console.warn("openContactModal function is not available");
          }
        });
      }
    });
  </script>
</Layout>
