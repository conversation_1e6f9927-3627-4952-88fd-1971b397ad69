---
import Layout from "../layouts/Layout.astro";
import { SUBSCRIPTION_TIERS } from "../lib/subscriptionConfig";
import ContactForm from "../components/ContactForm.astro";

const faqs = {
  general: [
    {
      question: "What is <PERSON>rax<PERSON><PERSON>s?",
      answer:
        "PraxJobs is an platform that helps you create professional, ATS-friendly resumes. Our platform analyzes job descriptions and suggests improvements to make your resume more relevant and impactful for your target role.",
    },
    {
      question: "Is my data secure?",
      answer:
        "Yes, we take data security seriously. All your personal information is encrypted and stored securely and is deleted after 7 days. We never share your data with third parties.",
    },
    {
      question: "What's included in the free plan?",
      answer: `The free plan includes ${SUBSCRIPTION_TIERS.free.features.join(", ")}.`,
    },
  ],
  features: [
    {
      question: "How does the optimization work?",
      answer:
        "Our tool analyzes your resume against job descriptions, identifying key skills and experiences that match the requirements. It then suggests improvements to your content and formatting to increase your chances of getting noticed.",
    },
    {
      question: "What do I get with the Pro plan?",
      answer: `The Pro plan unlocks 300 uses of all features.`,
    },
    {
      question: "Does the tool help with cover letters?",
      answer:
        "Yes, we provide smart cover letter generation that matches your resume and the job requirements, helping you create compelling cover letters quickly.",
    },
  ],
  technical: [
    {
      question: "What file formats can I export my resume in?",
      answer:
        "Pro users can export their resumes in PDF, ensuring compatibility with various Applicant Tracking Systems (ATS).",
    },
    {
      question: "Can I import my existing resume?",
      answer:
        "Yes, you can import existing resumes in PDF or DOCX formats. We will analyze and convert them into our format while preserving your content.",
    },
    {
      question: "Is the platform mobile-friendly?",
      answer:
        "Yes, our platform is fully responsive and works on all devices, allowing you to create and edit your resume on desktop, tablet, or mobile.",
    },
  ],
};
---

<Layout
  title="Help & Support | PraxJobs FAQ and Resources"
  description="Find answers to frequently asked questions about PraxJobs, learn how to use our career tools, and get support for any issues you encounter."
  image="/images/help-og-image.jpg"
  type="website"
  canonical="/help"
  ogTitle="PraxJobs Help Center | FAQ and Support Resources"
  ogDescription="Get help with PraxJobs' career tools. Find answers to common questions about resume building, job research, and account management."
>
  <main class="min-h-screen">
    <!-- Hero Section -->
    <section
      class="py-16 bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-950"
    >
      <div class="max-w-5xl mx-auto px-6">
        <div class="text-center">
          <h1
            class="text-5xl font-bold text-gray-900 dark:text-white mb-4 mt-20"
          >
            How can we help?
          </h1>
          <p class="text-lg text-gray-500 dark:text-gray-300 mb-8">
            Find answers to common questions or reach out to our support team
          </p>
        </div>
      </div>
    </section>

    <!-- FAQ Sections -->
    <section class="py-16">
      <div class="max-w-5xl mx-auto px-6">
        <!-- General FAQs -->
        <div class="mb-16">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">
            General Questions
          </h2>
          <div class="space-y-6">
            {
              faqs.general.map((faq) => (
                <div class="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-sm">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {faq.question}
                  </h3>
                  <p class="text-gray-500 dark:text-gray-300">{faq.answer}</p>
                </div>
              ))
            }
          </div>
        </div>

        <!-- Feature FAQs -->
        <div class="mb-16">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">
            Features & Usage
          </h2>
          <div class="space-y-6">
            {
              faqs.features.map((faq) => (
                <div class="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-sm">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {faq.question}
                  </h3>
                  <p class="text-gray-500 dark:text-gray-300">{faq.answer}</p>
                </div>
              ))
            }
          </div>
        </div>

        <!-- Technical FAQs -->
        <div class="mb-16">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">
            Technical Questions
          </h2>
          <div class="space-y-6">
            {
              faqs.technical.map((faq) => (
                <div class="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-sm">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {faq.question}
                  </h3>
                  <p class="text-gray-500 dark:text-gray-300">{faq.answer}</p>
                </div>
              ))
            }
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Form Section -->
    <section id="contact" class="py-16 bg-gray-50 dark:bg-gray-950">
      <div class="max-w-3xl mx-auto px-6">
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Still need help?
          </h2>
          <p class="text-lg text-gray-500 dark:text-gray-300 mb-4">
            Send us a message and we'll get back to you as soon as possible
          </p>
        </div>

        <ContactForm
          title="Get in Touch"
          subtitle="We value your questions and feedback! Our team is ready to assist you with any inquiries you may have."
        />
      </div>
    </section>
  </main>
</Layout>

<script>
  // No contact form script needed as we're using Google Forms
</script>
