---
import Layout from "../layouts/Layout.astro";
import Container from "../components/Container.astro";
import LinkedInOptimiser from "../components/tools/LinkedInOptimiser.astro";
// Add hybrid rendering
export const prerender = false;
---

<Layout
  title="LinkedIn Optimisation | Get The Visibility You Deserve"
  description="Optimize your LinkedIn profile. Enhance your professional brand, increase visibility, and attract the right opportunities."
  image="/images/linkedin-optimisation-og-image.jpg"
  type="website"
  canonical="/linkedin-optimisation"
  ogTitle="LinkedIn Optimisation | Get The Visibility You Deserve | PraxJobs"
  ogDescription="Optimize your LinkedIn profile. Enhance your professional brand, increase visibility, and attract the right opportunities."
>
  <main class="relative min-h-screen dark:bg-gray-950 pt-16">
    <div
      aria-hidden="true"
      class="absolute inset-0 grid grid-cols-2 -space-x-52 opacity-40 dark:opacity-20"
    >
      <div
        class="blur-[106px] h-56 bg-gradient-to-br from-cyan-500 to-purple-300 dark:from-cyan-500 dark:to-purple-500"
      >
      </div>
      <div
        class="blur-[106px] h-56 bg-gradient-to-r from-cyan-500 to-purple-300 dark:from-cyan-500 dark:to-purple-500"
      >
      </div>
    </div>
    <Container>
      <div class="relative pt-36 space-y-20">
        <div class="w-full text-center mx-auto">
          <h1
            class="text-gray-900 dark:text-white font-bold text-4xl md:text-5xl lg:text-6xl xl:text-7xl"
          >
            <span class="text-primary dark:text-white"
              >Get The Visibility You Deserve</span
            >
          </h1>
          <p
            class="mt-8 text-gray-700 dark:text-gray-300 text-lg max-w-3xl mx-auto"
          >
            Transform your LinkedIn profile with smart suggestions personalised
            to your profile.
          </p>

          <!-- Stats -->
          <div
            class="hidden py-8 mt-16 border-y border-gray-100 dark:border-gray-800 sm:flex justify-between"
          >
            <div class="text-center">
              <h6 class="text-lg font-semibold text-gray-700 dark:text-white">
                Personalized Guidance
              </h6>
              <p class="mt-2 text-gray-500">Smart Rewrite Suggestions</p>
            </div>
            <div class="text-center">
              <h6 class="text-lg font-semibold text-gray-700 dark:text-white">
                Expert Insights
              </h6>
              <p class="mt-2 text-gray-500">Data-Driven Optimization Tips</p>
            </div>
            <div class="text-center">
              <h6 class="text-lg font-semibold text-gray-700 dark:text-white">
                Instant Results
              </h6>
              <p class="mt-2 text-gray-500">
                See your profile's strength in seconds.
              </p>
            </div>
          </div>
        </div>
      </div>
    </Container>

    <div class="relative pt-3 space-y-20">
      <div class="w-full text-center mx-auto">
        <LinkedInOptimiser />
      </div>
    </div>
    <Container>
      <section
        id="benefits"
        class="relative py-16 w-full overflow-hidden rounded-2xl"
      >
        <div class="container relative mx-auto p-4 sm:p-6 lg:p-8">
          <!-- Main Heading -->
          <div class="text-center mb-16">
            <h2
              class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4"
            >
              Maximize Your LinkedIn Impact
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-400">
              Transform your LinkedIn profile with tailored optimization and
              strategic insights.
            </p>
          </div>

          <!-- Feature Grid -->
          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
            role="list"
          >
            <!-- Practice Makes Perfect -->
            <div
              class="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              role="listitem"
            >
              <div
                class="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center mb-4"
              >
                <svg
                  class="w-6 h-6 text-blue-600 dark:text-blue-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
              </div>
              <h3
                class="text-xl font-semibold text-gray-900 dark:text-white mb-2"
              >
                Profile Enhancement
              </h3>
              <p class="text-gray-600 dark:text-gray-400">
                Refine your profile with suggestions to highlight your
                key skills and experiences.
              </p>
            </div>

            <!-- Personalized Feedback -->
            <div
              class="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              role="listitem"
            >
              <div
                class="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center mb-4"
              >
                <svg
                  class="w-6 h-6 text-green-600 dark:text-green-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <h3
                class="text-xl font-semibold text-gray-900 dark:text-white mb-2"
              >
                Data-Driven Insights
              </h3>
              <p class="text-gray-600 dark:text-gray-400">
                Receive actionable recommendations based on profile analysis and
                industry trends.
              </p>
            </div>

            <!-- Industry-Specific Questions -->
            <div
              class="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              role="listitem"
            >
              <div
                class="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center mb-4"
              >
                <svg
                  class="w-6 h-6 text-purple-600 dark:text-purple-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                  ></path>
                </svg>
              </div>
              <h3
                class="text-xl font-semibold text-gray-900 dark:text-white mb-2"
              >
                Targeted Optimization
              </h3>
              <p class="text-gray-600 dark:text-gray-400">
                Optimize your profile to align with specific industry keywords
                and recruiter preferences
              </p>
            </div>
          </div>

          <!-- Detailed Benefits Section -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Left Column: Preparation Strategy -->
            <div class="space-y-8">
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white">
                Complete Profile Optimization
              </h3>
              <p class="text-lg text-gray-600 dark:text-gray-400">
                We analyze your profile and resume to generate
                comprehensive optimization strategies for your summary,
                headline, and experience sections.
              </p>
              <div class="space-y-6">
                <div class="flex items-start space-x-4">
                  <div class="flex-shrink-0">
                    <div
                      class="h-8 w-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center"
                    >
                      <svg
                        class="w-4 h-4 text-blue-600 dark:text-blue-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h4
                      class="text-lg font-semibold text-gray-900 dark:text-white"
                    >
                      Keyword Optimization
                    </h4>
                    <p class="text-gray-600 dark:text-gray-400">
                      Enhance your profile with relevant keywords to improve
                      search visibility.
                    </p>
                  </div>
                </div>
                <div class="flex items-start space-x-4">
                  <div class="flex-shrink-0">
                    <div
                      class="h-8 w-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center"
                    >
                      <svg
                        class="w-4 h-4 text-green-600 dark:text-green-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h4
                      class="text-lg font-semibold text-gray-900 dark:text-white"
                    >
                      Network Growth
                    </h4>
                    <p class="text-gray-600 dark:text-gray-400">
                      Expand your professional network with targeted connection
                      suggestions.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Column: Success Metrics -->
            <div class="space-y-8">
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white">
                Proven Results
              </h3>
              <p class="text-lg text-gray-600 dark:text-gray-400">
                Users who optimize their LinkedIn profile see a
                significant increase in profile views and connection requests.
              </p>
              <div class="space-y-6">
                <div class="flex items-start space-x-4">
                  <div class="flex-shrink-0">
                    <div
                      class="h-8 w-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center"
                    >
                      <svg
                        class="w-4 h-4 text-purple-600 dark:text-purple-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                        ></path>
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h4
                      class="text-lg font-semibold text-gray-900 dark:text-white"
                    >
                      Confidence Boost
                    </h4>
                    <p class="text-gray-600 dark:text-gray-400">
                      90% of users report increased confidence in their LinkedIn
                      profile's effectiveness.
                    </p>
                  </div>
                </div>
                <div class="flex items-start space-x-4">
                  <div class="flex-shrink-0">
                    <div
                      class="h-8 w-8 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center"
                    >
                      <svg
                        class="w-4 h-4 text-yellow-600 dark:text-yellow-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        ></path>
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h4
                      class="text-lg font-semibold text-gray-900 dark:text-white"
                    >
                      Success Rate
                    </h4>
                    <p class="text-gray-600 dark:text-gray-400">
                      80% of users report increased recruiter outreach after
                      optimizing their profile.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Container>
  </main>

  <script>
    import { authService } from "../lib/auth";

    document.addEventListener("DOMContentLoaded", async () => {
      try {
        const user = await authService.getCurrentUser();
        // You can add user-specific functionality here if needed
      } catch (error) {
        console.error("Auth error:", error);
      }
    });
  </script>
</Layout>
