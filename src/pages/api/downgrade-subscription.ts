import type { APIRoute } from "astro";
import { getAuth } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";
import { initializeApp, getApps, cert } from "firebase-admin/app";
import { TierManagementService } from "../../lib/tierManagement";
import { RazorpayService } from "../../lib/razorpayService";

// Initialize Firebase Admin if not already initialized
if (!getApps().length) {
  initializeApp({
    credential: cert({
      projectId: import.meta.env.FIREBASE_PROJECT_ID,
      clientEmail: import.meta.env.FIREBASE_CLIENT_EMAIL,
      privateKey: import.meta.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, "\n"),
    }),
  });
}

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Get user from session/auth
    const authHeader = request.headers.get("authorization");
    if (!authHeader) return new Response("Unauthorized", { status: 401 });
    const idToken = authHeader.replace("Bearer ", "");
    const decodedToken = await getAuth().verifyIdToken(idToken);
    const userId = decodedToken.uid;

    // Get user's subscription info from Firestore
    const db = getFirestore();
    const userDoc = await db.collection("subscriptions").doc(userId).get();
    if (!userDoc.exists)
      return new Response("Subscription not found", { status: 404 });
    const userData = userDoc.data();
    const subscriptionId = userData?.razorpaySubscriptionId;
    if (!subscriptionId)
      return new Response("No active subscription", { status: 400 });

    // Cancel Razorpay subscription
    const razorpayService = new RazorpayService();
    await razorpayService.cancelSubscription(subscriptionId);
    // Downgrade user tier
    await TierManagementService.downgradeUserTier(userId);

    return new Response(JSON.stringify({ success: true }), { status: 200 });
  } catch (error) {
    console.error("Error in downgrade endpoint:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
};
