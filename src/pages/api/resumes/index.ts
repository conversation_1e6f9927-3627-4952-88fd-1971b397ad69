import type { APIRoute } from 'astro';
import { getFirestore } from 'firebase-admin/firestore';
import { verifyIdToken } from '../../../lib/firebase-admin';

// CREATE a new resume
export const POST: APIRoute = async ({ request }) => {
  const token = request.headers.get('Authorization')?.split(' ')[1];

  if (!token) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const decodedToken = await verifyIdToken(token);
    if (!decodedToken) {
      return new Response('Unauthorized', { status: 401 });
    }
    const userId = decodedToken.uid;
    const data = await request.json();

    const db = getFirestore();
    const resumeRef = await db.collection('resumes').add({
      ...data,
      userId,
      createdAt: new Date(),
      lastUpdated: new Date(),
    });

    return new Response(JSON.stringify({ id: resumeRef.id }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error creating resume:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
};

// GET all resumes for a user
export const GET: APIRoute = async ({ request }) => {
  const token = request.headers.get('Authorization')?.split(' ')[1];

  if (!token) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const decodedToken = await verifyIdToken(token);
    if (!decodedToken) {
      return new Response('Unauthorized', { status: 401 });
    }
    const userId = decodedToken.uid;

    const db = getFirestore();
    const resumesSnapshot = await db.collection('resumes').where('userId', '==', userId).get();
    
    const resumes = resumesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    return new Response(JSON.stringify(resumes), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error fetching resumes:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
};