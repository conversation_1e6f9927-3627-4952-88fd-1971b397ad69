import type { APIRoute } from 'astro';
import { getFirestore } from 'firebase-admin/firestore';
import { verifyIdToken } from '../../../lib/firebase-admin';

// GET a single resume
export const GET: APIRoute = async ({ params, request }) => {
  const { id } = params;
  if (!id) {
    return new Response('Bad Request: Missing ID', { status: 400 });
  }
  const token = request.headers.get('Authorization')?.split(' ')[1];

  if (!token) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const decodedToken = await verifyIdToken(token);
    if (!decodedToken) {
      return new Response('Unauthorized', { status: 401 });
    }
    const userId = decodedToken.uid;

    const db = getFirestore();
    const resumeRef = db.collection('resumes').doc(id);
    const resumeDoc = await resumeRef.get();

    const docData = resumeDoc.data();
    if (!resumeDoc.exists || (docData && docData.userId !== userId)) {
      return new Response('Resume not found', { status: 404 });
    }

    return new Response(JSON.stringify(docData), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error fetching resume:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
};

// UPDATE a resume
export const PUT: APIRoute = async ({ params, request }) => {
  const { id } = params;
  if (!id) {
    return new Response('Bad Request: Missing ID', { status: 400 });
  }
  const token = request.headers.get('Authorization')?.split(' ')[1];

  if (!token) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const decodedToken = await verifyIdToken(token);
    if (!decodedToken) {
      return new Response('Unauthorized', { status: 401 });
    }
    const userId = decodedToken.uid;
    const data = await request.json();

    const db = getFirestore();
    const resumeRef = db.collection('resumes').doc(id);
    const resumeDoc = await resumeRef.get();

    const docData = resumeDoc.data();
    if (!resumeDoc.exists || (docData && docData.userId !== userId)) {
      return new Response('Resume not found or unauthorized', { status: 404 });
    }

    await resumeRef.update({ ...data, lastUpdated: new Date() });

    return new Response(JSON.stringify({ id }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error updating resume:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
};

export const PATCH = PUT;

// DELETE a resume
export const DELETE: APIRoute = async ({ params, request }) => {
  const { id } = params;
  if (!id) {
    return new Response('Bad Request: Missing ID', { status: 400 });
  }
  const token = request.headers.get('Authorization')?.split(' ')[1];

  if (!token) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const decodedToken = await verifyIdToken(token);
    if (!decodedToken) {
      return new Response('Unauthorized', { status: 401 });
    }
    const userId = decodedToken.uid;

    const db = getFirestore();
    const resumeRef = db.collection('resumes').doc(id);
    const resumeDoc = await resumeRef.get();

    const docData = resumeDoc.data();
    if (!resumeDoc.exists || (docData && docData.userId !== userId)) {
      return new Response('Resume not found or unauthorized', { status: 404 });
    }

    await resumeRef.delete();

    return new Response(null, { status: 204 });
  } catch (error) {
    console.error('Error deleting resume:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
};