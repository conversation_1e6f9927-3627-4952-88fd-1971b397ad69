import type { APIRoute } from 'astro';
import { verifyIdToken } from '../../../lib/firebase-admin';
import { randomBytes } from 'crypto';

export const prerender = false;

export const GET: APIRoute = async () => {
    return new Response(JSON.stringify({
        message: 'Login route is available. Use POST method to authenticate.'
    }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
    });
};

export const POST: APIRoute = async ({ request, cookies }) => {
    try {
        // Parse the request body
        const body = await request.json();
        const { token } = body;

        if (!token) {
            return new Response(JSON.stringify({ error: 'No token provided' }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        // Verify the token
        const decodedToken = await verifyIdToken(token);
        if (!decodedToken) {
            return new Response(JSON.stringify({ error: 'Invalid token' }), {
                status: 401,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        // Generate CSRF token
        const csrfToken = randomBytes(16).toString('hex');

        cookies.set('auth_token', token, {
            path: '/',
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            httpOnly: true,
            expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
        });

        cookies.set('csrf_token', csrfToken, {
            path: '/',
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
        });

        return new Response(JSON.stringify({ success: true, csrfToken }), {
            status: 200,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    } catch (error) {
        console.error('Login API Error', {
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });

        return new Response(JSON.stringify({ error: 'Internal server error' }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
};
