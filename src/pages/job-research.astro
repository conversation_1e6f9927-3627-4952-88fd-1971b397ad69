---
import Layout from "../layouts/Layout.astro";
import Container from "../components/Container.astro";
import JobAnalysisComponent from "../components/tools/JobAnalysis.astro";
// Add hybrid rendering
export const prerender = false;
---

<Layout
  title="AI Job Research | Understand Job, Better"
  description="Research jobs with us to identify key requirements, skills, and qualifications. Get personalized insights to improve your application strategy."
  image="/images/job-analysis-og-image.jpg"
  type="website"
  canonical="/job-research"
  ogTitle="Job Research | PraxJobs"
  ogDescription="Decode jobs with our research tool. Identify key skills, requirements, and hidden expectations to tailor your application perfectly."
>
  <main class="relative min-h-screen dark:bg-gray-950 pt-16">
    <div
      aria-hidden="true"
      class="absolute inset-0 grid grid-cols-2 -space-x-52 opacity-40 dark:opacity-20"
    >
      <div
        class="blur-[106px] h-56 bg-gradient-to-br from-cyan-500 to-purple-300 dark:from-cyan-500 dark:to-purple-500"
      >
      </div>
      <div
        class="blur-[106px] h-56 bg-gradient-to-r from-cyan-500 to-purple-300 dark:from-cyan-500 dark:to-purple-500"
      >
      </div>
    </div>
    <Container>
      <div class="relative pt-36 space-y-20">
        <div class="w-full text-center mx-auto">
          <h1
            class="text-gray-900 dark:text-white font-bold text-4xl md:text-5xl lg:text-6xl xl:text-7xl"
          >
            <span class="text-primary dark:text-white"
              >Job Research</span
            >
          </h1>
          <p
            class="mt-8 text-gray-700 dark:text-gray-300 text-lg max-w-3xl mx-auto"
          >
            Research jobs to uncover essential skills, salary
            expectations, and growth opportunities. Get smart insights
            tailored to your career path.
          </p>

          <!-- Stats Section -->
          <div
            class="hidden py-8 mt-16 border-y border-gray-100 dark:border-gray-800 sm:flex justify-between"
          >
            <div class="text-center">
              <h6 class="text-lg font-semibold text-gray-700 dark:text-white">
                AI-Powered
              </h6>
              <p class="mt-2 text-gray-500">Insights powered by advanced AI.</p>
            </div>
            <div class="text-center">
              <h6 class="text-lg font-semibold text-gray-700 dark:text-white">
                Comprehensive
              </h6>
              <p class="mt-2 text-gray-500">In-depth research of companies.</p>
            </div>
            <div class="text-center">
              <h6 class="text-lg font-semibold text-gray-700 dark:text-white">
                Instant Results
              </h6>
              <p class="mt-2 text-gray-500">Get results in seconds</p>
            </div>
          </div>

          <!-- Analysis Section -->
          <section id="job-analysis" class="relative w-full py-16 sm:py-20">
            <div class="mx-auto">
              <JobAnalysisComponent />
            </div>
          </section>

          <!-- Benefit Section -->
          <section
            id="benefits"
            class="relative py-16 w-full overflow-hidden rounded-2xl"
          >
            <div class="container relative mx-auto p-4 sm:p-6 lg:p-8">
              <!-- Main Heading -->
              <div class="text-center mb-16">
                <h2
                  class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4"
                >
                  Your Career Advantage
                </h2>
                <p class="text-lg text-gray-600 dark:text-gray-400">
                  Transform any job description into actionable insights and
                  strategic career intelligence.
                </p>
              </div>

              <!-- Feature Grid -->
              <div
                class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
                role="list"
                aria-label="Key features of the Job Research tool"
              >
                <!-- Comprehensive Analysis Card -->
                <div
                  class="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                  role="listitem"
                >
                  <div
                    class="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center mb-4"
                  >
                    <svg
                      class="w-6 h-6 text-blue-600 dark:text-blue-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                      ></path>
                    </svg>
                  </div>
                  <h3
                    class="text-xl font-semibold text-gray-900 dark:text-white mb-2"
                  >
                    360° Job Intelligence
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    Get detailed insights about compensation, company culture,
                    growth opportunities, and market position from a single job
                    description.
                  </p>
                </div>

                <!-- Strategic Advantage Card -->
                <div
                  class="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                  role="listitem"
                >
                  <div
                    class="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center mb-4"
                  >
                    <svg
                      class="w-6 h-6 text-green-600 dark:text-green-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                  </div>
                  <h3
                    class="text-xl font-semibold text-gray-900 dark:text-white mb-2"
                  >
                    Negotiation Power
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    Make informed decisions with market-based salary data,
                    benefits comparisons, and industry compensation trends.
                  </p>
                </div>

                <!-- Interview Prep Card -->
                <div
                  class="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                  role="listitem"
                >
                  <div
                    class="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center mb-4"
                  >
                    <svg
                      class="w-6 h-6 text-purple-600 dark:text-purple-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      ></path>
                    </svg>
                  </div>
                  <h3
                    class="text-xl font-semibold text-gray-900 dark:text-white mb-2"
                  >
                    Interview Excellence
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400">
                    Get tailored interview preparation with common questions,
                    technical assessment insights, and company-specific tips.
                  </p>
                </div>
              </div>

              <!-- Detailed Benefits Section -->
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Left Column: Career Strategy -->
                <div class="space-y-8">
                  <h3 class="text-2xl font-bold text-gray-900 dark:text-white">
                    Strategic Career Planning
                  </h3>
                  <div class="space-y-6">
                    <!-- Market Position -->
                    <div class="flex items-start space-x-4">
                      <div class="flex-shrink-0 mt-1">
                        <div
                          class="h-6 w-6 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center"
                        >
                          <svg
                            class="w-4 h-4 text-indigo-600 dark:text-indigo-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                          </svg>
                        </div>
                      </div>
                      <div>
                        <h4
                          class="text-lg font-semibold text-gray-900 dark:text-white"
                        >
                          Market Position Analysis
                        </h4>
                        <p class="text-gray-600 dark:text-gray-400">
                          Understand where the role fits in the industry
                          landscape and how it aligns with your career
                          trajectory.
                        </p>
                      </div>
                    </div>
                    <!-- Growth Opportunities -->
                    <div class="flex items-start space-x-4">
                      <div class="flex-shrink-0 mt-1">
                        <div
                          class="h-6 w-6 bg-rose-100 dark:bg-rose-900 rounded-full flex items-center justify-center"
                        >
                          <svg
                            class="w-4 h-4 text-rose-600 dark:text-rose-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                          </svg>
                        </div>
                      </div>
                      <div>
                        <h4
                          class="text-lg font-semibold text-gray-900 dark:text-white"
                        >
                          Growth Trajectory
                        </h4>
                        <p class="text-gray-600 dark:text-gray-400">
                          Map out potential career paths and identify skill
                          development opportunities for long-term success.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Right Column: Application Success -->
                <div class="space-y-8">
                  <h3 class="text-2xl font-bold text-gray-900 dark:text-white">
                    Application Success Blueprint
                  </h3>
                  <div class="space-y-6">
                    <!-- Resume Optimization -->
                    <div class="flex items-start space-x-4">
                      <div class="flex-shrink-0 mt-1">
                        <div
                          class="h-6 w-6 bg-amber-100 dark:bg-amber-900 rounded-full flex items-center justify-center"
                        >
                          <svg
                            class="w-4 h-4 text-amber-600 dark:text-amber-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                            ></path>
                          </svg>
                        </div>
                      </div>
                      <div>
                        <h4
                          class="text-lg font-semibold text-gray-900 dark:text-white"
                        >
                          Resume Optimization
                        </h4>
                        <p class="text-gray-600 dark:text-gray-400">
                          Get tailored advice on highlighting relevant skills
                          and experiences that match the job requirements.
                        </p>
                      </div>
                    </div>
                    <!-- Interview Strategy -->
                    <div class="flex items-start space-x-4">
                      <div class="flex-shrink-0 mt-1">
                        <div
                          class="h-6 w-6 bg-emerald-100 dark:bg-emerald-900 rounded-full flex items-center justify-center"
                        >
                          <svg
                            class="w-4 h-4 text-emerald-600 dark:text-emerald-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                            ></path>
                          </svg>
                        </div>
                      </div>
                      <div>
                        <h4
                          class="text-lg font-semibold text-gray-900 dark:text-white"
                        >
                          Interview Strategy
                        </h4>
                        <p class="text-gray-600 dark:text-gray-400">
                          Prepare with company-specific interview questions and
                          understand the evaluation process.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </Container>
  </main>
</Layout>

<script>
  import { authService } from "../lib/auth";

  // Client-side authentication check
  async function checkAuthentication() {
    try {
      const user = await authService.getCurrentUser();

      // If no user is found, redirect to login
      if (!user) {
        window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
      }
      // If user is found, do nothing (page access is allowed)
    } catch (error) {
      // Redirect to login even if there's an error during the check
      window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
    }
  }

  // Run authentication check when the page loads
  document.addEventListener("DOMContentLoaded", checkAuthentication);
</script>
