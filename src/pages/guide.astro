---
import Layout from "../layouts/Layout.astro";
import Container from "../components/Container.astro";
import GuideMode from "../components/tools/GuideMode.astro";
// Add hybrid rendering
export const prerender = false;

// Get URL parameters
const url = new URL(Astro.request.url);
const initialQuery = url.searchParams.get("query") || "";
const lastQ = url.searchParams.get("lastQ") || "";
const lastA = url.searchParams.get("lastA") || "";

// Define prompt suggestions
const promptSuggestions = [
  "What are some good career paths for someone with my skills?",
  "How can I improve my networking skills?",
  "What are the latest trends in the job market?",
  "Can you help me identify my strengths and weaknesses?",
  "What are some strategies for dealing with workplace stress?",
];
---

<Layout
  title="AI Career Advisor | Get Personalized Career Guidance"
  description="Get personalized career advice and guidance with our career advisor. Discuss your career aspirations, skills, and concerns and receive actionable suggestions and resources."
  image="/images/og-image.jpg"
  type="website"
  canonical="/guide"
  ogTitle="AI Career Advisor | Personalized Career Guidance"
  ogDescription="Discuss your career and get personalized advice with our advisor. Receive actionable suggestions and resources to help you achieve your career goals."
>
  {/* Make main a flex column that takes at least screen height */}
  <main class="relative min-h-screen dark:bg-gray-950 pt-16">
    {/* Background Gradient Effect */}
    <div
      aria-hidden="true"
      class="absolute inset-0 grid grid-cols-2 -space-x-52 opacity-40 dark:opacity-20 pointer-events-none"
    >
      <div
        class="blur-[106px] h-56 bg-gradient-to-br from-primary to-purple-400 dark:from-blue-700"
      >
      </div>
      <div
        class="blur-[106px] h-32 bg-gradient-to-r from-cyan-400 to-sky-300 dark:to-indigo-600"
      >
      </div>
    </div>

    <Container>
      <div class="relative pt-36 space-y-20">
        <div class="w-full text-center mx-auto">
          <h1
            class="text-gray-900 dark:text-white font-bold text-4xl md:text-5xl lg:text-6xl xl:text-7xl"
          >
            <span class="text-primary dark:text-white">AI Career Advisor</span>
          </h1>
          <p
            class="mt-8 text-gray-700 dark:text-gray-300 text-lg max-w-3xl mx-auto"
          >
            Discuss your career goals and challenges with our AI career advisor.
            Get personalized guidance, actionable suggestions, and relevant
            resources to help you succeed.
          </p>

          {/* Stats */}
          <div
            class="hidden py-8 mt-16 border-y border-gray-100 dark:border-gray-800 sm:flex justify-between"
          >
            <div class="text-left">
              <h6 class="text-lg font-semibold text-gray-700 dark:text-white">
                Personalized Advice
              </h6>
              <p class="mt-2 text-gray-500">Tailored to your needs</p>
            </div>
            <div class="text-left">
              <h6 class="text-lg font-semibold text-gray-700 dark:text-white">
                Actionable Insights
              </h6>
              <p class="mt-2 text-gray-500">
                Specific suggestions and resources
              </p>
            </div>
            <div class="text-left">
              <h6 class="text-lg font-semibold text-gray-700 dark:text-white">
                Conversational Guidance
              </h6>
              <p class="mt-2 text-gray-500">Engage in a helpful dialogue</p>
            </div>
          </div>
        </div>
      </div>
    </Container>

    {/* Guide Mode Section */}
    <div class="relative pt-3 space-y-20">
      <div class="w-full text-center mx-auto">
        <GuideMode
          initialQuery={initialQuery}
          lastQ={lastQ}
          lastA={lastA}
          promptSuggestions={promptSuggestions}
        />
      </div>
    </div>

    {/* Benefits Section */}
    <Container>
      <section
        id="benefits"
        class="relative py-16 w-full overflow-hidden rounded-2xl"
      >
        <div class="container relative mx-auto p-4 sm:p-6 lg:p-8">
          {/* Main Heading */}
          <div class="text-center mb-16">
            <h2
              class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4"
            >
              Advance Your Career With Expert Guidance
            </h2> 
            <p class="text-lg text-gray-600 dark:text-gray-400">
              Get personalized career advice and actionable insights to help you achieve your professional goals
            </p>
          </div>

          {/* Feature Grid */}
          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
            role="list"
          >
            {/* Personalized Guidance */}
            <div
              class="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              role="listitem"
            >
              <div
                class="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center mb-4"
              >
                <svg
                  class="w-6 h-6 text-blue-600 dark:text-blue-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                  ></path>
                </svg>
              </div>
              <h3
                class="text-xl font-semibold text-gray-900 dark:text-white mb-2"
              >
                Personalized Guidance
              </h3>
              <p class="text-gray-600 dark:text-gray-400">
                Receive advice tailored to your unique skills, experience, and career aspirations
              </p>
            </div>

            {/* Career Strategy */}
            <div
              class="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              role="listitem"
            >
              <div
                class="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center mb-4"
              >
                <svg
                  class="w-6 h-6 text-green-600 dark:text-green-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  ></path>
                </svg>
              </div>
              <h3
                class="text-xl font-semibold text-gray-900 dark:text-white mb-2"
              >
                Strategic Career Planning
              </h3>
              <p class="text-gray-600 dark:text-gray-400">
                Develop effective strategies for career advancement, job transitions, and professional growth
              </p>
            </div>

            {/* Real-time Answers */}
            <div
              class="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              role="listitem"
            >
              <div
                class="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center mb-4"
              >
                <svg
                  class="w-6 h-6 text-purple-600 dark:text-purple-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
              </div>
              <h3
                class="text-xl font-semibold text-gray-900 dark:text-white mb-2"
              >
                Real-time Answers
              </h3>
              <p class="text-gray-600 dark:text-gray-400">
                Get immediate responses to your career questions and concerns from our advisors.
              </p>
            </div>
          </div>
        </div>
      </section>
    </Container>
  </main>
</Layout>

<script>
  import { authService } from "../lib/auth";

  // Client-side authentication check with 2-second delay
  function checkAuthentication() {
    // Delay the authentication check by 2 seconds
    setTimeout(async () => {
      try {
        const user = await authService.getCurrentUser();

        // If no user is found after 2 seconds, redirect to login
        if (!user) {
          window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname + window.location.search)}`;
        }
        // If user is found, do nothing (page access is allowed)
      } catch (error) {
        // Redirect to login even if there's an error during the check
        window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname + window.location.search)}`;
      }
    }, 2000); // 2-second delay
  }

  // Run authentication check when the page loads
  document.addEventListener("DOMContentLoaded", checkAuthentication);
</script>

