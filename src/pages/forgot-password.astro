---
import Layout from "../layouts/Layout.astro";
---

<Layout 
  title="Reset Your PraxJobs Password | Account Recovery"
  description="Reset your PraxJobs password securely. Enter your email to receive a password reset link and regain access to your account."
  image="/images/reset-password-og-image.jpg"
  type="website"
  canonical="/forgot-password"
  ogTitle="Reset Your PraxJobs Password | Secure Account Recovery"
>
  <main class="relative min-h-screen bg-gray-50 dark:bg-gray-950 pt-16">
    <section
      class="min-h-screen flex items-center justify-center px-4 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 transition-all duration-500"
    >
      <div
        class="max-w-md w-full bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-2xl shadow-xl p-8 space-y-8 transform transition-all duration-300 border border-gray-200/50 dark:border-gray-700/50"
      >
        <div class="space-y-4 text-center">
          <img
            src="/logo.svg"
            alt="PraxJobs Logo"
            class="h-16 mx-auto mb-4 transform hover:scale-105 transition-transform duration-300 filter dark:invert"
          />
          <h1
            class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-gray-700 to-gray-900 dark:from-white dark:via-gray-200 dark:to-white bg-clip-text text-transparent animate-gradient-x"
          >
            Reset Password
          </h1>
          <p class="text-gray-600 dark:text-gray-300">
            Enter your email to receive a reset link
          </p>
        </div>

        <form id="forgot-password-form" class="space-y-4">
          <div class="space-y-2">
            <input
              id="email"
              name="email"
              type="email"
              required
              placeholder="Enter your email"
              class="w-full px-4 py-3 border-2 rounded-xl dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 focus:border-primary dark:focus:border-primary outline-none transition-colors duration-200"
            />
          </div>
          <div id="resetErrorContainer" class="text-red-500 text-sm hidden">
          </div>
          <button
            type="submit"
            class="w-full bg-black text-white dark:bg-white dark:text-black py-3 rounded-xl hover:bg-primary-dark transition-colors duration-200"
          >
            Send Reset Link
          </button>
        </form>

        <div class="text-center text-sm">
          <span class="text-gray-600 dark:text-gray-400"
            >Remember your password?</span
          >
          <a href="/login" class="text-primary hover:underline ml-1">Sign in</a>
        </div>
      </div>
    </section>
  </main>
</Layout>

<script>
  import { authService } from "../lib/auth";

  const forgotPasswordForm = document.getElementById("forgot-password-form");
  const errorContainer = document.getElementById("resetErrorContainer");

  forgotPasswordForm?.addEventListener("submit", async (e) => {
    e.preventDefault();

    const emailInput = document.getElementById("email") as HTMLInputElement;
    if (!emailInput) {
      return;
    }

    try {
      // Show loading state
      const submitButton = forgotPasswordForm.querySelector(
        'button[type="submit"]'
      );
      if (submitButton) {
        submitButton.innerHTML = "Sending reset link...";
        (submitButton as HTMLButtonElement).disabled = true;
      }

      // Clear any previous errors
      if (errorContainer) {
        errorContainer.textContent = "";
        errorContainer.classList.add("hidden");
      }

      await authService.resetPassword(emailInput.value);

      // Show success message in the error container but with green color
      if (errorContainer) {
        errorContainer.textContent =
          "Password reset link sent! Check your email.";
        errorContainer.classList.remove("text-red-500", "hidden");
        errorContainer.classList.add("text-green-500");
      }

      // Clear the input
      emailInput.value = "";
    } catch (error) {
      // Show error message
      if (errorContainer) {
        if (error instanceof Error) {
          const errorMessage = error.message.toLowerCase();
          if (errorMessage.includes("user-not-found")) {
            errorContainer.textContent =
              "No account found with this email address.";
          } else if (errorMessage.includes("too-many-requests")) {
            errorContainer.textContent =
              "Too many reset attempts. Please try again later.";
          } else {
            errorContainer.textContent =
              "Failed to send password reset link. Please try again.";
          }
        } else {
          errorContainer.textContent =
            "Password reset failed. Please try again.";
        }
        errorContainer.classList.remove("hidden", "text-green-500");
        errorContainer.classList.add("text-red-500");
      }
    } finally {
      // Reset button state
      const submitButton = forgotPasswordForm.querySelector(
        'button[type="submit"]'
      );
      if (submitButton) {
        submitButton.innerHTML = "Send Reset Link";
        (submitButton as HTMLButtonElement).disabled = false;
      }
    }
  });
</script>
