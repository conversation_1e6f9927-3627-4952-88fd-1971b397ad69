---
import Layout from "../layouts/Layout.astro";
---

<Layout 
    title="Complete Sign In | PraxJobs"
    description="Complete the sign-in process to access your PraxJobs account."
>
    <main class="relative min-h-screen bg-gray-50 dark:bg-gray-950 pt-16">
        <section
            class="min-h-screen flex items-center justify-center px-4 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 transition-all duration-500"
        >
            <div id="finish-login-container" class="max-w-md w-full bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-2xl shadow-xl p-8 space-y-8 transform transition-all duration-300 border border-gray-200/50 dark:border-gray-700/50 mx-auto text-center">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-gray-700 to-gray-900 dark:from-white dark:via-gray-200 dark:to-white bg-clip-text text-transparent animate-gradient-x">
                    Complete Sign In
                </h1>
                <p id="message" class="text-gray-600 dark:text-gray-300">
                    Please wait while we verify your sign-in link...
                </p>
                <form id="password-form" class="hidden space-y-4">
                    <input type="password" id="password" placeholder="Enter your password" class="w-full px-4 py-3 border-2 rounded-xl dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 focus:border-primary dark:focus:border-primary outline-none transition-colors duration-200" required />
                    <input type="password" id="confirm-password" placeholder="Confirm your password" class="w-full px-4 py-3 border-2 rounded-xl dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 focus:border-primary dark:focus:border-primary outline-none transition-colors duration-200" required />
                    <button type="submit" class="w-full bg-gray-800 dark:bg-gray-100 dark:hover:bg-white text-white dark:text-gray-900 py-3 rounded-xl hover:bg-black">Set Password & Sign In</button>
                </form>
                <div id="error-container" class="text-red-500 text-sm hidden"></div>
            </div>
        </section>
    </main>
</Layout>

<script>
    import { authService } from '../lib/auth';
    import { isSignInWithEmailLink, signInWithEmailLink, getAdditionalUserInfo } from "firebase/auth";

    const message = document.getElementById('message');
    const passwordForm = document.getElementById('password-form') as HTMLFormElement;
    const errorContainer = document.getElementById('error-container');

    async function handleSignIn() {
        if (isSignInWithEmailLink(authService.auth, window.location.href)) {
            let email = window.localStorage.getItem('emailForSignIn');
            if (!email) {
                email = window.prompt('Please provide your email for confirmation');
            }

            if (email) {
                try {
                    const userCredential = await signInWithEmailLink(authService.auth, email, window.location.href);
                    window.localStorage.removeItem('emailForSignIn');
                    
                    const token = await userCredential.user.getIdToken();
                    await authService.setSessionCookie(token);

                    const additionalUserInfo = getAdditionalUserInfo(userCredential);

                    if (additionalUserInfo?.isNewUser) {
                        if (message) message.textContent = 'Welcome! Please set a password to secure your account.';
                        if (passwordForm) passwordForm.classList.remove('hidden');
                    } else {
                        window.location.href = '/dashboard';
                    }
                } catch (error) {
                    if (message) message.textContent = 'Failed to sign in. The link may be invalid or expired.';
                    if (errorContainer && error instanceof Error) errorContainer.textContent = error.message;
                    if (errorContainer) errorContainer.classList.remove('hidden');
                }
            }
        }
    }

    if (passwordForm) {
        passwordForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const password = (document.getElementById('password') as HTMLInputElement).value;
            const confirmPassword = (document.getElementById('confirm-password') as HTMLInputElement).value;

            if (password !== confirmPassword) {
                if (errorContainer) {
                    errorContainer.textContent = 'Passwords do not match.';
                    errorContainer.classList.remove('hidden');
                }
                return;
            }

            try {
                await authService.updatePassword(password);
                const user = authService.auth.currentUser;
                if (user) {
                    const token = await user.getIdToken();
                    await authService.setSessionCookie(token);
                }
                window.location.href = '/dashboard';
            } catch (error) {
                if (errorContainer && error instanceof Error) errorContainer.textContent = error.message;
                if (errorContainer) errorContainer.classList.remove('hidden');
            }
        });
    }

    handleSignIn();
</script>