---
import Layout from "../layouts/Layout.astro";
import Container from "../components/Container.astro";
---

<Layout
  title="Account Settings | Manage Your PraxJobs Profile"
  description="Manage your PraxJobs account settings, update your profile information, and customize your preferences for a personalized experience."
  image="/images/settings-og-image.jpg"
  type="website"
  canonical="/settings"
>
  <main class="relative min-h-screen dark:bg-gray-950 pt-16">
    <section class="relative pt-24 pb-12">
      <Container>
        <div class="relative max-w-full">
          <div
            class="flex flex-col md:flex-row items-center justify-between gap-8"
          >
            <div class="md:w-1/2 space-y-6">
              <h1
                class="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight"
              >
                <span class="block text-gray-900 dark:text-white font-extrabold"
                  >My Account</span
                >
              </h1>
              <p class="text-lg text-gray-600 dark:text-gray-300">
                Manage your profile, subscription, and account preferences with
                ease.
              </p>
            </div>
          </div>
        </div>
      </Container>
    </section>

    <section class="py-8">
      <Container>
        <div
          class="flex flex-col bg-white dark:bg-gray-900 shadow-2xl rounded-3xl overflow-hidden p-6 md:p-8"
        >
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
              Account Details
            </h2>
            <div class="flex space-x-4">
              <button
                id="changePasswordButton"
                class="bg-black text-white dark:bg-white dark:text-black px-4 py-2 rounded-full transition-colors"
              >
                Change Password
              </button>
            </div>
          </div>
          <div class="space-y-4 md:ml-6 md:mb-4">
            <div>
              <label
                class="block text-lg font-semibold text-gray-900 dark:text-gray-300"
                >Name</label
              >
              <p
                id="user-name"
                class="text-lg text-gray-900 dark:text-white text-gray-400 animate-pulse"
              >
                Loading...
              </p>
            </div>
            <div>
              <label
                class="block text-lg font-semibold text-gray-900 dark:text-gray-300"
                >Email</label
              >
              <p
                id="user-email"
                class="text-lg text-gray-900 dark:text-white text-gray-400 animate-pulse"
              >
                Loading...
              </p>
            </div>
          </div>

              <!-- Subscription Details Section -->
              <div class="bg-gray-50 dark:bg-gray-800 p-6 md:my-6 rounded-xl">
                <h3
                  class="text-lg font-semibold text-gray-900 dark:text-white mb-4"
                >
                  Subscription Details
                </h3>
                <div class="space-y-4">
                  <div class="grid grid-cols-2 gap-y-2">
                    <label class="text-base font-medium text-gray-700 dark:text-gray-300">Current Tier</label>
                    <p
                      id="current-tier"
                      class="text-lg text-gray-900 dark:text-white font-semibold text-gray-400 animate-pulse text-right"
                    >
                      Loading...
                    </p>
                  </div>
                  <div id="expiration-container" class="hidden grid grid-cols-2 gap-y-2">
                    <label class="text-base font-medium text-gray-700 dark:text-gray-300">Expiration</label>
                    <p
                      id="subscription-expiration"
                      class="text-lg text-gray-900 dark:text-white text-gray-400 animate-pulse text-right"
                    >
                      Loading...
                    </p>
                    <label class="text-base font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                    <p id="subscription-start-date" class="text-lg text-gray-900 dark:text-white text-gray-400 animate-pulse text-right">Loading...</p>
                  </div>
                </div>
              </div>

              <!-- Usage Limits Section -->
              <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl">
                <h3
                  class="text-lg font-semibold text-gray-900 dark:text-white mb-4"
                >
                  Usage Limits
                </h3>
                <div class="space-y-4">
                  <div id="feature-limits" class="space-y-3">
                    <!-- Feature limits will be dynamically populated here -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </section>
  </main>

  <script>
    import { authService } from "../lib/auth";
    import { getFirestore, doc, getDoc } from "firebase/firestore";
    import { SUBSCRIPTION_TIERS } from "../lib/subscriptionConfig";


    async function fetchUserData() {
      try {
        const user = await authService.getCurrentUser();

        if (user) {
          // Update user details
          const nameElement = document.getElementById("user-name");
          const emailElement = document.getElementById("user-email");
          const currentTierElement = document.getElementById("current-tier");
          const subscriptionExpirationElement = document.getElementById(
            "subscription-expiration"
          );
          const upgradeButton = document.getElementById("upgrade-button");

          // Restore original styling and populate data
          if (nameElement) {
            nameElement.textContent =
              user.displayName || user.email || "Not available";
            nameElement.classList.remove("text-gray-400", "animate-pulse");
          }

          if (emailElement) {
            emailElement.textContent = user.email || "Not available";
            emailElement.classList.remove("text-gray-400", "animate-pulse");
          }

          // Fetch subscription details
          const db = getFirestore();
          const docRef = doc(db, "userSubscriptions", user.uid);
          const docSnap = await getDoc(docRef);

          if (docSnap.exists()) {
            const expirationContainer = document.getElementById(
              "expiration-container"
            );
            if (expirationContainer) {
              expirationContainer.classList.remove("hidden");
            }
            const subscriptionStatusElement = document.getElementById(
              "subscription-status"
            );
            const subscriptionStartDateElement = document.getElementById(
              "subscription-start-date"
            );
            const subscriptionIdElement =
              document.getElementById("subscription-id");
            const userSubscription = docSnap.data();

            const paymentStatus = userSubscription.paymentStatus || "unknown";
            const subscriptionId =
              userSubscription.razorpaySubscriptionId || "N/A";

            let formattedStartDate = "Not available";
            if (userSubscription.subscriptionStartDate) {
              const startDate =
                userSubscription.subscriptionStartDate instanceof Date
                  ? userSubscription.subscriptionStartDate
                  : userSubscription.subscriptionStartDate.toDate();
              formattedStartDate = new Intl.DateTimeFormat("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
              }).format(startDate);
            }

            if (subscriptionStatusElement) {
              subscriptionStatusElement.textContent = paymentStatus.charAt(0).toUpperCase() + paymentStatus.slice(1);
              subscriptionStatusElement.classList.remove("text-gray-400", "animate-pulse");
            }
            if (subscriptionStartDateElement) {
              subscriptionStartDateElement.textContent = formattedStartDate;
              subscriptionStartDateElement.classList.remove("text-gray-400", "animate-pulse");
            }
            if (subscriptionIdElement) {
              subscriptionIdElement.textContent = subscriptionId;
              subscriptionIdElement.classList.remove("text-gray-400", "animate-pulse");
            }
            const currentTier = userSubscription.currentTier || "free";

            // Format subscription expiration date
            let formattedExpirationDate = "Not available";
            let isExpired = false;

            // Get tier configuration
            const tierConfig = SUBSCRIPTION_TIERS[currentTier];

            // Determine expiration based on tier and billing cycle
            if (currentTier === "free") {
              formattedExpirationDate = "Free Tier (No Expiration)";
              // Hide expiration container for free tier
              const expirationContainer = document.getElementById(
                "expiration-container"
              );
              if (expirationContainer) {
                expirationContainer.classList.add("hidden");
              }
            } else if (currentTier === "pro" || currentTier === "pro_quarterly") {
              // Show expiration container for pro tier
              const expirationContainer = document.getElementById(
                "expiration-container"
              );
              if (expirationContainer) {
                expirationContainer.classList.remove("hidden");
              }

              // For pro tier, calculate expiration based on billing cycle
              const startDate = userSubscription.subscriptionStartDate
                ? userSubscription.subscriptionStartDate instanceof Date
                  ? userSubscription.subscriptionStartDate
                  : userSubscription.subscriptionStartDate.toDate()
                : new Date();

              const expirationDate = new Date(startDate);

              // Add billing cycle duration based on actual configuration
              if (tierConfig.billingCycle === "monthly") {
                expirationDate.setMonth(expirationDate.getMonth() + 1);
              } else if (tierConfig.billingCycle === "yearly") {
                expirationDate.setFullYear(expirationDate.getFullYear() + 1);
              } else if (tierConfig.billingCycle === "quarterly") {
                expirationDate.setMonth(expirationDate.getMonth() + 3); // Add 3 months for quarterly
              } else {
                // Default to monthly if no billing cycle specified
                expirationDate.setMonth(expirationDate.getMonth() + 1);
              }

              // Format the expiration date
              formattedExpirationDate = new Intl.DateTimeFormat("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
              }).format(expirationDate);

              // Check if expired
              const currentDate = new Date();
              isExpired = expirationDate < currentDate;

              if (isExpired) {
                formattedExpirationDate += " (Expired)";
              }
            }

            if (currentTierElement) {
              // Create badge based on tier
              const tierBadgeClasses = {
                free: "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",
                pro: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
                pro_quarterly: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
              };

              // Create badge HTML
              const displayTier =
                currentTier === "free" ? "starter" : currentTier.replace('_', ' ');
              const badgeHtml = `
                                <span class="px-2 py-2 rounded-full text-md font-semibold ${tierBadgeClasses[currentTier] || ""}">
                                    ${displayTier.charAt(0).toUpperCase() + displayTier.slice(1)}
                                </span>
                            `;

              // Set content with badge
              currentTierElement.innerHTML = `${badgeHtml}`;
              currentTierElement.classList.remove(
                "text-gray-400",
                "animate-pulse"
              );
            }

            if (subscriptionExpirationElement) {
              subscriptionExpirationElement.textContent =
                formattedExpirationDate;
              subscriptionExpirationElement.classList.remove(
                "text-gray-400",
                "animate-pulse"
              );

              // Add visual indication for expired subscriptions
              if (isExpired) {
                subscriptionExpirationElement.classList.add("text-red-500");
              }
            }

            if (upgradeButton) {
              // Hide upgrade button for Pro tier users
              if (currentTier === "pro" || currentTier === "pro_quarterly") {
                upgradeButton.style.display = "none";
              } else {
                upgradeButton.style.display = "block";
                upgradeButton.textContent = "Upgrade to Pro";
              }
            }

            // Feature limit display logic
            const featureLimitsElement =
              document.getElementById("feature-limits");

            if (docSnap.exists() && featureLimitsElement) {
              const userSubscription = docSnap.data();
              const currentTier = userSubscription.currentTier || "free";
              const tierConfig = SUBSCRIPTION_TIERS[currentTier];

              // Define features to track
              const features = [
                "resumeGeneration",
                "coverLetterGeneration",
                "jobAnalysis",
                "interviewPrep",
                "jobApplications",
                "linkedinOptimization",
              ];

              // Clear previous content
              featureLimitsElement.innerHTML = "";

              // Create feature limit display
              features.forEach((feature) => {
                // Get current usage and limit
                const usageCount =
                  userSubscription.featureUsage[feature]?.usageCount || 0;
                const limit = tierConfig.featureUsageLimits[feature];

                // Determine display details
                const isUnlimited = limit === Infinity || limit > 10000;
                const progressPercentage = isUnlimited
                  ? 100
                  : Math.min((usageCount / limit) * 100, 100);

                // Feature name mapping
                const featureNames = {
                  resumeGeneration: "Resumes",
                  coverLetterGeneration: "Cover Letters",
                  jobAnalysis: "Job Research",
                  interviewPrep: "Interview Preparation",
                  jobApplications: "Job Applications Tracked",
                  linkedinOptimization: "LinkedIn Optimizations",
                };

                // Create feature limit element
                const featureElement = document.createElement("div");
                featureElement.classList.add(
                  "bg-gray-100",
                  "dark:bg-gray-800",
                  "p-3",
                  "rounded-lg"
                );

                // Paid tiers: Unlimited features
                if (currentTier !== "free") {
                  featureElement.innerHTML = `
                                        <div class="flex justify-between items-center">
                                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                                                ${featureNames[feature]}
                                            </span>
                                            <span class="text-sm font-semibold text-green-600 dark:text-green-400">
                                                300
                                            </span>
                                        </div>
                                    `;
                }
                // Free tier: Show limits and upgrade message
                else {
                  featureElement.innerHTML = `
                                        <div class="flex justify-between items-center mb-2">
                                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                                                ${featureNames[feature]}
                                            </span>
                                            <span class="text-sm font-semibold text-gray-600 dark:text-gray-300">
                                                ${usageCount}/${limit}
                                            </span>
                                        </div>
                                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                            <div class="bg-blue-600 h-2.5 rounded-full" style="width: ${progressPercentage}%"></div>
                                        </div>
                                        ${
                                          usageCount >= limit && limit !== Infinity
                                            ? `
                                            <div class="mt-2 text-xs text-red-600 dark:text-red-400">
                                                Limit reached. Upgrade to Pro to continue.
                                            </div>
                                        `
                                            : ""
                                        }
                                    `;
                }

                featureLimitsElement.appendChild(featureElement);
              });

              // Add upgrade message for free tier
              if (currentTier === "free") {
                const upgradeMessageElement = document.createElement("div");
                upgradeMessageElement.classList.add(
                  "mt-4",
                  "p-3",
                  "bg-blue-50",
                  "dark:bg-blue-900",
                  "border",
                  "border-blue-200",
                  "dark:border-blue-700",
                  "rounded-lg",
                  "text-center"
                );
                upgradeMessageElement.innerHTML = `
                                    <p class="text-sm text-blue-800 dark:text-blue-200">
                                        Upgrade to Pro for 300 uses of all features!
                                    </p>
                                    <button id="upgrade-now-btn" class="mt-2 w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 transition">
                                        Upgrade Now
                                    </button>
                                `;

                // Add click event to upgrade button
                upgradeMessageElement
                  .querySelector("#upgrade-now-btn")
                  ?.addEventListener("click", () => {
                    window.location.href = "/pricing";
                  });

                featureLimitsElement.appendChild(upgradeMessageElement);
              }

              // Add downgrade button for pro tier
              if (currentTier === "pro" || currentTier === "pro_quarterly") {
                const downgradeMessageElement = document.createElement("div");
                downgradeMessageElement.classList.add(
                  "mt-4",
                  "p-3",
                  "bg-red-50",
                  "dark:bg-red-900",
                  "border",
                  "border-red-200",
                  "dark:border-red-700",
                  "rounded-lg",
                  "text-center"
                );
                downgradeMessageElement.innerHTML = `
                                    <p class="text-sm text-red-800 dark:text-red-200">
                                        Want to stop your subscription? Downgrade to Free to lose premium access.
                                    </p>
                                     <button id="downgrade-now-btn" class="mt-2 w-full bg-red-600 text-white py-2 rounded-md hover:bg-red-700 transition">
                                         Downgrade to Free
                                     </button>
                                     <!-- Downgrade Confirmation Modal -->
                                     <div id="downgrade-confirm-modal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black/40">
                                       <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6 mx-auto text-center flex flex-col items-center">
                                         <svg class="h-10 w-10 text-red-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/></svg>
                                         <h3 class="text-lg font-bold mb-2 text-gray-900 dark:text-white">Confirm Downgrade</h3>
                                         <p class="mb-6 text-gray-700 dark:text-gray-300">Are you sure you want to downgrade to the Free tier? You will lose premium features.</p>
                                         <div class="flex gap-3 w-full">
                                           <button id="cancel-downgrade-btn" class="flex-1 py-2 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition">Cancel</button>
                                           <button id="confirm-downgrade-btn" class="flex-1 py-2 rounded-md bg-red-600 text-white font-medium hover:bg-red-700 transition">Confirm Downgrade</button>
                                         </div>
                                       </div>
                                     </div>
                                `;
                downgradeMessageElement
                  .querySelector("#downgrade-now-btn")
                  ?.addEventListener("click", async () => {
                    // Show custom downgrade modal
                    const modal = document.getElementById("downgrade-confirm-modal");
                    if (!modal) return;
                    modal.classList.remove("hidden");
                    // Modal button listeners
                    const cancelBtn = document.getElementById("cancel-downgrade-btn");
                    const confirmBtn = document.getElementById("confirm-downgrade-btn");
                    if (!cancelBtn || !confirmBtn) return;
                    cancelBtn.onclick = () => { modal.classList.add("hidden"); };
                    (confirmBtn as HTMLButtonElement).onclick = async () => {
                      const btn = confirmBtn as HTMLButtonElement;
                      btn.disabled = true;
                      btn.classList.add("opacity-60");
                      try {
                        const response = await fetch("/api/downgrade-subscription", { method: "POST" });
                        if (response.ok) {
                          alert("Your subscription has been downgraded to Free.");
                          window.location.reload();
                        } else {
                          alert("Failed to downgrade subscription. Please try again or contact support.");
                        }
                      } catch (err) {
                        alert("An error occurred while downgrading. Please try again.");
                      } finally {
                        btn.disabled = false;
                        btn.classList.remove("opacity-60");
                        modal.classList.add("hidden");
                      }
                    };

                  });
                featureLimitsElement.appendChild(downgradeMessageElement);
              }
            }
          }
        } else {
          // Redirect to login if no user
          window.location.href = "/login";
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
        // Optionally show error to user
        const nameElement = document.getElementById("user-name");
        const emailElement = document.getElementById("user-email");
        const currentTierElement = document.getElementById("current-tier");
        const subscriptionExpirationElement = document.getElementById(
          "subscription-expiration"
        );

        if (nameElement) {
          nameElement.textContent = "Error loading data";
          nameElement.classList.add("text-red-500");
        }
        if (emailElement) {
          emailElement.textContent = "Error loading data";
          emailElement.classList.add("text-red-500");
        }
        if (currentTierElement) {
          currentTierElement.textContent = "Error loading data";
          currentTierElement.classList.add("text-red-500");
        }
        if (subscriptionExpirationElement) {
          subscriptionExpirationElement.textContent = "Error loading data";
          subscriptionExpirationElement.classList.add("text-red-500");
        }
      }
    }

    // Initial styling for loading state
    const loadingElements = [
      "user-name",
      "user-email",
      "current-tier",
      "subscription-expiration",
    ];

    loadingElements.forEach((id) => {
      const element = document.getElementById(id);
      if (element) {
        element.classList.add("text-gray-400", "animate-pulse");
      }
    });

    // Run on client-side
    document.addEventListener("DOMContentLoaded", () => {
      fetchUserData();
    });

    // Add event listeners
    document
      .getElementById("changePasswordButton")
      ?.addEventListener("click", () => {
        window.location.href = "/forgot-password";
      });

    document.getElementById("upgrade-button")?.addEventListener("click", () => {
      window.location.href = "/pricing";
    });
  </script>
</Layout>
