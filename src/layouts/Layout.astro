---
import { AstroFont } from "astro-font";
import AppFooter from "../components/AppFooter.astro";
import AppHeader from "../components/AppHeader.astro";
import AICopilot from "../components/AICopilot.astro";
import SEO from "../components/SEO.astro";
import "../styles/global.css";
import "../tailus.css";
import "../styles/transitions.css";

// Add Firebase config
const firebaseConfig = {
  apiKey: import.meta.env.PUBLIC_FIREBASE_API_KEY,
  authDomain: import.meta.env.PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.PUBLIC_FIREBASE_APP_ID,
  measurementId: import.meta.env.PUBLIC_FIREBASE_MEASUREMENT_ID,
};

const {
  title = "PraxJobs",
  description,
  canonical,
  image,
  type,
  publishDate,
  ogTitle,
  ogDescription,
  twitterTitle,
  twitterDescription
} = Astro.props;
---

<!doctype html>
<html lang="en" class="scroll-auto">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />

    <SEO
      title={title}
      description={description}
      canonical={canonical}
      image={image}
      type={type}
      publishDate={publishDate}
      ogTitle={ogTitle}
      ogDescription={ogDescription}
      twitterTitle={twitterTitle}
      twitterDescription={twitterDescription}
    />

    <AstroFont
      config={[
        {
          src: [],
          name: "Urbanist",
          preload: true,
          display: "swap",
          selector: "html",
          fallback: "sans-serif",
          googleFontsURL:
            "https://fonts.googleapis.com/css2?family=Urbanist:wght@400;500;600;700&display=swap",
        },
      ]}
    />
    {/* Preload Firebase scripts */}
    <link
      rel="preload"
      as="script"
      href="https://www.gstatic.com/firebasejs/10.8.0/firebase-app-compat.js"
    />
    <link
      rel="preload"
      as="script"
      href="https://www.gstatic.com/firebasejs/10.8.0/firebase-auth-compat.js"
    />
    <style is:global>
      html {
        /* Optimize transitions */
        transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      body::before {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        transition: background-color 0.3s ease-in-out;
        background-color: inherit;
      }

      html.dark body::before {
        background-color: #030712;
      }

      html:not(.dark) body::before {
        background-color: #fff;
      }

      /* Optimize background pattern transitions */
      .animate-float {
        transition:
          opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
          transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      html.dark {
        color-scheme: dark;
      }
      html:not(.dark) {
        color-scheme: light;
      }
    </style>
    <script is:inline>
      (function() {
        const theme = (() => {
          if (typeof localStorage !== 'undefined' && localStorage.getItem('color-theme')) {
            return localStorage.getItem('color-theme');
          }
          if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
          }
          return 'light';
        })();
    
        if (theme === 'light') {
          document.documentElement.classList.remove('dark');
        } else {
          document.documentElement.classList.add('dark');
        }
        window.localStorage.setItem('color-theme', theme);
      })();
    </script>
  </head>
  <body
    class="bg-white dark:bg-gray-950 text-gray-900 dark:text-gray-100 overflow-x-hidden fade-in"
  >
    <!-- Mobile Menu Container (Outside Main Content) -->
    <slot name="mobile-menu" />
    <div class="relative min-h-screen flex flex-col z-10">
      <AppHeader />
      <main class="flex-grow">
        <slot />
      </main>
      <AppFooter />
    </div>
    <AICopilot />
    {/* Load Firebase scripts */}
    <script
      is:inline
      src="https://www.gstatic.com/firebasejs/10.8.0/firebase-app-compat.js"
    ></script>
    <script
      is:inline
      src="https://www.gstatic.com/firebasejs/10.8.0/firebase-auth-compat.js"
    ></script>

    {/* Firebase Initialization Script - No console logs */}
    <script define:vars={{ firebaseConfig }}>
      function initializeFirebase() {
        if (typeof firebase !== "undefined") {
          if (!firebase.apps.length) {
            try {
              firebase.initializeApp(firebaseConfig);
              // Initialization successful, no log needed for production
            } catch (error) {
              // Removed console.error("Firebase initialization error:", error);
              // Consider sending this error to an error tracking service in production
            }
          } // else {
            // Removed console.log("✅ Firebase app already initialized");
          // }
        } else {
          // Removed console.warn("⚠️ Firebase SDK not loaded yet");
          // Retry initialization after a short delay
          setTimeout(initializeFirebase, 150); // Slightly increased delay for retry
        }
      }

      // Start initialization attempt when the document is ready or immediately if already loaded
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", initializeFirebase);
      } else {
        // DOMContentLoaded has already fired
        initializeFirebase();
      }
    </script>

    <style is:global>
      html {
        /* Other global styles */
        scroll-behavior: auto;
        -webkit-font-smoothing: antialiased;
        text-rendering: optimizeLegibility;
        -moz-osx-font-smoothing: grayscale;
      }
    </style>


    <script is:inline>
      document.addEventListener('DOMContentLoaded', () => {
        document.body.classList.add('fade-in');

        // Handle fade-out on navigation
        document.querySelectorAll('a').forEach(link => {
          link.addEventListener('click', (e) => {
            const href = link.getAttribute('href');
            // Only apply fade-out for internal links that are not hash links or external
            if (href && !href.startsWith('#') && !href.includes('://') && href !== window.location.pathname) {
              e.preventDefault(); // Prevent immediate navigation
              document.body.classList.remove('fade-in');
              document.body.classList.add('fade-out');

              // Wait for the fade-out animation to complete before navigating (0.5s as defined in transitions.css)
              setTimeout(() => {
                window.location.href = href;
              }, 500); // Match the animation duration
            }
          });
        });
      });
    </script>
  </body>
</html>