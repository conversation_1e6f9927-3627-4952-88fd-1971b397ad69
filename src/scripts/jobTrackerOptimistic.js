/**
 * Job Tracker Optimistic Loading
 *
 * This script implements optimistic loading for the Job Tracker page.
 */

// Note: The main JobTrackerComponent.astro script handles fetching data
// using onSnapshot and rendering the list via renderFilteredJobs.
// This optimistic script primarily listens for preloaded data and
// triggers the rendering function in the main script if data is available early.

document.addEventListener('DOMContentLoaded', () => {
  // Listen for preloaded data
  document.addEventListener('preloaded-data-available', (event) => {
    const preloadedData = event.detail?.data;

    // Check if preloaded job data is available and the main script's
    // renderFilteredJobs function exists (it's attached to the window)
    if (preloadedData && preloadedData.jobs && Array.isArray(preloadedData.jobs) && typeof window.renderFilteredJobs === 'function') {
      console.log("Preloaded job data available:", preloadedData.jobs.length, "jobs");

      // Call the main script's rendering function with the preloaded data
      // This function is responsible for hiding the loading state and rendering.
      // We need to simulate a snapshot structure expected by renderFilteredJobs
      const simulatedSnapshot = {
        docs: preloadedData.jobs.map(job => ({
          id: job.id,
          data: () => job // Provide a data function like a real snapshot doc
        }))
      };
      window.renderFilteredJobs(simulatedSnapshot);

    } else {
      console.log("No preloaded job data available via event or render function not found.");
      // If preloaded data is not available via the event, the main script's
      // onSnapshot listener will handle fetching and rendering.
      // We don't need to do anything here, just ensure the main script runs.
    }
  });

  // No need for fallback fetching here, as the main script's onSnapshot
  // listener provides real-time updates and initial data.

});

// Note: The renderJobs and createJobCard functions are now defined
// within the main JobTrackerComponent.astro script block.
// This optimistic script only needs to trigger the main script's
// rendering function if preloaded data is available early.
