import { PersistentDocumentService } from "../lib/persistentDocumentService"; // Import the service for fallback fetching

/**
 * Renders resume cards from preloaded or fetched data
 * @param resumes The resume data to render
 */
function renderResumes(resumes) {
  const resumesList = document.getElementById('resumesList');
  const loadingElement = document.getElementById('resumeLoadingState');

  if (!resumesList) {
    console.error("resumesList element not found!");
    return;
  }

  // Hide loading state
  if (loadingElement) {
    loadingElement.classList.add('hidden');
  }

  // Clear any existing content
  resumesList.innerHTML = '';

  if (!resumes || resumes.length === 0) {
    resumesList.innerHTML = `
      <div class="bg-white/80 dark:bg-gray-800/40 backdrop-blur-xl rounded-2xl p-6 border border-gray-200/50 dark:border-gray-700/30 flex flex-col items-center justify-center text-gray-500 dark:text-gray-300 w-full mx-auto shadow-md">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 dark:text-gray-500 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <p class="text-base font-medium">No resumes added yet</p>
          <p class="text-sm mt-1">Create your first resume to get started</p>
      </div>
    `;
    return;
  }

  // Create resume cards
  resumes.forEach(resume => {
    const card = createResumeCard(resume);
    resumesList.appendChild(card);
  });

  // Note: Pagination logic is handled in the main ResumeManager.astro script
  // and will operate on the elements added to resumesList.
}

/**
 * Creates a resume card element
 * @param resume The resume data
 * @returns The resume card element
 */
function createResumeCard(resume) {
  const card = document.createElement('div');
  card.className = 'bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border border-gray-200 dark:border-gray-700';
  card.id = `resume-card-${resume.id}`;

  // Format date
  const updatedDate = resume.updatedAt ? new Date(resume.updatedAt.seconds * 1000) : new Date();
  const formattedDate = updatedDate.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });

  // Create card content
  card.innerHTML = `
    <div class="p-5">
      <div class="flex justify-between items-center mb-3">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">${resume.title || 'Untitled Resume'}</h3>
        <span class="text-xs text-gray-500 dark:text-gray-400">${formattedDate}</span>
      </div>

      <div class="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 mb-4">
        ${resume.summary || 'No summary available'}
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-2">
          <button class="edit-resume-btn px-3 py-1 text-xs bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 rounded-full hover:bg-blue-100 dark:hover:bg-blue-800/40 transition-colors" data-resume-id="${resume.id}">
            Edit
          </button>
          <button class="copy-resume-btn px-3 py-1 text-xs bg-gray-50 text-gray-600 dark:bg-gray-700/30 dark:text-gray-400 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-colors" data-resume-id="${resume.id}">
            Copy
          </button>
        </div>
        <button class="delete-resume-btn text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors" data-resume-id="${resume.id}">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>
    </div>
  `;

  return card;
}


document.addEventListener('DOMContentLoaded', async () => {
  const loadingElement = document.getElementById('resumeLoadingState');

  // Listen for preloaded data
  document.addEventListener('preloaded-data-available', (event) => {
    const preloadedData = event.detail?.data;

    if (preloadedData && preloadedData.resumes && Array.isArray(preloadedData.resumes)) {
      console.log("Preloaded resume data available:", preloadedData.resumes.length, "resumes");
      // Render resumes with preloaded data
      renderResumes(preloadedData.resumes);
    } else {
      console.log("No preloaded resume data available via event.");
      // If preloaded data is not available via the event, the main script's
      // loadResumes function will handle fetching. We just need to ensure
      // the loading state is hidden when that happens.
      // The main script's loadResumes function needs to be modified to hide
      // the loading state. I will do this in a subsequent step.
    }
  });

  // Fallback/Initial Load: The main ResumeManager.astro script's DOMContentLoaded
  // listener already calls loadResumes() which handles fetching if preloaded
  // data wasn't available. We just need to ensure the loading state is hidden
  // when loadResumes() finishes.

  // The main script's loadResumes function needs to be modified to hide
  // the loading state. I will do this in a subsequent step.

});
