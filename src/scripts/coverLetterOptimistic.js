import { APIClient } from "../lib/apiClient"; // Import APIClient for fetching data
import { PersistentDocumentService } from "../lib/persistentDocumentService"; // Import for fallback fetching

/**
 * Cover Letter Generator Optimistic Loading
 *
 * This script implements optimistic loading for the Cover Letter Generator page.
 */

// Function to populate the resume options selector
async function populateResumeOptions(preloadedResumes) {
  const resumeOptionsSelector = document.getElementById('resumeOptionsSelector');
  const resumeOptionsLoading = document.getElementById('resumeOptionsLoading');
  const resumeFileUploadButton = document.getElementById('resumeFileUploadButton');
  const importResumeButton = document.getElementById('importResumeButton');
  const enterManuallyButton = document.getElementById('enterManuallyButton');

  if (!resumeOptionsSelector || !resumeOptionsLoading || !resumeFileUploadButton || !importResumeButton || !enterManuallyButton) {
    console.error("Resume options elements not found!");
    return;
  }

  let resumesToDisplay = preloadedResumes;

  // If no preloaded resumes, fetch them as a fallback
  if (!resumesToDisplay || resumesToDisplay.length === 0) {
    try {
      // Assuming APIClient.getResumes exists and fetches resumes
      resumesToDisplay = await APIClient.getResumes();
      console.log("Fetched resumes as fallback:", resumesToDisplay);
    } catch (error) {
      console.error("Failed to fetch resumes:", error);
      // Hide loading state and show an error message if fetching fails
      resumeOptionsLoading.classList.add('hidden');
      resumeOptionsSelector.innerHTML = `<p class="col-span-full text-red-500 text-sm text-center">Failed to load resumes.</p>`;
      return;
    }
  }

  // Hide loading state
  resumeOptionsLoading.classList.add('hidden');

  // Show the actual options
  resumeFileUploadButton.classList.remove('hidden');
  importResumeButton.classList.remove('hidden');
  enterManuallyButton.classList.remove('hidden');

  // Note: The resumeInputHandler utility handles the display of the
  // file added UI or manual entry container based on user interaction.
  // We just need to ensure the initial options are shown.

}

// Function to populate the template options
function populateTemplateOptions(preloadedTemplates) {
  const templateOptions = document.getElementById('templateOptions');
  const templateOptionsLoading = document.getElementById('templateOptionsLoading');

  if (!templateOptions || !templateOptionsLoading) {
    console.error("Template options elements not found!");
    return;
  }

  let templatesToDisplay = preloadedTemplates;

  // If no preloaded templates, use the globally available templates from the component
  if (!templatesToDisplay || templatesToDisplay.length === 0) {
    if (typeof window.coverLetterTemplates !== 'undefined' && Array.isArray(window.coverLetterTemplates)) {
      templatesToDisplay = window.coverLetterTemplates;
      console.log("Using globally available templates as fallback:", templatesToDisplay);
    } else {
      console.error("No templates available via preloaded data or global variable.");
      // Hide loading state and show an error message if no templates are found
      templateOptionsLoading.classList.add('hidden');
      templateOptions.innerHTML = `<p class="col-span-full text-red-500 text-sm text-center">Failed to load templates.</p>`;
      return;
    }
  }

  // Hide loading state
  templateOptionsLoading.classList.add('hidden');

  // Clear any existing content
  templateOptions.innerHTML = '';

  if (!templatesToDisplay || templatesToDisplay.length === 0) {
    templateOptions.innerHTML = `<p class="col-span-full text-gray-500 text-sm text-center">No templates available.</p>`;
    return;
  }

  // Add template options
  templatesToDisplay.forEach((template, index) => {
    const templateOption = document.createElement('div');
    templateOption.className = 'relative group cursor-pointer'; // Keep existing classes

    templateOption.innerHTML = `
      <input
        type="radio"
        id="template-${template.id}"
        name="template"
        value="${template.id}"
        class="hidden peer"
        required
        ${index === 0 ? 'checked' : ''} // Check the first one by default
      />
      <label
        for="template-${template.id}"
        class="block p-3 border-2 border-gray-300 dark:border-gray-700 rounded-xl
               bg-white/10 dark:bg-gray-800/20
               hover:bg-white/20 dark:hover:bg-gray-800/30
               peer-checked:border-primary peer-checked:ring-2 peer-checked:ring-primary/30
               transition-all duration-300
               text-center space-y-2"
      >
        <span class="block text-sm font-semibold text-gray-900 dark:text-white">
          ${template.name || 'Untitled Template'}
        </span>
        <span class="block text-xs text-gray-600 dark:text-gray-400">
          ${template.description || 'No description available'}
        </span>
      </label>
    `;

    templateOptions.appendChild(templateOption);
  });
}


document.addEventListener('DOMContentLoaded', async () => {
  // Listen for preloaded data
  document.addEventListener('preloaded-data-available', (event) => {
    const preloadedData = event.detail?.data;

    if (preloadedData) {
      // Handle resumes if available
      if (preloadedData.resumes && Array.isArray(preloadedData.resumes)) {
        // Populate resume options using the new function
        populateResumeOptions(preloadedData.resumes);
      } else {
        console.log("No preloaded resume data available via event.");
        // If no preloaded resume data, trigger the fallback fetch
        populateResumeOptions(); // This will trigger the fetch inside the function
      }

      // Handle templates if available
      // Note: Templates are hardcoded in the component, so we just need to access them.
      // We can get them from the global scope if the component script has run.
      if (typeof window.coverLetterTemplates !== 'undefined' && Array.isArray(window.coverLetterTemplates)) {
         populateTemplateOptions(window.coverLetterTemplates);
      } else if (preloadedData.templates && Array.isArray(preloadedData.templates)) {
         // Fallback to preloaded templates if available (though they should be in window)
         populateTemplateOptions(preloadedData.templates);
      } else {
         console.log("No preloaded template data available via event or window.");
         // If no preloaded template data, the loading state will remain visible.
         // The main component script doesn't currently fetch templates,
         // so we'll rely on the initial loading state for templates.
      }
    } else {
       console.log("No preloaded data available via event.");
       // If no preloaded data at all, trigger fallback fetches
       populateResumeOptions(); // This will trigger the fetch inside the function
       // Templates will remain in loading state as they are hardcoded in component
    }
  });
});
