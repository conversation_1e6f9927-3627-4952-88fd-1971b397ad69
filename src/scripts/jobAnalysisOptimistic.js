/**
 * job research Optimistic Loading
 * 
 * This script implements optimistic loading for the job research page.
 */

document.addEventListener('DOMContentLoaded', () => {
  // Listen for preloaded data
  document.addEventListener('preloaded-data-available', (event) => {
    const preloadedData = event.detail?.data;
    
    if (preloadedData) {
      // Handle resumes if available
      if (preloadedData.resumes && Array.isArray(preloadedData.resumes)) {
        populateResumeDropdown(preloadedData.resumes);
      }
      
      // Handle recent analyses if available
      if (preloadedData.recentAnalyses && Array.isArray(preloadedData.recentAnalyses) && preloadedData.recentAnalyses.length > 0) {
        showRecentAnalyses(preloadedData.recentAnalyses);
      }
    }
  });
  
  /**
   * Populates the resume dropdown with preloaded resumes
   * @param resumes The preloaded resumes
   */
  function populateResumeDropdown(resumes) {
    if (!resumes || !resumes.length) return;
    
    const resumeDropdown = document.getElementById('resumeDropdown');
    if (!resumeDropdown) return;
    
    // Clear existing options except the first one (if it's a placeholder)
    const firstOption = resumeDropdown.querySelector('option:first-child');
    resumeDropdown.innerHTML = '';
    if (firstOption && firstOption.value === '') {
      resumeDropdown.appendChild(firstOption);
    }
    
    // Add resume options
    resumes.forEach(resume => {
      const option = document.createElement('option');
      option.value = resume.id;
      option.textContent = resume.title || `Resume ${new Date(resume.updatedAt).toLocaleDateString()}`;
      resumeDropdown.appendChild(option);
    });
    
    // Enable the dropdown
    resumeDropdown.disabled = false;
  }
  
  /**
   * Shows recent job analyses in the UI
   * @param analyses The recent job analyses
   */
  function showRecentAnalyses(analyses) {
    if (!analyses || !analyses.length) return;
    
    const recentAnalysesContainer = document.getElementById('recentAnalysesContainer');
    if (!recentAnalysesContainer) return;
    
    // Create a section for recent analyses if it doesn't exist
    let recentSection = document.getElementById('recentAnalysesSection');
    if (!recentSection) {
      recentSection = document.createElement('div');
      recentSection.id = 'recentAnalysesSection';
      recentSection.className = 'mt-8 p-4 bg-white/90 dark:bg-gray-800/90 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700';
      
      const heading = document.createElement('h3');
      heading.className = 'text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4';
      heading.textContent = 'Recent Analyses';
      recentSection.appendChild(heading);
      
      const analysesGrid = document.createElement('div');
      analysesGrid.id = 'recentAnalysesGrid';
      analysesGrid.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';
      recentSection.appendChild(analysesGrid);
      
      recentAnalysesContainer.appendChild(recentSection);
    }
    
    // Get the grid container
    const analysesGrid = document.getElementById('recentAnalysesGrid');
    if (!analysesGrid) return;
    
    // Clear existing analyses
    analysesGrid.innerHTML = '';
    
    // Add recent analyses
    analyses.forEach(analysis => {
      const card = document.createElement('div');
      card.className = 'bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow';
      
      const title = document.createElement('h4');
      title.className = 'font-medium text-gray-900 dark:text-white mb-2';
      title.textContent = analysis.jobPosition || 'Job research';
      
      const company = document.createElement('p');
      company.className = 'text-sm text-gray-600 dark:text-gray-400 mb-3';
      company.textContent = analysis.companyName || '';
      
      const date = document.createElement('p');
      date.className = 'text-xs text-gray-500 dark:text-gray-500';
      date.textContent = `Analyzed on ${new Date(analysis.createdAt).toLocaleDateString()}`;
      
      const viewButton = document.createElement('button');
      viewButton.className = 'mt-3 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300';
      viewButton.textContent = 'View Analysis';
      viewButton.onclick = () => loadAnalysis(analysis);
      
      card.appendChild(title);
      card.appendChild(company);
      card.appendChild(date);
      card.appendChild(viewButton);
      
      analysesGrid.appendChild(card);
    });
  }
  
  /**
   * Loads a saved analysis into the UI
   * @param analysis The analysis to load
   */
  function loadAnalysis(analysis) {
    // This function would be implemented to load the analysis data into the UI
    // For now, we'll just log it
    console.log('Loading analysis:', analysis);
    
    // In a real implementation, you would:
    // 1. Populate the form fields with the analysis data
    // 2. Show the analysis results
    // 3. Scroll to the results section
  }
});
