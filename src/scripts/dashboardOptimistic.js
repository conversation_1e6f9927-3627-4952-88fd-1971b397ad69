/**
 * Dashboard Optimistic Loading
 *
 * This script implements optimistic loading for the Dashboard page.
 */

import { authStore } from '../lib/authStore'; // Import authStore

document.addEventListener('DOMContentLoaded', () => {
  // First, ensure the user name is displayed from localStorage
  updateUserNameFromLocalStorage();

  // Also check authStore for user data immediately
  const currentUser = authStore.get().user;
  if (currentUser && (currentUser.displayName || currentUser.email)) {
    updateUserDataUI(currentUser);
  }

  // Listen for preloaded data
  window.addEventListener('preloaded-data-available', (event) => {
    const preloadedData = event.detail?.data;

    if (preloadedData) {
      // Handle user data if available
      if (preloadedData.userData) {
        updateUserDataUI(preloadedData.userData);
      }

      // Handle user tier data if available
      if (preloadedData.userTier) {
        updateUserTierUI(preloadedData.userTier);
      }

      // Handle feature usage data if available
      if (preloadedData.featureUsage) {
        updateFeatureUsageUI(preloadedData.featureUsage);
      }
    }
  });

  /**
   * Updates the user name from localStorage immediately on page load
   */
  function updateUserNameFromLocalStorage() {
    const userNameElement = document.getElementById("userName");
    if (!userNameElement) return;

    try {
      const storedUserData = localStorage.getItem("astro_resume_user_data");
      if (storedUserData) {
        const userData = JSON.parse(storedUserData);
        const displayName = userData.displayName ||
          (userData.email ? userData.email.split("@")[0] : "User");

        if (displayName && displayName !== "User") {
          userNameElement.textContent = displayName;
        }
      }
    } catch (error) {
      console.error("Error updating user name from localStorage:", error);
    }
  }

  /**
   * Updates the user data UI with preloaded data
   * @param userData The user data
   */
  function updateUserDataUI(userData) {
    const userNameElement = document.getElementById("userName");
    if (!userNameElement || !userData) return;

    // Get display name with fallbacks
    const displayName = userData.displayName ||
      (userData.email ? userData.email.split("@")[0] : "User");

    // Only update if we have a valid name
    if (displayName && displayName !== "User") {
      userNameElement.textContent = displayName;

      // Also update localStorage for future use
      try {
        localStorage.setItem("astro_resume_user_data", JSON.stringify(userData));
      } catch (error) {
        console.error("Error saving user data to localStorage:", error);
      }
    }
  }

  /**
   * Updates the user tier UI with preloaded data
   * @param userTier The user tier data
   */
  function updateUserTierUI(userTier) {
    // Update tier badge
    const tierBadge = document.getElementById('userTierBadge');
    if (tierBadge) {
      const tier = userTier.currentTier || 'free';
      const tierDisplayName = {
        'free': 'Free',
        'pro': 'Pro',
        'premium': 'Premium'
      }[tier] || 'Free';

      const tierColors = {
        'free': 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
        'pro': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
        'premium': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'
      }[tier] || 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';

      tierBadge.textContent = tierDisplayName;
      tierBadge.className = `px-2.5 py-0.5 text-xs font-medium rounded-full ${tierColors}`;
    }

    // Update subscription info
    const subscriptionInfo = document.getElementById('subscriptionInfo');
    if (subscriptionInfo) {
      const tier = userTier.currentTier || 'free';
      const expiryDate = userTier.expiryDate ? new Date(userTier.expiryDate.seconds * 1000) : null;

      let subscriptionText = '';
      if (tier === 'free') {
        subscriptionText = 'Free Plan';
      } else {
        subscriptionText = `${tier.charAt(0).toUpperCase() + tier.slice(1)} Plan`;
        if (expiryDate) {
          subscriptionText += ` · Expires ${expiryDate.toLocaleDateString()}`;
        }
      }

      subscriptionInfo.textContent = subscriptionText;
      subscriptionInfo.classList.remove('animate-pulse', 'bg-gray-100', 'dark:bg-gray-800');
    }

    // Update upgrade button visibility
    const upgradeButton = document.getElementById('upgradeButton');
    if (upgradeButton) {
      const tier = userTier.currentTier || 'free';
      if (tier !== 'free') {
        upgradeButton.classList.add('hidden');
      } else {
        upgradeButton.classList.remove('hidden');
      }
    }
  }

  /**
   * Updates the feature usage UI with preloaded data
   * @param featureUsage The feature usage data
   */
  function updateFeatureUsageUI(featureUsage) {
    // Update usage counters
    const features = [
      'resumeTailoring',
      'coverLetters',
      'jobAnalysis',
      'interviewPrep',
      'linkedinOptimization'
    ];

    features.forEach(feature => {
      const usageCounter = document.getElementById(`${feature}UsageCounter`);
      const usageProgress = document.getElementById(`${feature}UsageProgress`);

      if (usageCounter && usageProgress && featureUsage[feature]) {
        const usage = featureUsage[feature];
        const count = usage.usageCount || 0;
        const max = usage.maxAllowedUsage?.limit || 0;

        // Update counter text
        usageCounter.textContent = `${count}/${max}`;

        // Update progress bar
        const percentage = max > 0 ? (count / max) * 100 : 0;
        usageProgress.style.width = `${Math.min(percentage, 100)}%`;

        // Update color based on usage
        if (percentage >= 90) {
          usageProgress.classList.add('bg-red-500', 'dark:bg-red-600');
          usageProgress.classList.remove('bg-blue-500', 'dark:bg-blue-600');
        } else {
          usageProgress.classList.add('bg-blue-500', 'dark:bg-blue-600');
          usageProgress.classList.remove('bg-red-500', 'dark:bg-red-600');
        }

        // Remove loading classes
        const container = usageCounter.closest('.feature-usage-item');
        if (container) {
          container.classList.remove('animate-pulse');
        }
      }
    });
  }
});
