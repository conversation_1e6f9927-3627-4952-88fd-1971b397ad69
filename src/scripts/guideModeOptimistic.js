/**
 * Guide Mode Optimistic Loading
 * 
 * This script implements optimistic loading for the Guide Mode page.
 */

document.addEventListener('DOMContentLoaded', () => {
  // Listen for preloaded data
  document.addEventListener('preloaded-data-available', (event) => {
    const preloadedData = event.detail?.data;
    
    if (preloadedData) {
      // Handle user context if available
      if (preloadedData.userContext) {
        updateUserContextUI(preloadedData.userContext);
      }
      
      // Handle conversation history if available
      if (preloadedData.conversationHistory && Array.isArray(preloadedData.conversationHistory) && preloadedData.conversationHistory.length > 0) {
        restoreConversationHistory(preloadedData.conversationHistory);
      }
    }
  });
  
  /**
   * Updates the user context UI with preloaded data
   * @param userContext The preloaded user context
   */
  function updateUserContextUI(userContext) {
    if (!userContext) return;
    
    const contextPanel = document.getElementById('userContextPanel');
    if (!contextPanel) return;
    
    // Only show if we have meaningful context
    if (userContext.skills?.length || userContext.interests?.length || userContext.goals?.length) {
      contextPanel.innerHTML = `
        <div class="p-3 bg-gray-50/80 dark:bg-gray-800/50 rounded-3xl border border-gray-200/50 dark:border-gray-700/30 mb-3">
          <h3 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 text-left">Your Career Profile</h3>
          <div class="space-y-2 text-xs text-left">
            ${userContext.skills?.length ? `
              <div class="text-left">
                <span class="font-medium text-gray-700 dark:text-gray-300">Skills:</span>
                <span class="text-gray-600 dark:text-gray-400">${userContext.skills.slice(0, 5).join(', ')}${userContext.skills.length > 5 ? '...' : ''}</span>
              </div>
            ` : ''}
            
            ${userContext.interests?.length ? `
              <div class="text-left">
                <span class="font-medium text-gray-700 dark:text-gray-300">Interests:</span>
                <span class="text-gray-600 dark:text-gray-400">${userContext.interests.slice(0, 5).join(', ')}${userContext.interests.length > 5 ? '...' : ''}</span>
              </div>
            ` : ''}
            
            ${userContext.goals?.length ? `
              <div class="text-left">
                <span class="font-medium text-gray-700 dark:text-gray-300">Goals:</span>
                <span class="text-gray-600 dark:text-gray-400">${userContext.goals.slice(0, 3).join(', ')}${userContext.goals.length > 3 ? '...' : ''}</span>
              </div>
            ` : ''}
          </div>
        </div>
      `;
      
      // Show the panel
      contextPanel.classList.remove('hidden');
    }
  }
  
  /**
   * Restores the conversation history from preloaded data
   * @param conversationHistory The preloaded conversation history
   */
  function restoreConversationHistory(conversationHistory) {
    if (!conversationHistory || !conversationHistory.length) return;
    
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;
    
    // Clear existing messages
    chatMessages.innerHTML = '';
    
    // Add conversation history
    conversationHistory.forEach(message => {
      if (message.role === 'user') {
        // Add user message
        const userMessageEl = document.createElement('div');
        userMessageEl.className = 'user-message flex justify-end mb-4';
        userMessageEl.innerHTML = `
          <div class="max-w-[85%] bg-blue-500 text-white rounded-2xl rounded-tr-sm px-4 py-2 shadow-sm">
            <p class="text-sm">${message.parts[0].text}</p>
          </div>
        `;
        chatMessages.appendChild(userMessageEl);
      } else if (message.role === 'model') {
        // Add AI message
        const aiMessageEl = document.createElement('div');
        aiMessageEl.className = 'ai-message flex mb-4';
        aiMessageEl.innerHTML = `
          <div class="max-w-[85%] bg-gray-100 dark:bg-gray-800 rounded-2xl rounded-tl-sm px-4 py-2 shadow-sm">
            <p class="text-sm text-gray-800 dark:text-gray-200">${message.parts[0].text}</p>
          </div>
        `;
        chatMessages.appendChild(aiMessageEl);
      }
    });
    
    // Scroll to bottom
    chatMessages.scrollTo(0, chatMessages.scrollHeight);
    
    // Hide suggestions if we have conversation history
    const suggestionsContainer = document.getElementById('suggestions-container');
    if (suggestionsContainer) {
      suggestionsContainer.classList.add('hidden');
    }
  }
});
