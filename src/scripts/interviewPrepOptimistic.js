/**
 * Interview Prep Optimistic Loading
 * 
 * This script implements optimistic loading for the Interview Prep page.
 */

document.addEventListener('DOMContentLoaded', () => {
  // Listen for preloaded data
  document.addEventListener('preloaded-data-available', (event) => {
    const preloadedData = event.detail?.data;
    
    if (preloadedData && preloadedData.categories && Array.isArray(preloadedData.categories)) {
      // Get the categories container
      const categoriesContainer = document.getElementById('categoriesContainer');
      const loadingElement = document.getElementById('categoriesLoadingState');
      
      if (categoriesContainer) {
        // Hide loading state if it exists
        if (loadingElement) {
          loadingElement.classList.add('hidden');
        }
        
        // Initialize the categories UI
        initializeCategoriesUI(preloadedData.categories, categoriesContainer);
      }
    }
  });
  
  /**
   * Initializes the categories UI with preloaded data
   * @param categories The categories data
   * @param container The container element
   */
  function initializeCategoriesUI(categories, container) {
    if (!container || !categories.length) return;
    
    // Map of category IDs to display names
    const categoryDisplayNames = {
      'behavioral': 'Behavioral Questions',
      'technical': 'Technical Questions',
      'roleSpecific': 'Role-Specific Questions',
      'companySpecific': 'Company-Specific Questions',
      'situational': 'Situational Questions',
      'leadership': 'Leadership Questions'
    };
    
    // Map of category IDs to icons
    const categoryIcons = {
      'behavioral': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                    </svg>`,
      'technical': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                   </svg>`,
      'roleSpecific': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                       </svg>`,
      'companySpecific': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                          </svg>`,
      'situational': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>`,
      'leadership': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                       <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                     </svg>`
    };
    
    // Create category tabs
    const tabsHTML = categories.map(category => {
      const displayName = categoryDisplayNames[category] || category;
      const icon = categoryIcons[category] || '';
      
      return `
        <button class="category-tab flex items-center space-x-2 px-4 py-2 rounded-lg text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors" data-category="${category}">
          ${icon}
          <span>${displayName}</span>
        </button>
      `;
    }).join('');
    
    // Add tabs to container
    container.innerHTML = tabsHTML;
    
    // Add placeholder for questions that will be loaded when a category is selected
    const questionsContainer = document.getElementById('questionsContainer');
    if (questionsContainer) {
      questionsContainer.innerHTML = `
        <div class="text-center py-8">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 dark:text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p class="text-gray-500 dark:text-gray-400">Select a category to view interview questions</p>
        </div>
      `;
    }
  }
});
