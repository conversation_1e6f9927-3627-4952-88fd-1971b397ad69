/**
 * AI Copilot Optimistic Loading
 * 
 * This script implements optimistic loading for the AI Copilot component.
 */

document.addEventListener('DOMContentLoaded', () => {
  // Listen for preloaded data
  document.addEventListener('preloaded-data-available', (event) => {
    const preloadedData = event.detail?.data;
    
    if (preloadedData) {
      // Handle copilot access if available
      if (preloadedData.copilotAccess !== undefined) {
        updateCopilotAccess(preloadedData.copilotAccess);
      }
      
      // Handle conversation history if available
      if (preloadedData.copilotHistory && Array.isArray(preloadedData.copilotHistory) && preloadedData.copilotHistory.length > 0) {
        restoreCopilotHistory(preloadedData.copilotHistory);
      }
    }
  });
  
  /**
   * Updates the copilot access UI based on preloaded data
   * @param hasAccess Whether the user has access to the copilot
   */
  function updateCopilotAccess(hasAccess) {
    const triggerButton = document.getElementById('copilot-trigger');
    if (!triggerButton) return;
    
    if (hasAccess) {
      // Show the copilot trigger button
      triggerButton.classList.remove('hidden');
      
      // Remove any upgrade prompts
      const upgradePrompt = document.getElementById('copilot-upgrade-prompt');
      if (upgradePrompt) {
        upgradePrompt.classList.add('hidden');
      }
    } else {
      // Hide the copilot trigger button
      triggerButton.classList.add('hidden');
      
      // Show upgrade prompt if it exists
      const upgradePrompt = document.getElementById('copilot-upgrade-prompt');
      if (upgradePrompt) {
        upgradePrompt.classList.remove('hidden');
      }
    }
  }
  
  /**
   * Restores the copilot conversation history from preloaded data
   * @param history The preloaded conversation history
   */
  function restoreCopilotHistory(history) {
    if (!history || !history.length) return;
    
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;
    
    // Don't restore history if the chat is already open and has messages
    if (chatMessages.hasChildNodes()) return;
    
    // Add conversation history
    history.forEach(message => {
      if (message.role === 'user') {
        // Add user message
        const userMessageEl = document.createElement('div');
        userMessageEl.className = 'user-message flex justify-end mb-3';
        userMessageEl.innerHTML = `
          <div class="max-w-[85%] bg-blue-500 text-white rounded-xl rounded-tr-sm px-3 py-2 shadow-sm">
            <p class="text-sm">${message.content}</p>
          </div>
        `;
        chatMessages.appendChild(userMessageEl);
      } else if (message.role === 'assistant') {
        // Add AI message
        const aiMessageEl = document.createElement('div');
        aiMessageEl.className = 'ai-message flex mb-3';
        aiMessageEl.innerHTML = `
          <div class="max-w-[85%] bg-gray-100 dark:bg-gray-800 rounded-xl rounded-tl-sm px-3 py-2 shadow-sm">
            <p class="text-sm text-gray-800 dark:text-gray-200">${message.content}</p>
          </div>
        `;
        chatMessages.appendChild(aiMessageEl);
      }
    });
  }
});
