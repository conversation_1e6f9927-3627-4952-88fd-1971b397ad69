/**
 * LinkedIn Optimizer Optimistic Loading
 * 
 * This script implements optimistic loading for the LinkedIn Optimizer page.
 */

document.addEventListener('DOMContentLoaded', () => {
  // Listen for preloaded data
  document.addEventListener('preloaded-data-available', (event) => {
    const preloadedData = event.detail?.data;
    
    if (preloadedData && preloadedData.resumes && Array.isArray(preloadedData.resumes)) {
      populateResumeDropdown(preloadedData.resumes);
    }
  });
  
  /**
   * Populates the resume dropdown with preloaded resume data
   * @param resumes The resume data to populate with
   */
  function populateResumeDropdown(resumes) {
    if (!resumes.length) return;
    
    const resumeSelect = document.getElementById('resumeSelect');
    if (!resumeSelect) return;
    
    // Clear loading state and any existing options except the first one
    while (resumeSelect.options.length > 1) {
      resumeSelect.remove(1);
    }
    
    // Add resume options
    resumes.forEach(resume => {
      const option = document.createElement('option');
      option.value = resume.id;
      option.textContent = resume.title || 'Untitled Resume';
      option.dataset.content = JSON.stringify(resume);
      resumeSelect.appendChild(option);
    });
    
    // Remove any loading classes
    resumeSelect.classList.remove('animate-pulse', 'bg-gray-100', 'dark:bg-gray-800');
    
    // Enable the select
    resumeSelect.disabled = false;
    
    // Also update the resume file upload component if it exists
    const resumeFileUpload = document.getElementById('resumeFileUpload');
    if (resumeFileUpload) {
      resumeFileUpload.classList.remove('animate-pulse');
    }
  }
});
