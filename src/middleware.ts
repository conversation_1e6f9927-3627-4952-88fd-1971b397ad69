import { defineMiddleware } from "astro/middleware";

export const onRequest = defineMiddleware(async ({ request, url, cookies }, next) => {
  const protectedRoutes = ['/dashboard', '/settings', '/resume', '/cover-letter'];
  
  // Check if this is a protected route
  if (protectedRoutes.some(route => url.pathname.startsWith(route))) {
    const sessionCookie = cookies.get('session')?.value;
    
    // If no session cookie, redirect to login
    if (!sessionCookie) {
      return Response.redirect(new URL('/login', request.url));
    }
    
    try {
      const verifyUrl = new URL('/.netlify/functions/verify-session', request.url);
      const response = await fetch(verifyUrl.toString(), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionCookie }),
      });

      if (!response.ok) {
        throw new Error('Session verification failed');
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error('Invalid session');
      }
    } catch (error) {
      // Clear the invalid cookie and redirect to login, preserving the current path as a redirect parameter
      cookies.delete('session', { path: '/' });
      const redirectUrl = new URL('/login', request.url);
      redirectUrl.searchParams.set('redirect', url.pathname);
      return Response.redirect(redirectUrl);
    }
  }

  const response = await next();

  // Cache static assets
  if (url.pathname.startsWith('/assets/')) {
    response.headers.set('Cache-Control', 'public, max-age=31536000');
  }

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  return response;
});
