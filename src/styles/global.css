@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Light mode colors */
  --color-background: theme('colors.white');
  --color-text: theme('colors.gray.900');
  --color-primary: theme('colors.blue.600');
  --color-purple: theme('colors.purple.600');

  /* Transition Colors */
  --transition-overlay-color: rgba(255, 255, 255, 0.9);
}

.dark {
  /* Dark mode colors */
  --color-background: theme('colors.gray.900');
  --color-text: theme('colors.gray.100');
  --color-primary: theme('colors.blue.400');
  --color-purple: theme('colors.purple.400');

  /* Transition Colors */
  --transition-overlay-color: rgba(0, 0, 0, 0.9);
}

/* Global transition for smooth dark mode switch - optimized for performance */

/* Theme Transition Styles */
html {
    transition:
        background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        color 0.3s cubic-bezier(0.4, 0, 0.2, 1);

}

html.dark {
    color-scheme: dark;
}

body {
    transition:
        background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom styles below */

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

/* Smooth scroll behavior */
html {
    scroll-behavior: auto;
    overflow-x: hidden;
}

body {
    min-height: 100vh;
    width: 100vw;
    max-width: 100%;
    position: relative;
}

/* Prevent overflow on mobile */
@media (max-width: 768px) {
    html, body {
        overflow-x: hidden;
        position: relative;
    }
}
@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

.slide-in {
  animation: slideInRight 0.2s ease-out forwards;
}

.slide-out {
  animation: slideOutRight 0.2s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.2s ease-out forwards;
}



/* Modal Transitions */
@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalFadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes modalContentIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes modalContentOut {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
}

.modal-transition {
  transition: opacity 0.2s ease-out;
}

.modal-content-transition {
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}

.modal-fade-in {
  animation: modalFadeIn 0.2s ease-out forwards;
}

.modal-fade-out {
  animation: modalFadeOut 0.2s ease-in forwards;
}

.modal-content-in {
  animation: modalContentIn 0.2s ease-out forwards;
}

.modal-content-out {
  animation: modalContentOut 0.2s ease-in forwards;
}

