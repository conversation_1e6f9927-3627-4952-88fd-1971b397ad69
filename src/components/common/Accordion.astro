---
interface Props {
  title: string;
  isOpen?: boolean;
}

const { title, isOpen = false } = Astro.props;
---

<div class="accordion-item border border-gray-200 dark:border-gray-700 rounded-md mb-4">
  <h2 class="accordion-header" id={`heading-${title.replace(/\s/g, '-')}`}>
    <button
      class="accordion-button flex justify-between items-center w-full p-4 text-left font-semibold text-gray-800 dark:text-white bg-gray-50 dark:bg-gray-800 rounded-t-md focus:outline-none"
      type="button"
      data-bs-toggle="collapse"
      data-bs-target={`#collapse-${title.replace(/\s/g, '-')}`}
      aria-expanded={isOpen}
      aria-controls={`collapse-${title.replace(/\s/g, '-')}`}
    >
      {title}
      <svg class="w-5 h-5 transform transition-transform duration-200 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </button>
  </h2>
  <div
    id={`collapse-${title.replace(/\s/g, '-')}`}
    class={`accordion-collapse ${isOpen ? 'show' : ''}`}
    aria-labelledby={`heading-${title.replace(/\s/g, '-')}`}
  >
  >
    <div class="accordion-body p-4">
      <slot />
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.accordion-button').forEach(button => {
      button.addEventListener('click', () => {
        const targetId = button.dataset.bsTarget;
        const targetEl = document.querySelector(targetId as string);
        const isExpanded = button.getAttribute('aria-expanded') === 'true';

        button.setAttribute('aria-expanded', String(!isExpanded));
        targetEl?.classList.toggle('show');
        button.querySelector('svg')?.classList.toggle('rotate-180', !isExpanded);
      });
    });
  });
</script>

<style>
  .accordion-button[aria-expanded="true"] svg {
    transform: rotate(180deg);
  }
  .accordion-collapse:not(.show) {
    display: none;
  }
</style>
