<div
  id="subscription-expired-modal"
  class="hidden fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
>
  <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full text-center">
    <h2 class="text-xl font-semibold mb-4">Your subscription has expired</h2>
    <p class="mb-6">
      To continue enjoying premium features, please upgrade your subscription.
    </p>
    <a
      href="/pricing"
      class="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
    >
      Upgrade Now
    </a>
  </div>
</div>

<script type="module">
  const checkStore = () => {
    const modal = document.getElementById("subscription-expired-modal");
    if (!modal || !window.authStore) return;

    window.authStore.subscribe((state) => {
      if (state.subscriptionExpired) {
        modal.classList.remove("hidden");
      } else {
        modal.classList.add("hidden");
      }
    });
  };

  if (window.authStore) {
    checkStore();
  } else {
    window.addEventListener("load", checkStore);
  }
</script>
