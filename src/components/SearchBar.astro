---
import Container from "../components/Container.astro";
---

<Container>
  <section
    id="search-section"
    class="py-8 w-full max-w-5xl mx-auto text-center rounded-full opacity-0 translate-y-4 transition-all duration-700 ease-out"
  >
    <form
      action="/guide"
      method="GET"
      class="relative group"
      id="dashboard-search-form"
    >
      <div
        id="search-wrapper"
        class="relative flex flex-col sm:flex-row items-center w-full gap-x-2 gap-y-2 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl md:rounded-full border border-gray-300 dark:border-gray-700 shadow-md hover:shadow-lg focus-within:shadow-xl focus-within:ring-2 focus-within:ring-purple-500/70 dark:focus-within:ring-purple-500 transition-all duration-300 p-2 sm:p-2"
      >
        <textarea
          id="search-input"
          name="query"
          rows="1"
          placeholder="Ask me anything"
          autocomplete="off"
          aria-label="Search for guidance"
          class="flex-grow w-full py-2 pl-4 text-sm sm:text-base bg-transparent focus:outline-none text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 resize-none overflow-hidden"
        ></textarea>
        
        <div class="flex flex-row w-full justify-end items-center gap-x-2">
          <!-- Animated Buttons Container -->
          <div class="relative flex items-center h-9 w-9">
            <button
              type="button"
              id="clear-search-btn"
              tabindex="-1"
              class="absolute inset-0 flex items-center justify-center rounded-full text-gray-500 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none opacity-0 scale-75 transition-all duration-200"
              aria-label="Clear search input"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
            </button>
            <button
              type="button"
              id="voice-search-btn"
              tabindex="-1"
              class="absolute inset-0 flex items-center justify-center rounded-full text-purple-500 hover:bg-purple-100 dark:hover:bg-gray-800 focus:outline-none opacity-0 scale-75 transition-all duration-200"
              aria-label="Search by voice"
            >
              <svg id="mic-icon" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12 15a3 3 0 003-3V7a3 3 0 10-6 0v5a3 3 0 003 3z"></path><path d="M19 11a1 1 0 10-2 0 5 5 0 01-10 0 1 1 0 10-2 0 7 7 0 006 6.92V21a1 1 0 102 0v-3.08A7 7 0 0019 11z"></path></svg>
            </button>
          </div>

          <span id="voice-status" class="sr-only" aria-live="polite"></span>

          <button
            type="submit"
            class="flex-shrink-0 h-11 w-11 flex items-center justify-center bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white font-medium rounded-full shadow-lg hover:shadow-purple-500/30 transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900 focus:ring-purple-500"
            aria-label="Submit search"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5"><path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
          </button>
        </div>
      </div>
    </form>
  </section>
</Container>

<style>
  /* Custom class for animated button visibility */
  .is-visible {
    opacity: 1 !important;
    transform: scale(1) !important;
  }
  /* Hide default clear button */
  input[type="search"]::-webkit-search-cancel-button {
    -webkit-appearance: none;
  }
</style>

<script is:inline>
  document.addEventListener('DOMContentLoaded', () => {
    // --- 1. Element Selection ---
    const searchSection = document.getElementById("search-section");
    const searchForm = document.getElementById("dashboard-search-form");
    const input = document.getElementById("search-input");
    const clearBtn = document.getElementById("clear-search-btn");
    const voiceBtn = document.getElementById("voice-search-btn");
    const micIcon = document.getElementById("mic-icon");
    const voiceStatus = document.getElementById("voice-status");

    if (!searchForm || !input || !clearBtn || !voiceBtn || !micIcon || !voiceStatus) {
      console.error("Search component UI element missing.");
      return;
    }

    // --- 2. State & UI Functions ---
    let recognition;
    let isRecognizing = false;
    let typewriterTimeout;

    const updateButtonVisibility = () => {
      const hasValue = input.value.length > 0;
      clearBtn.classList.toggle('is-visible', hasValue);
      voiceBtn.classList.toggle('is-visible', !hasValue);
    };

    const autoExpandTextarea = () => {
      input.style.height = 'auto';
      input.style.height = input.scrollHeight + 'px';
    };

    // --- 3. Typewriter Placeholder Animation ---
    const phrases = [
      "How do I switch from marketing to tech?",
      "What career suits my strengths?",
      "What is the best way to learn data science?",
      "What are the top skills for data analysts?",
    ];
    let phraseIndex = 0;
    let charIndex = 0;
    let isDeleting = false;
    let isTypingPaused = false;

    const type = () => {
      if (isTypingPaused) return;
      
      const currentPhrase = phrases[phraseIndex];
      let displayText = '';

      if (isDeleting) {
        displayText = currentPhrase.substring(0, charIndex - 1);
        charIndex--;
      } else {
        displayText = currentPhrase.substring(0, charIndex + 1);
        charIndex++;
      }
      input.setAttribute('placeholder', displayText);

      let typeSpeed = isDeleting ? 30 : 30;

      if (!isDeleting && charIndex === currentPhrase.length) {
        typeSpeed = 2000; // Pause at end
        isDeleting = true;
      } else if (isDeleting && charIndex === 0) {
        isDeleting = false;
        phraseIndex = (phraseIndex + 1) % phrases.length;
        typeSpeed = 500; // Pause before new phrase
      }
      
      typewriterTimeout = setTimeout(type, typeSpeed);
    };

    const startTypingAnimation = () => {
      if (input.value.length === 0) {
        isTypingPaused = false;
        clearTimeout(typewriterTimeout);
        type();
      }
    };

    const stopTypingAnimation = () => {
      isTypingPaused = true;
      clearTimeout(typewriterTimeout);
      input.setAttribute('placeholder', 'Ask me anything'); // Reset to default
    };

    // --- 4. Event Handlers ---
    const handleFormSubmit = (e) => {
      try {
        localStorage.removeItem('guideMode_conversation');
        localStorage.removeItem('guideMode_timestamp');
      } catch (err) {
        console.warn("Failed to clear localStorage:", err);
      }
    };

    const handleClearClick = () => {
      input.value = "";
      updateButtonVisibility();
      autoExpandTextarea(); // Reset height when cleared
      input.focus();
    };

    const handleVoiceClick = () => {
      if (isRecognizing) {
        recognition.stop();
      } else {
        try {
          recognition.start();
        } catch (e) {
          console.error("Voice recognition could not be started:", e);
          voiceStatus.textContent = "Voice recognition error. Please check permissions.";
        }
      }
    };

    // --- 5. Initialization & Event Listeners ---
    const hiddenInput = document.createElement("input");
    hiddenInput.type = "hidden";
    hiddenInput.name = "from";
    hiddenInput.value = "dashboard";
    searchForm.appendChild(hiddenInput);

    searchForm.addEventListener("submit", handleFormSubmit);
    input.addEventListener("input", () => {
      updateButtonVisibility();
      autoExpandTextarea(); // Auto-expand on input
    });
    input.addEventListener("focus", stopTypingAnimation);
    input.addEventListener("blur", startTypingAnimation);
    clearBtn.addEventListener("click", handleClearClick);

    // Voice Recognition Setup
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (SpeechRecognition) {
      recognition = new SpeechRecognition();
      recognition.lang = "en-US";
      recognition.interimResults = false;
      recognition.maxAlternatives = 1;

      recognition.onstart = () => {
        isRecognizing = true;
        voiceStatus.textContent = "Listening...";
        micIcon.parentElement.classList.add("bg-purple-100", "dark:bg-purple-600/50", "animate-pulse");
      };
      recognition.onend = () => {
        isRecognizing = false;
        voiceStatus.textContent = "";
        micIcon.parentElement.classList.remove("bg-purple-100", "dark:bg-purple-600/50", "animate-pulse");
      };
      recognition.onresult = (event) => {
        input.value = event.results[0][0].transcript;
        input.dispatchEvent(new Event("input"));
        input.focus();
      };
      recognition.onerror = (event) => {
        console.error("Speech recognition error:", event.error);
        voiceStatus.textContent = event.error === 'not-allowed' ? "Microphone access denied." : "Voice recognition error.";
      };
      voiceBtn.addEventListener("click", handleVoiceClick);
    } else {
      voiceBtn.classList.add('hidden');
    }

    // Set initial states
    updateButtonVisibility();
    autoExpandTextarea(); // Set initial height on load
    
    // Trigger entry animation
    setTimeout(() => {
      searchSection.style.opacity = '1';
      searchSection.style.transform = 'translateY(0)';
      startTypingAnimation();
    }, 100);
  });
</script>