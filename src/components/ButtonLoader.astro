---
interface Props {
  id?: string;
  isLoading?: boolean;
  loadingText?: string;
  spinnerPosition?: 'left' | 'right';
  spinnerSize?: 'xs' | 'sm' | 'md';
  spinnerColor?: 'inherit' | 'white' | 'primary' | 'gray';
  className?: string;
}

const {
  id,
  isLoading = false,
  loadingText,
  spinnerPosition = 'left',
  spinnerSize = 'sm',
  spinnerColor = 'inherit',
  className = ''
} = Astro.props;

// Size mappings
const sizeMap = {
  xs: 'h-3 w-3',
  sm: 'h-4 w-4',
  md: 'h-5 w-5'
};

// Color mappings
const colorMap = {
  inherit: 'text-current',
  white: 'text-white',
  primary: 'text-primary',
  gray: 'text-gray-700 dark:text-gray-300'
};

const spinnerSizeClass = sizeMap[spinnerSize] || sizeMap.sm;
const spinnerColorClass = colorMap[spinnerColor] || colorMap.inherit;

// Generate unique ID if not provided
const uniqueId = id || `button-loader-${Math.random().toString(36).substring(2, 9)}`;
---

<span
  id={uniqueId}
  class={`inline-flex items-center justify-center ${className} ${isLoading ? 'pointer-events-none' : ''}`}
  data-loading={isLoading}
>
  {isLoading && spinnerPosition === 'left' && (
    <svg
      class={`animate-spin mr-2 -ml-1 ${spinnerSizeClass} ${spinnerColorClass}`}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
  )}

  <span id={`${uniqueId}-text`} class="inline-flex items-center">
    {isLoading && loadingText ? loadingText : <slot />}
  </span>

  {isLoading && spinnerPosition === 'right' && (
    <svg
      class={`animate-spin ml-2 -mr-1 ${spinnerSizeClass} ${spinnerColorClass}`}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
  )}
</span>

<script is:inline define:vars={{ uniqueId }}>
  // Function to set loading state
  function setLoading(isLoading, loadingText) {
    const buttonLoader = document.getElementById(uniqueId);
    const textElement = document.getElementById(`${uniqueId}-text`);

    if (buttonLoader) {
      // Store original text if not already stored
      if (isLoading && textElement && !buttonLoader.dataset.originalText) {
        buttonLoader.dataset.originalText = textElement.innerHTML;
      }

      // Update loading state
      buttonLoader.dataset.loading = isLoading;

      // Add or remove pointer-events-none class
      if (isLoading) {
        buttonLoader.classList.add('pointer-events-none');
      } else {
        buttonLoader.classList.remove('pointer-events-none');
      }

      // Show or hide spinner elements
      const spinners = buttonLoader.querySelectorAll('svg');
      spinners.forEach(spinner => {
        spinner.style.display = isLoading ? 'inline-block' : 'none';
      });

      // Update text if loading text is provided
      if (textElement) {
        if (isLoading && loadingText) {
          textElement.textContent = loadingText;
        } else if (!isLoading && buttonLoader.dataset.originalText) {
          textElement.innerHTML = buttonLoader.dataset.originalText;
          delete buttonLoader.dataset.originalText;
        }
      }
    }
  }

  // Expose function to window for external access
  if (window.buttonLoaderFunctions === undefined) {
    window.buttonLoaderFunctions = {};
  }

  window.buttonLoaderFunctions[uniqueId] = {
    setLoading
  };
</script>
