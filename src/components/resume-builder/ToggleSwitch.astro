---
interface Props {
    id: string;
    label: string;
    checked: boolean;
    'data-setting'?: string;
    'data-section'?: string;
}

const { id, label, checked, ...rest } = Astro.props;
const dataAttrs = {};
if (rest['data-setting']) dataAttrs['data-setting'] = rest['data-setting'];
if (rest['data-section']) dataAttrs['data-section'] = rest['data-section'];
---

<label for={id} class="flex items-center justify-between cursor-pointer">
    <span class="text-sm font-medium text-gray-900 dark:text-gray-300">{label}</span>
    <div class="relative">
        <input
            type="checkbox"
            id={id}
            class="sr-only peer"
            checked={checked}
            {...dataAttrs}
        />
        <div class="w-11 h-6 bg-gray-200 rounded-full peer peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-500 dark:bg-gray-700 peer-checked:bg-indigo-600"></div>
        <div class="absolute left-1 top-1 bg-white border-gray-300 border rounded-full h-4 w-4 transition-transform peer-checked:translate-x-full peer-checked:border-white"></div>
    </div>
</label>

<script>
    document.addEventListener('change', (e) => {
        const target = e.target as HTMLInputElement;
        if (target.type === 'checkbox' && target.classList.contains('peer')) {
            const isChecked = target.checked;
            const container = target.nextElementSibling;
            const dot = container?.nextElementSibling;

            if (container) {
                container.classList.toggle('bg-indigo-600', isChecked);
                container.classList.toggle('bg-gray-200', !isChecked);
                container.classList.toggle('dark:bg-gray-700', !isChecked);
            }
            if (dot) {
                dot.classList.toggle('translate-x-full', isChecked);
            }
        }
    });
</script>
