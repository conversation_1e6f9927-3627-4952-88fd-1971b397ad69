---
// Form section for education.
---

<div id="education-container" class="space-y-6">
  <!-- Dynamic content will be injected here -->
</div>

<button type="button" id="add-education-btn" class="w-full text-center mt-4 px-4 py-2 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:border-primary hover:text-primary dark:hover:border-primary dark:hover:text-primary transition">
  + Add Another Education
</button>

<template id="education-template">
  <div class="p-4 border border-gray-200 dark:border-gray-700/50 rounded-lg space-y-4 education-item">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Institution</label>
        <input type="text" name="institution" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Degree</label>
        <input type="text" name="degree" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
      </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Field of Study</label>
        <input type="text" name="fieldOfStudy" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Graduation Date</label>
        <input type="month" name="graduationDate" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
      </div>
    </div>
    <div>
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Details</label>
      <textarea name="details" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white"></textarea>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        Include honors, awards, or relevant coursework.
      </p>
    </div>
    <button type="button" class="remove-education-btn text-red-500 hover:text-red-700">Remove</button>
  </div>
</template>

<script>
  import { resumeData, addItem, removeItem, updateSection } from '../../../lib/resumeBuilderService';

  document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('education-container');
    const template = document.getElementById('education-template') as HTMLTemplateElement;
    const addButton = document.getElementById('add-education-btn');

    if (!container || !template || !addButton) return;

    const render = (education) => {
      const renderedItems = new Map(
        Array.from(container.querySelectorAll('.education-item')).map(el => [(el as HTMLElement).dataset.id, el])
      );

      renderedItems.forEach((el, id) => {
        if (!education.some(item => item.id === id)) {
          el.remove();
        }
      });

      education.forEach((item, index) => {
        let itemElement = renderedItems.get(item.id);

        if (!itemElement) {
          const clone = (template.content.cloneNode(true)) as DocumentFragment;
          const newItem = clone.querySelector('.education-item');
          if (!newItem || !(newItem instanceof HTMLElement)) return;
          itemElement = newItem;

          (itemElement as HTMLElement).dataset.id = item.id;
          container.appendChild(itemElement);

          const inputs = itemElement.querySelectorAll('input, textarea');
          inputs.forEach(input => {
            const name = input.getAttribute('name');
            if (name) {
              input.addEventListener('input', (e) => {
                const target = e.target as HTMLInputElement | HTMLTextAreaElement;
                const currentEducation = [...resumeData.get().education];
                const educationItem = currentEducation.find(edu => edu.id === item.id);
                if (educationItem) {
                  educationItem[name] = target.value;
                  updateSection('education', currentEducation);
                }
              });
            }
          });

          const removeButton = itemElement.querySelector('.remove-education-btn');
          if (removeButton) {
            removeButton.addEventListener('click', () => {
              removeItem('education', item.id);
            });
          }
        }

        const inputs = itemElement.querySelectorAll('input, textarea');
        inputs.forEach(input => {
          const name = input.getAttribute('name');
          if (name && name in item) {
            const inputElement = input as HTMLInputElement | HTMLTextAreaElement;
            if (inputElement.value !== item[name]) {
              inputElement.value = item[name];
            }
          }
        });
      });
    };

    // Initial render with current data
    const currentData = resumeData.get();
    if (currentData.education) {
      render(currentData.education);
    }

    const unsubscribe = resumeData.subscribe(data => {
      if (data.education) {
        render(data.education);
      }
    });

    addButton.addEventListener('click', () => {
      addItem('education');
    });

    window.addEventListener('beforeunload', unsubscribe);
  });
</script>