---
// Form section for custom, user-defined sections.
---

<div id="custom-sections-container" class="space-y-6">
  <!-- Dynamic content will be injected here -->
</div>

<button type="button" id="add-section-btn" class="w-full text-center px-4 py-2 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:border-primary hover:text-primary dark:hover:border-primary dark:hover:text-primary transition">
  + Add Another Section
</button>

<template id="section-template">
  <div class="p-4 border border-gray-200 dark:border-gray-700/50 rounded-lg space-y-4 custom-section-item">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Section Title</label>
        <input type="text" name="title" placeholder="e.g., Projects, Certifications" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
      </div>
    </div>
    <div>
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Content</label>
      <textarea name="content" rows="4" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white"></textarea>
    </div>
    <button type="button" class="remove-section-btn text-red-500 hover:text-red-700">Remove Section</button>
  </div>
</template>

<script>
  import { resumeData, addItem, removeItem, updateSection } from '../../../lib/resumeBuilderService';

  document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('custom-sections-container');
    const template = document.getElementById('section-template') as HTMLTemplateElement;
    const addButton = document.getElementById('add-section-btn');

    if (!container || !template || !addButton) return;

    const render = (customSections) => {
      const renderedItems = new Map(
        Array.from(container.querySelectorAll('.custom-section-item')).map(el => [(el as HTMLElement).dataset.id, el])
      );

      renderedItems.forEach((el, id) => {
        if (!customSections.some(item => item.id === id)) {
          el.remove();
        }
      });

      customSections.forEach((item, index) => {
        let itemElement = renderedItems.get(item.id);

        if (!itemElement) {
          const clone = (template.content.cloneNode(true)) as DocumentFragment;
          const newItem = clone.querySelector('.custom-section-item');
          if (!newItem || !(newItem instanceof HTMLElement)) return;
          itemElement = newItem;

          (itemElement as HTMLElement).dataset.id = item.id;
          container.appendChild(itemElement);

          const inputs = itemElement.querySelectorAll('input, textarea');
          inputs.forEach(input => {
            const name = input.getAttribute('name');
            if (name) {
              input.addEventListener('input', (e) => {
                const target = e.target as HTMLInputElement | HTMLTextAreaElement;
                const currentSections = [...resumeData.get().customSections];
                const sectionItem = currentSections.find(sec => sec.id === item.id);
                if (sectionItem) {
                  sectionItem[name] = target.value;
                  updateSection('customSections', currentSections);
                }
              });
            }
          });

          const removeButton = itemElement.querySelector('.remove-section-btn');
          if (removeButton) {
            removeButton.addEventListener('click', () => {
              removeItem('customSections', item.id);
            });
          }
        }

        const inputs = itemElement.querySelectorAll('input, textarea');
        inputs.forEach(input => {
          const name = input.getAttribute('name');
          if (name && name in item) {
            const inputElement = input as HTMLInputElement | HTMLTextAreaElement;
            if (inputElement.value !== item[name]) {
              inputElement.value = item[name as keyof typeof item];
            }
          }
        });
      });
    };

    const unsubscribe = resumeData.subscribe(data => {
      if (data.customSections) {
        render(data.customSections);
      }
    });

    addButton.addEventListener('click', () => {
      addItem('customSections');
    });

    window.addEventListener('beforeunload', unsubscribe);
  });
</script>