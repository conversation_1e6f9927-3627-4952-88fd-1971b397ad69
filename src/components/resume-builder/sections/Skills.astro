---
// Form section for skills.
---

<div class="space-y-4">
  <div>
    <label for="skills" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Skills</label>
    <textarea id="skills" name="skills" rows="4" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white"></textarea>
    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
      Enter skills separated by commas (e.g., JavaScript, React, Node.js).
    </p>
  </div>
</div>

<script>
  import { resumeData, updateSection } from '../../../lib/resumeBuilderService';

  document.addEventListener('DOMContentLoaded', () => {
    const skillsTextarea = document.getElementById('skills') as HTMLTextAreaElement;

    if (!skillsTextarea) return;

    // Initial population from store
    const currentData = resumeData.get();
    if (currentData.skills && currentData.skills.length > 0) {
      skillsTextarea.value = currentData.skills.join(', ');
    }

    const unsubscribe = resumeData.subscribe(data => {
      if (data.skills) {
        const skillsString = data.skills.join(', ');
        if (skillsTextarea.value !== skillsString) {
          skillsTextarea.value = skillsString;
        }
      }
    });

    skillsTextarea.addEventListener('input', (e) => {
      const target = e.target as HTMLTextAreaElement;
      const skillsArray = target.value.split(',').map(skill => skill.trim()).filter(skill => skill);
      updateSection('skills', skillsArray);
    });

    window.addEventListener('beforeunload', unsubscribe);
  });
</script>