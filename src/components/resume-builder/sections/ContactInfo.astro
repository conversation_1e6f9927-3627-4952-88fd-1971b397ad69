---
// Form section for contact information.
---

<div class="space-y-4">
  <div>
    <label for="fullName" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Full Name</label>
    <input type="text" id="fullName" name="fullName" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
  </div>
  <div>
    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
    <input type="email" id="email" name="email" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
  </div>
  <div>
    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone</label>
    <input type="tel" id="phone" name="phone" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
  </div>
  <div>
    <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Location</label>
    <input type="text" id="location" name="location" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
  </div>
  <div>
    <label for="linkedin" class="block text-sm font-medium text-gray-700 dark:text-gray-300">LinkedIn Profile</label>
    <input type="url" id="linkedin" name="linkedin" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
  </div>
  <div>
    <label for="portfolio" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Portfolio/Website</label>
    <input type="url" id="portfolio" name="portfolio" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white">
  </div>
</div>

<script>
  import { resumeData, updateContactInfo } from "../../../lib/resumeBuilderService";

  document.addEventListener('DOMContentLoaded', () => {
    const inputs = {
      fullName: document.getElementById('fullName') as HTMLInputElement,
      email: document.getElementById('email') as HTMLInputElement,
      phone: document.getElementById('phone') as HTMLInputElement,
      location: document.getElementById('location') as HTMLInputElement,
      linkedin: document.getElementById('linkedin') as HTMLInputElement,
      portfolio: document.getElementById('portfolio') as HTMLInputElement,
    };

    // Function to update inputs from store
    const updateInputs = (data) => {
      if (data && data.contactInfo) {
        for (const key in inputs) {
          if (inputs[key] && inputs[key].value !== data.contactInfo[key]) {
            inputs[key].value = data.contactInfo[key] || '';
          }
        }
      }
    };

    // Initial population
    updateInputs(resumeData.get());

    // Subscribe to changes
    const unsubscribe = resumeData.subscribe(updateInputs);

    // Add event listeners to update store on input
    for (const key in inputs) {
      if (inputs[key]) {
        inputs[key].addEventListener('input', (e) => {
          const target = e.target as HTMLInputElement;
          updateContactInfo(key as keyof typeof inputs, target.value);
        });
      }
    }

    // Cleanup subscription on component unload
    window.addEventListener('beforeunload', unsubscribe);
  });
</script>