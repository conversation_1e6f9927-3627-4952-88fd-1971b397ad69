---
import ToggleSwitch from './ToggleSwitch.astro';
import Accordion from '../common/Accordion.astro';
---

<div class="mt-20 p-4  bg-white dark:bg-gray-800/50 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700/50">
    <div class="flex justify-between items-center mb-4">
        <h4 class="text-lg font-bold text-gray-800 dark:text-white">Customize Your Resume</h4>
        <div class="flex gap-2">
            <button id="reset-styles-btn" class="text-xs font-medium text-indigo-600 dark:text-indigo-400 hover:underline">Reset Styles</button>
        </div>
    </div>
    
    <div class="space-y-6">
        <Accordion title="Layout & Structure">
            <div class="space-y-6">
                <div>
                    <label for="layout-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Layout</label>
                    <select id="layout-select" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200">
                        <option value="one-column">One Column</option>
                        <option value="two-column">Two Column</option>
                    </select>
                </div>
                <div>
                    <label for="margins-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Margins</label>
                    <select id="margins-select" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200">
                        <option value="narrow">Narrow</option>
                        <option value="normal">Normal</option>
                        <option value="wide">Wide</option>
                    </select>
                </div>
                <div>
                    <label for="section-spacing-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Section Spacing</label>
                    <select id="section-spacing-select" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200">
                        <option value="compact">Compact</option>
                        <option value="normal">Normal</option>
                        <option value="relaxed">Relaxed</option>
                    </select>
                </div>
            </div>
        </Accordion>

        <Accordion title="Typography">
            <div class="space-y-6">
                <div>
                    <label for="font-family-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Font Family</label>
                    <select id="font-family-select" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200">
                        <option value="Arial, sans-serif">Arial</option>
                        <option value="'Times New Roman', serif">Times New Roman</option>
                        <option value="Calibri, sans-serif">Calibri</option>
                    </select>
                </div>
                <div>
                    <label for="font-size-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Body Font Size</label>
                    <select id="font-size-select" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200">
                        <option value="10pt">10pt</option>
                        <option value="11pt">11pt</option>
                        <option value="12pt">12pt</option>
                    </select>
                </div>
                <div>
                    <label for="line-spacing-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Line Spacing</label>
                    <select id="line-spacing-select" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200">
                        <option value="1.0">Single</option>
                        <option value="1.15">1.15</option>
                        <option value="1.5">1.5</option>
                    </select>
                </div>
            </div>
        </Accordion>

        <Accordion title="Content">
            <div class="space-y-6">
                <div>
                    <label for="date-format-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date Format</label>
                    <select id="date-format-select" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200">
                        <option value="short">Jan 2022</option>
                        <option value="long">January 2022</option>
                    </select>
                </div>
                <div id="section-visibility-container" class="space-y-4">
                    <h6 class="text-sm font-semibold text-gray-600 dark:text-gray-400">Section Visibility</h6>
                    <ToggleSwitch id="summary-visibility-toggle" label="Summary" data-section="summary" checked={true} />
                    <ToggleSwitch id="work-experience-visibility-toggle" label="Work Experience" data-section="workExperience" checked={true} />
                    <ToggleSwitch id="education-visibility-toggle" label="Education" data-section="education" checked={true} />
                    <ToggleSwitch id="skills-visibility-toggle" label="Skills" data-section="skills" checked={true} />
                </div>
            </div>
        </Accordion>

        <Accordion title="Heading Styles">
            <div class="space-y-6">
                <div class="space-y-4" id="heading-appearance-toggles">
                    <h6 class="text-sm font-semibold text-gray-600 dark:text-gray-400">Appearance</h6>
                    <ToggleSwitch id="heading-underline-checkbox" label="Underlined Headings" checked={false} data-setting="underlined" />
                    <ToggleSwitch id="heading-allcaps-checkbox" label="ALL CAPS HEADINGS" checked={false} data-setting="allCaps" />
                    <ToggleSwitch id="heading-rule-checkbox" label="Horizontal Rule" checked={true} data-setting="horizontalRule" />
                    <ToggleSwitch id="contact-icons-checkbox" label="Contact Icons" checked={true} data-setting="contactInfoIcons" />
                </div>
                <div>
                    <label for="h1-font-size-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Name Font Size</label>
                    <select id="h1-font-size-select" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200">
                        <option value="2rem">Large</option>
                        <option value="2.25rem">X-Large</option>
                        <option value="2.5rem">2X-Large</option>
                    </select>
                </div>
                <div>
                    <label for="h2-font-size-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Section Heading Size</label>
                    <select id="h2-font-size-select" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-200">
                        <option value="1.1rem">Small</option>
                        <option value="1.25rem">Medium</option>
                        <option value="1.4rem">Large</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Heading Color</label>
                    <div id="heading-color-options" class="flex flex-wrap gap-3">
                        <button data-color="default" class="color-option h-8 w-8 rounded-full bg-black border-2" title="Default (Black)"></button>
                        <button data-color="#4a90e2" class="color-option h-8 w-8 rounded-full bg-[#4a90e2] border-2" title="Harvard Blue"></button>
                        <button data-color="#50e3c2" class="color-option h-8 w-8 rounded-full bg-[#50e3c2] border-2" title="Modern Teal"></button>
                        <button data-color="#d0021b" class="color-option h-8 w-8 rounded-full bg-[#d0021b] border-2" title="Classic Crimson"></button>
                        <button data-color="#417505" class="color-option h-8 w-8 rounded-full bg-[#417505] border-2" title="Forest Green"></button>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Body Text Color</label>
                    <div id="body-color-options" class="flex flex-wrap gap-3">
                        <button data-color="#374151" class="color-option h-8 w-8 rounded-full bg-[#374151] border-2" title="Dark Gray"></button>
                        <button data-color="#000000" class="color-option h-8 w-8 rounded-full bg-black border-2" title="Black"></button>
                    </div>
                </div>
            </div>
        </Accordion>
    </div>
</div>

<style>
    .color-option.active {
        box-shadow: 0 0 0 2px white, 0 0 0 4px var(--active-color, #4f46e5);
    }
</style>

<script>
    import * as resumeService from '../../lib/resumeBuilderService';

    document.addEventListener('DOMContentLoaded', () => {
        // --- SELECTORS ---
        const layoutSelect = document.getElementById('layout-select') as HTMLSelectElement;
        const fontSelect = document.getElementById('font-family-select') as HTMLSelectElement;
        const fontSizeSelect = document.getElementById('font-size-select') as HTMLSelectElement;
        const lineSpacingSelect = document.getElementById('line-spacing-select') as HTMLSelectElement;
        const marginsSelect = document.getElementById('margins-select') as HTMLSelectElement;
        const sectionSpacingSelect = document.getElementById('section-spacing-select') as HTMLSelectElement;
        const dateFormatSelect = document.getElementById('date-format-select') as HTMLSelectElement;
        const h1FontSizeSelect = document.getElementById('h1-font-size-select') as HTMLSelectElement;
        const h2FontSizeSelect = document.getElementById('h2-font-size-select') as HTMLSelectElement;
        const headingColorContainer = document.getElementById('heading-color-options') as HTMLElement;
        const bodyColorContainer = document.getElementById('body-color-options') as HTMLElement;
        const headingAppearanceToggles = document.getElementById('heading-appearance-toggles') as HTMLElement;
        const sectionVisibilityContainer = document.getElementById('section-visibility-container') as HTMLElement;
        const resetBtn = document.getElementById('reset-styles-btn');

        // --- INITIALIZATION ---
        function initialize() {
            const currentSettings = resumeService.resumeData.get();
            if (!currentSettings) return;

            layoutSelect.value = currentSettings.layout;
            fontSelect.value = currentSettings.fontFamily;
            fontSizeSelect.value = currentSettings.fontSize;
            lineSpacingSelect.value = currentSettings.lineSpacing;
            marginsSelect.value = currentSettings.margins;
            sectionSpacingSelect.value = currentSettings.sectionSpacing;
            dateFormatSelect.value = currentSettings.dateFormat;
            h1FontSizeSelect.value = currentSettings.headingStyles.h1FontSize;
            h2FontSizeSelect.value = currentSettings.headingStyles.h2FontSize;

            // Initialize heading style toggles
            const headingToggles = headingAppearanceToggles.querySelectorAll('input[type="checkbox"]');
            headingToggles.forEach(toggle => {
                const setting = (toggle as HTMLElement).dataset.setting;
                if (setting === 'contactInfoIcons') {
                    (toggle as HTMLInputElement).checked = currentSettings.contactInfoIcons;
                } else if (setting && currentSettings.headingStyles && typeof currentSettings.headingStyles[setting as keyof typeof currentSettings.headingStyles] !== 'undefined') {
                    const value = currentSettings.headingStyles[setting as keyof typeof currentSettings.headingStyles];
                    if (typeof value === 'boolean') {
                        (toggle as HTMLInputElement).checked = value;
                    }
                }
            });

            // Initialize section visibility toggles
            const visibilityToggles = sectionVisibilityContainer.querySelectorAll('input[type="checkbox"]');
            visibilityToggles.forEach(toggle => {
                const section = (toggle as HTMLElement).dataset.section as keyof resumeService.ResumeData['sectionVisibility'];
                if (currentSettings.sectionVisibility && typeof currentSettings.sectionVisibility[section] !== 'undefined') {
                    (toggle as HTMLInputElement).checked = currentSettings.sectionVisibility[section];
                }
            });

            initColorPicker(headingColorContainer, currentSettings.headingColor, resumeService.setHeadingColor);
            initColorPicker(bodyColorContainer, currentSettings.bodyTextColor, (color) => resumeService.setBodyTextColor(color ?? '#000000'));
        }

        function initColorPicker(container: HTMLElement, initialColor: string | null, setterFn: (color: string | null) => void) {
            const buttons = container.querySelectorAll<HTMLButtonElement>('.color-option');
            buttons.forEach(button => {
                const color = button.dataset.color;
                button.classList.toggle('active', initialColor === color || (initialColor === null && color === 'default'));
                const activeColor = color === 'default' ? '#000000' : color;
                if(button.classList.contains('active') && activeColor) {
                    button.style.setProperty('--active-color', activeColor);
                }
            });
        }

        // --- EVENT LISTENERS ---
        layoutSelect.addEventListener('change', e => resumeService.setLayout((e.target as HTMLSelectElement).value as any));
        fontSelect.addEventListener('change', e => resumeService.setFontFamily((e.target as HTMLSelectElement).value));
        fontSizeSelect.addEventListener('change', e => resumeService.setFontSize((e.target as HTMLSelectElement).value));
        lineSpacingSelect.addEventListener('change', e => resumeService.setLineSpacing((e.target as HTMLSelectElement).value));
        marginsSelect.addEventListener('change', e => resumeService.setMargins((e.target as HTMLSelectElement).value));
        sectionSpacingSelect.addEventListener('change', e => resumeService.setSectionSpacing((e.target as HTMLSelectElement).value));
        dateFormatSelect.addEventListener('change', e => resumeService.setDateFormat((e.target as HTMLSelectElement).value));
        h1FontSizeSelect.addEventListener('change', e => resumeService.setHeadingStyle('h1FontSize', (e.target as HTMLSelectElement).value));
        h2FontSizeSelect.addEventListener('change', e => resumeService.setHeadingStyle('h2FontSize', (e.target as HTMLSelectElement).value));
        
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                if(confirm('Are you sure you want to reset all style settings to their defaults?')) {
                    resumeService.resetStyles();
                }
            });
        }

        headingAppearanceToggles.addEventListener('change', e => {
            const target = e.target as HTMLInputElement;
            if (target.type === 'checkbox' && target.dataset.setting) {
                const setting = target.dataset.setting;
                if (setting === 'contactInfoIcons') {
                    resumeService.setContactInfoIcons(target.checked);
                } else {
                    resumeService.setHeadingStyle(setting as keyof resumeService.ResumeData['headingStyles'], target.checked);
                }
            }
        });

        function setupColorPickerListener(container: HTMLElement, setterFn: (color: string | null) => void) {
            container.addEventListener('click', e => {
                const button = (e.target as HTMLElement).closest<HTMLButtonElement>('.color-option');
                if (!button) return;
                container.querySelector('.active')?.classList.remove('active');
                button.classList.add('active');
                const color = button.dataset.color;
                setterFn(color === 'default' ? null : (color ?? null));
                const activeColor = color === 'default' ? '#000000' : color;
                if (activeColor) {
                    button.style.setProperty('--active-color', activeColor);
                }
            });
        }

        setupColorPickerListener(headingColorContainer, resumeService.setHeadingColor);
        setupColorPickerListener(bodyColorContainer, (color) => resumeService.setBodyTextColor(color ?? '#000000'));

        sectionVisibilityContainer.addEventListener('change', e => {
            const target = e.target as HTMLInputElement;
            if (target.type === 'checkbox' && target.dataset.section) {
                const section = target.dataset.section as keyof resumeService.ResumeData['sectionVisibility'];
                resumeService.setSectionVisibility(section, target.checked);
            }
        });

        // Re-initialize on store change
        resumeService.resumeData.subscribe(initialize);

        initialize();
    });
</script>
