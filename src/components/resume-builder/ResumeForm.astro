---
import ContactInfo from "./sections/ContactInfo.astro";
import Summary from "./sections/Summary.astro";
import WorkExperience from "./sections/WorkExperience.astro";
import Education from "./sections/Education.astro";
import Skills from "./sections/Skills.astro";
import CustomSection from "./sections/CustomSection.astro";
---

<div class="space-y-4">
  <!-- Accordion Item 1: Contact Info -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button class="accordion-button relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-t-2xl" type="button">
        Contact Information
        <span class="icon-[lucide--chevron-down] ml-auto h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
      </button>
    </h2>
    <div class="accordion-collapse">
      <div class="accordion-body py-4 px-5">
        <ContactInfo />
      </div>
    </div>
  </div>

  <!-- Accordion Item 2: Summary -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button class="accordion-button relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-t-2xl" type="button">
        Summary / Objective
        <span class="icon-[lucide--chevron-down] ml-auto h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
      </button>
    </h2>
    <div class="accordion-collapse show">
      <div class="accordion-body py-4 px-5">
        <Summary />
      </div>
    </div>
  </div>

  <!-- Accordion Item 3: Work Experience -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button class="accordion-button relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-t-2xl" type="button">
        Work Experience
        <span class="icon-[lucide--chevron-down] ml-auto h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
      </button>
    </h2>
    <div class="accordion-collapse show">
      <div class="accordion-body py-4 px-5">
        <WorkExperience />
      </div>
    </div>
  </div>

  <!-- Accordion Item 4: Education -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button class="accordion-button relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-t-2xl" type="button">
        Education
        <span class="icon-[lucide--chevron-down] ml-auto h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
      </button>
    </h2>
    <div class="accordion-collapse show">
      <div class="accordion-body py-4 px-5">
        <Education />
      </div>
    </div>
  </div>

  <!-- Accordion Item 5: Skills -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button class="accordion-button relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-t-2xl" type="button">
        Skills
        <span class="icon-[lucide--chevron-down] ml-auto h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
      </button>
    </h2>
    <div class="accordion-collapse show">
      <div class="accordion-body py-4 px-5">
        <Skills />
      </div>
    </div>
  </div>

  <!-- Accordion Item 6: Custom Sections -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button class="accordion-button collapsed relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-2xl" type="button">
        Add Custom Section
        <span class="icon-[lucide--chevron-down] ml-auto h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
      </button>
    </h2>
    <div class="accordion-collapse">
      <div class="accordion-body py-4 px-5">
        <CustomSection />
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const accordionButtons = document.querySelectorAll('.accordion-button');

    const slideDown = (element) => {
      element.style.display = 'block';
      const height = element.scrollHeight;
      element.style.height = '0px';
      element.style.overflow = 'hidden';
      element.style.transition = 'height 0.3s ease-in-out, opacity 0.3s ease-in-out';
      element.style.opacity = '0';
      setTimeout(() => {
        element.style.height = `${height}px`;
        element.style.opacity = '1';
      }, 0);
      setTimeout(() => {
        element.style.height = '';
        element.style.overflow = '';
      }, 300);
    };

    const slideUp = (element) => {
      const height = element.scrollHeight;
      element.style.height = `${height}px`;
      element.style.overflow = 'hidden';
      element.style.transition = 'height 0.3s ease-in-out, opacity 0.3s ease-in-out';
      element.style.opacity = '1';
      setTimeout(() => {
        element.style.height = '0px';
        element.style.opacity = '0';
      }, 0);
      setTimeout(() => {
        element.style.display = 'none';
        element.style.height = '';
        element.style.overflow = '';
      }, 300);
    };

    accordionButtons.forEach(button => {
      const parent = button.parentElement;
      if (!parent) return;
      
      const collapse = parent.nextElementSibling as HTMLElement;
      if (!collapse) return;

      const icon = button.querySelector('span');

      // By default, all sections except the custom one are open.
      if (!button.classList.contains('collapsed')) {
        collapse.style.display = 'block';
        if (icon) icon.style.transform = 'rotate(0deg)';
      } else {
        if (icon) icon.style.transform = 'rotate(-90deg)';
      }

      button.addEventListener('click', () => {
        // Close all other accordion items
        accordionButtons.forEach(otherButton => {
          if (otherButton !== button) {
            const parent = otherButton.parentElement;
            if (parent) {
              const otherCollapse = parent.nextElementSibling as HTMLElement;
              const otherIcon = otherButton.querySelector('span');
              if (otherCollapse && !otherButton.classList.contains('collapsed')) {
                otherButton.classList.add('collapsed');
                slideUp(otherCollapse);
                if (otherIcon) otherIcon.style.transform = 'rotate(-90deg)';
              }
            }
          }
        });

        // Toggle the clicked accordion item
        const isCollapsed = button.classList.contains('collapsed');
        if (isCollapsed) {
          button.classList.remove('collapsed');
          slideDown(collapse);
          if (icon) icon.style.transform = 'rotate(0deg)';
        } else {
          button.classList.add('collapsed');
          slideUp(collapse);
          if (icon) icon.style.transform = 'rotate(-90deg)';
        }
      });
    });
  });
</script>

<style>
  .accordion-button.collapsed .icon-[lucide--chevron-down] {
    transform: rotate(-90deg);
  }
  .accordion-collapse {
    display: none;
  }
</style>