---
import ContactInfo from "./sections/ContactInfo.astro";
import Summary from "./sections/Summary.astro";
import WorkExperience from "./sections/WorkExperience.astro";
import Education from "./sections/Education.astro";
import Skills from "./sections/Skills.astro";
import CustomSection from "./sections/CustomSection.astro";
---

<div class="space-y-4">
  <!-- Accordion Item 1: Contact Info -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button class="accordion-button relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-t-2xl" type="button">
        <div class="flex items-center">
          <span class="w-6 h-6 bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center text-sm font-bold mr-3">1</span>
          Contact Information
        </div>
        <div class="flex items-center ml-auto">
          <span id="contact-status" class="w-2 h-2 bg-gray-300 rounded-full mr-2"></span>
          <span class="icon-[lucide--chevron-down] h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
        </div>
      </button>
    </h2>
    <div class="accordion-collapse show">
      <div class="accordion-body py-4 px-5">
        <ContactInfo />
      </div>
    </div>
  </div>

  <!-- Accordion Item 2: Summary -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button class="accordion-button collapsed relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-2xl" type="button">
        <div class="flex items-center">
          <span class="w-6 h-6 bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center text-sm font-bold mr-3">2</span>
          Summary / Objective
        </div>
        <div class="flex items-center ml-auto">
          <span id="summary-status" class="w-2 h-2 bg-gray-300 rounded-full mr-2"></span>
          <span class="icon-[lucide--chevron-down] h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
        </div>
      </button>
    </h2>
    <div class="accordion-collapse">
      <div class="accordion-body py-4 px-5">
        <Summary />
      </div>
    </div>
  </div>

  <!-- Accordion Item 3: Work Experience -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button class="accordion-button collapsed relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-2xl" type="button">
        <div class="flex items-center">
          <span class="w-6 h-6 bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center text-sm font-bold mr-3">3</span>
          Work Experience
        </div>
        <div class="flex items-center ml-auto">
          <span id="work-status" class="w-2 h-2 bg-gray-300 rounded-full mr-2"></span>
          <span class="icon-[lucide--chevron-down] h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
        </div>
      </button>
    </h2>
    <div class="accordion-collapse">
      <div class="accordion-body py-4 px-5">
        <WorkExperience />
      </div>
    </div>
  </div>

  <!-- Accordion Item 4: Education -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button class="accordion-button collapsed relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-2xl" type="button">
        <div class="flex items-center">
          <span class="w-6 h-6 bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center text-sm font-bold mr-3">4</span>
          Education
        </div>
        <div class="flex items-center ml-auto">
          <span id="education-status" class="w-2 h-2 bg-gray-300 rounded-full mr-2"></span>
          <span class="icon-[lucide--chevron-down] h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
        </div>
      </button>
    </h2>
    <div class="accordion-collapse">
      <div class="accordion-body py-4 px-5">
        <Education />
      </div>
    </div>
  </div>

  <!-- Accordion Item 5: Skills -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button class="accordion-button collapsed relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-2xl" type="button">
        <div class="flex items-center">
          <span class="w-6 h-6 bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center text-sm font-bold mr-3">5</span>
          Skills
        </div>
        <div class="flex items-center ml-auto">
          <span id="skills-status" class="w-2 h-2 bg-gray-300 rounded-full mr-2"></span>
          <span class="icon-[lucide--chevron-down] h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
        </div>
      </button>
    </h2>
    <div class="accordion-collapse">
      <div class="accordion-body py-4 px-5">
        <Skills />
      </div>
    </div>
  </div>

  <!-- Accordion Item 6: Custom Sections -->
  <div class="accordion-item bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg">
    <h2 class="accordion-header mb-0">
      <button class="accordion-button collapsed relative flex items-center w-full py-4 px-5 text-base text-gray-800 dark:text-white text-left rounded-2xl" type="button">
        Add Custom Section
        <span class="icon-[lucide--chevron-down] ml-auto h-5 w-5 shrink-0 transition-transform duration-200 ease-in-out"></span>
      </button>
    </h2>
    <div class="accordion-collapse">
      <div class="accordion-body py-4 px-5">
        <CustomSection />
      </div>
    </div>
  </div>
</div>

<script>
  import { resumeData } from '../../lib/resumeBuilderService';

  document.addEventListener('DOMContentLoaded', () => {
    const accordionButtons = document.querySelectorAll('.accordion-button');

    const slideDown = (element: HTMLElement) => {
      element.style.display = 'block';
      const height = element.scrollHeight;
      element.style.height = '0px';
      element.style.overflow = 'hidden';
      element.style.transition = 'height 0.3s ease-in-out, opacity 0.3s ease-in-out';
      element.style.opacity = '0';
      setTimeout(() => {
        element.style.height = `${height}px`;
        element.style.opacity = '1';
      }, 0);
      setTimeout(() => {
        element.style.height = '';
        element.style.overflow = '';
      }, 300);
    };

    const slideUp = (element: HTMLElement) => {
      const height = element.scrollHeight;
      element.style.height = `${height}px`;
      element.style.overflow = 'hidden';
      element.style.transition = 'height 0.3s ease-in-out, opacity 0.3s ease-in-out';
      element.style.opacity = '1';
      setTimeout(() => {
        element.style.height = '0px';
        element.style.opacity = '0';
      }, 0);
      setTimeout(() => {
        element.style.display = 'none';
        element.style.height = '';
        element.style.overflow = '';
      }, 300);
    };

    // Section completion tracking
    const updateSectionStatus = (sectionName: string, isComplete: boolean) => {
      const statusElement = document.getElementById(`${sectionName}-status`);
      if (statusElement) {
        statusElement.className = isComplete
          ? 'w-2 h-2 bg-green-500 rounded-full mr-2'
          : 'w-2 h-2 bg-gray-300 rounded-full mr-2';
      }
    };

    // Check section completion
    const checkSectionCompletion = (data: any) => {
      if (data.contactInfo) {
        const hasRequiredContact = data.contactInfo.fullName && data.contactInfo.email;
        updateSectionStatus('contact', hasRequiredContact);
      }

      if (data.summary) {
        updateSectionStatus('summary', data.summary.trim().length > 0);
      }

      if (data.workExperience) {
        updateSectionStatus('work', data.workExperience.length > 0);
      }

      if (data.education) {
        updateSectionStatus('education', data.education.length > 0);
      }

      if (data.skills) {
        updateSectionStatus('skills', data.skills.length > 0);
      }
    };

    // Subscribe to data changes
    const unsubscribe = resumeData.subscribe(checkSectionCompletion);

    // Initial check
    checkSectionCompletion(resumeData.get());

    // Cleanup subscription on component unload
    window.addEventListener('beforeunload', unsubscribe);

    accordionButtons.forEach(button => {
      const parent = button.parentElement;
      if (!parent) return;

      const collapse = parent.nextElementSibling as HTMLElement;
      if (!collapse) return;

      const icon = button.querySelector('span');

      // By default, all sections except the custom one are open.
      if (!button.classList.contains('collapsed')) {
        collapse.style.display = 'block';
        if (icon) icon.style.transform = 'rotate(0deg)';
      } else {
        if (icon) icon.style.transform = 'rotate(-90deg)';
      }

      button.addEventListener('click', () => {
        // Close all other accordion items
        accordionButtons.forEach(otherButton => {
          if (otherButton !== button) {
            const parent = otherButton.parentElement;
            if (parent) {
              const otherCollapse = parent.nextElementSibling as HTMLElement;
              const otherIcon = otherButton.querySelector('span');
              if (otherCollapse && !otherButton.classList.contains('collapsed')) {
                otherButton.classList.add('collapsed');
                slideUp(otherCollapse);
                if (otherIcon) otherIcon.style.transform = 'rotate(-90deg)';
              }
            }
          }
        });

        // Toggle the clicked accordion item
        const isCollapsed = button.classList.contains('collapsed');
        if (isCollapsed) {
          button.classList.remove('collapsed');
          slideDown(collapse);
          if (icon) icon.style.transform = 'rotate(0deg)';
        } else {
          button.classList.add('collapsed');
          slideUp(collapse);
          if (icon) icon.style.transform = 'rotate(-90deg)';
        }
      });
    });
  });
</script>

<style>
  .accordion-button.collapsed .icon-[lucide--chevron-down] {
    transform: rotate(-90deg);
  }
  .accordion-collapse {
    display: none;
  }
</style>