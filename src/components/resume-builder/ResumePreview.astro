---
// This component will display a live preview of the resume.
---

<div>
    <div class="mb-4">
        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-3">
            Preview
        </h3>
        <div id="preview-buttons" class="flex flex-wrap gap-2">
            <button id="sample-data-btn" class="inline-flex items-center gap-1.5 text-xs font-bold px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 shadow-sm bg-green-50 text-green-700 dark:bg-green-900/40 dark:text-green-300 hover:bg-green-100 dark:hover:bg-green-800/50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span>Sample</span>
            </button>
            <button id="clear-data-btn" class="inline-flex items-center gap-1.5 text-xs font-bold px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 shadow-sm bg-red-50 text-red-700 dark:bg-red-900/40 dark:text-red-300 hover:bg-red-100 dark:hover:bg-red-800/50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                <span>Clear</span>
            </button>
            <button id="download-pdf-btn" class="inline-flex items-center gap-1.5 text-xs font-bold px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 shadow-sm bg-blue-50 text-blue-700 dark:bg-blue-900/40 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800/50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                <span>Download</span>
            </button>
            <div id="customize-toggle-buttons">
                <button id="customize-btn" class="inline-flex items-center gap-1.5 text-xs font-bold px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 shadow-sm bg-purple-50 text-purple-700 dark:bg-purple-900/40 dark:text-purple-300 hover:bg-purple-100 dark:hover:bg-purple-800/50">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span>Customize</span>
                </button>
                <button id="back-to-edit-btn" class="hidden w-[200px] items-center gap-1.5 text-xs font-bold px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 shadow-sm bg-gray-50 text-gray-700 dark:bg-gray-700/40 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800/50">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M11 15l-3-3m0 0l3-3m-3 3h8M3 12a9 9 0 1118 0 9 9 0 01-18 0z"></path>
                    </svg>
                    <span>Back to Edit</span>
                </button>
            </div>
        </div>
    </div>
    <div class="bg-white dark:bg-gray-800/50 focus:outline-none">
        <div id="resume-preview-content" class="prose prose-sm dark:prose-invert max-w-full" data-template="reverse-chronological">
            <!-- The formatted resume content will be rendered here -->
        </div>
    </div>
</div>

    <style>
        #resume-preview-content {
            aspect-ratio: 210 / 297;
            max-height: 100vh;
            width: 100%;
            max-width: 50rem; /* A4-like max width */
            margin-left: auto;
            margin-right: auto;
            background: #fff;
            border: 1px solid #e5e7eb;
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
        }
        .two-column .main-content {
            grid-column: 2 / 3;
        }
        .two-column .sidebar {
            grid-column: 1 / 2;
        }
    </style>

    <script>
        import { resumeData, moveSection, clearResumeData, loadSampleData } from '../../lib/resumeBuilderService';

        // Ambient type declarations for CDN libraries
        declare global {
            interface Window {
                jspdf: any;
                html2canvas: any;
            }
        }

        // Add CDN links for jsPDF and html2canvas
        const jspdfScript = document.createElement('script');
        jspdfScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
        document.head.appendChild(jspdfScript);

        const html2canvasScript = document.createElement('script');
        html2canvasScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
        document.head.appendChild(html2canvasScript);

        document.addEventListener('DOMContentLoaded', () => {
            const previewContent = document.getElementById('resume-preview-content');
            const downloadBtn = document.getElementById('download-pdf-btn') as HTMLButtonElement;
            const clearBtn = document.getElementById('clear-data-btn');
            const sampleBtn = document.getElementById('sample-data-btn');
            if (!previewContent || !downloadBtn || !clearBtn || !sampleBtn) return;

            const renderPreview = (data) => {
                // Apply dynamic styles
                const styleTag = document.getElementById('resume-dynamic-styles') || document.createElement('style');
                styleTag.id = 'resume-dynamic-styles';
                styleTag.innerHTML = `
                    #resume-preview-content {
                        font-family: ${data.fontFamily};
                        font-size: ${data.fontSize};
                        line-height: ${data.lineSpacing};
                        color: ${data.bodyTextColor};
                    }
                    #resume-preview-content h1 {
                        font-size: ${data.headingStyles.h1FontSize};
                        font-weight: ${data.headingStyles.h1FontWeight};
                    }
                    #resume-preview-content h2 {
                        font-size: ${data.headingStyles.h2FontSize};
                        font-weight: ${data.headingStyles.h2FontWeight};
                    }
                    #resume-preview-content h1,
                    #resume-preview-content h2,
                    #resume-preview-content h3 {
                        color: ${data.headingColor || data.bodyTextColor};
                        text-transform: ${data.headingStyles.allCaps ? 'uppercase' : 'none'};
                        text-decoration: ${data.headingStyles.underlined ? 'underline' : 'none'};
                        border-bottom: ${data.headingStyles.horizontalRule ? `1px solid ${data.bodyTextColor}` : 'none'};
                        padding-bottom: ${data.headingStyles.horizontalRule ? '0.25rem' : '0'};
                    }
                    #resume-preview-content .section-margin {
                        margin-bottom: ${data.sectionSpacing === 'compact' ? '0.5rem' : data.sectionSpacing === 'relaxed' ? '1.5rem' : '1rem'};
                    }
                    #resume-preview-content ul {
                        list-style-position: inside;
                        padding-left: 0.5rem;
                    }
                `;
                document.head.appendChild(styleTag);

                previewContent.style.padding = data.margins === 'narrow' ? '1.5rem' : data.margins === 'wide' ? '3.5rem' : '2.5rem';
                previewContent.classList.toggle('two-column', data.layout === 'two-column');

                const formatDate = (dateStr) => {
                    if (!dateStr) return '';
                    const [year, month] = dateStr.split('-');
                    const date = new Date(parseInt(year), parseInt(month) - 1);
                    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: data.dateFormat === 'short' ? 'short' : 'long' };
                    return date.toLocaleDateString('en-US', options);
                };

                const sections = {
                    contactInfo: `
                        <div class="mb-6" style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                            <h1 class="font-bold">${data.contactInfo.fullName}</h1>
                            <p>
                                ${data.contactInfoIcons ? '📧 ' : ''}${data.contactInfo.email} |
                                ${data.contactInfoIcons ? '📞 ' : ''}${data.contactInfo.phone} |
                                ${data.contactInfoIcons ? '📍 ' : ''}${data.contactInfo.location}
                            </p>
                            <p>
                                <a href="${data.contactInfo.linkedin}" target="_blank" rel="noopener noreferrer">${data.contactInfoIcons ? '🔗 ' : ''}LinkedIn</a> |
                                <a href="${data.contactInfo.portfolio}" target="_blank" rel="noopener noreferrer">${data.contactInfoIcons ? '💼 ' : ''}Portfolio</a>
                            </p>
                        </div>
                    `,
                    summary: data.sectionVisibility.summary ? `
                        <div class="section-margin">
                            <h2 class="font-semibold pb-2">Summary</h2>
                            ${data.summary ? `<p>${data.summary}</p>` : `<p class="text-gray-500 italic">Click "Summary / Objective" above to add your professional summary</p>`}
                        </div>
                    ` : '',
                    workExperience: data.sectionVisibility.workExperience ? `
                        <div class="section-margin work-experience"><h2 class="font-semibold pb-2">Work Experience</h2>
                        ${data.workExperience.map(job => `
                            <div class="mt-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="font-bold">${job.jobTitle || 'Job Title'}</h3>
                                        <p class="italic">${job.company || 'Company Name'}</p>
                                    </div>
                                    <div class="text-right">
                                        ${job.location ? `<p class="italic">${job.location}</p>` : ''}
                                        <p class="text-sm text-gray-600">${formatDate(job.startDate)} - ${job.endDate === 'Present' ? 'Present' : formatDate(job.endDate)}</p>
                                    </div>
                                </div>
                                <ul class="list-disc pl-5 mt-2">
                                  ${(job.bulletPoints && job.bulletPoints.length > 0 ? job.bulletPoints.map(bp => `<li>${bp.text || 'Bullet point description'}</li>`).join('') : `<li>Job description and achievements</li>`)}
                                </ul>
                            </div>
                        `).join('') || `<p class="text-gray-500 italic mt-4">Click "Work Experience" above to add your work history</p>`}
                        </div>
                    ` : '',
                    education: data.sectionVisibility.education ? `
                        <div class="section-margin"><h2 class="font-semibold pb-2">Education</h2>
                        ${data.education.map(edu => `
                            <div class="mt-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="font-bold">${edu.institution || 'Institution Name'}</h3>
                                        <p class="italic">${edu.degree || 'Degree'}, ${edu.fieldOfStudy || 'Field of Study'}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm text-gray-600">${formatDate(edu.graduationDate)}</p>
                                    </div>
                                </div>
                                <p>${edu.details || 'Additional details about your education'}</p>
                            </div>
                        `).join('') || `<p class="text-gray-500 italic mt-4">Click "Education" above to add your educational background</p>`}
                        </div>
                    ` : '',
                    skills: data.sectionVisibility.skills ? `
                        <div class="section-margin">
                            <h2 class="font-semibold pb-2">Skills</h2>
                            ${data.skills.length > 0 ? `<p>${data.skills.join(', ')}</p>` : `<p class="text-gray-500 italic">Click "Skills" above to add your technical and soft skills</p>`}
                        </div>
                    ` : '',
                    customSections: data.customSections.filter(s => s.visible).map(section => `
                        <div class="section-margin">
                            <h2 class="font-semibold pb-2">${section.title}</h2>
                            <div class="prose">${section.content}</div>
                        </div>
                    `).join('')
                };

                let mainContentHtml = '';
                let sidebarHtml = '';

                if (data.layout === 'two-column') {
                    sidebarHtml += sections.contactInfo;
                    if (data.sectionVisibility.skills) sidebarHtml += sections.skills;

                    const mainSections = data.sectionsOrder.filter(s => s !== 'skills');
                    mainSections.forEach(sectionName => {
                        if (sections[sectionName] && sectionName !== 'contactInfo') {
                            mainContentHtml += sections[sectionName];
                        }
                    });
                    previewContent.innerHTML = `<div class="sidebar">${sidebarHtml}</div><div class="main-content">${mainContentHtml}</div>`;
                } else {
                    let html = sections.contactInfo;
                    data.sectionsOrder.forEach(sectionName => {
                        if (sections[sectionName] && sectionName !== 'contactInfo') {
                            html += sections[sectionName];
                        }
                    });
                    previewContent.innerHTML = html;
                }
            };

            const unsubscribe = resumeData.subscribe(renderPreview);

            downloadBtn.addEventListener('click', async () => {
                if (typeof window.jspdf === 'undefined' || typeof window.html2canvas === 'undefined') {
                    console.error("jsPDF or html2canvas is not loaded yet.");
                    alert("PDF generation library is still loading. Please try again in a moment.");
                    return;
                }
                const { jsPDF } = window.jspdf;

                try {
                    downloadBtn.disabled = true;
                    const downloadButtonSpan = downloadBtn.querySelector('span');
                    if (downloadButtonSpan) {
                        downloadButtonSpan.textContent = 'Generating...';
                    }

                    const canvas = await window.html2canvas(previewContent, {
                        scale: 2,
                        useCORS: true,
                        logging: false,
                    });

                    const imgData = canvas.toDataURL('image/png');
                    const pdf = new jsPDF({
                        orientation: 'portrait',
                        unit: 'pt',
                        format: 'a4'
                    });

                    const pdfWidth = pdf.internal.pageSize.getWidth();
                    const pdfHeight = pdf.internal.pageSize.getHeight();

                    // The canvas has been scaled up by a factor of 2, so we need to account for that
                    const canvasWidth = canvas.width;
                    const canvasHeight = canvas.height;

                    // Calculate the aspect ratio of the canvas
                    const canvasAspectRatio = canvasWidth / canvasHeight;

                    // Set the width of the image on the PDF to be the full width of the page
                    const renderWidth = pdfWidth;
                    // Calculate the height based on the aspect ratio to maintain proportions
                    const renderHeight = renderWidth / canvasAspectRatio;

                    // If the rendered height is greater than the PDF height, it will create multiple pages (which is fine for a resume)
                    // We will add the image at the top-left corner (0, 0)
                    pdf.addImage(imgData, 'PNG', 0, 0, renderWidth, renderHeight);
                    pdf.save('resume.pdf');

                } catch (error) {
                    console.error("Error generating PDF:", error);
                    alert("Failed to generate PDF. Please try again.");
                } finally {
                    downloadBtn.disabled = false;
                    const downloadButtonSpan = downloadBtn.querySelector('span');
                    if (downloadButtonSpan) {
                        downloadButtonSpan.textContent = 'Download';
                    }
                }
            });

            clearBtn.addEventListener('click', () => {
                if (confirm('Are you sure you want to clear all data and start with a blank resume?')) {
                    clearResumeData();
                }
            });

            sampleBtn.addEventListener('click', () => {
                loadSampleData();
            });

            window.addEventListener('beforeunload', unsubscribe);
        });
    </script>