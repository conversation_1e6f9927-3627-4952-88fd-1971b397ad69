---
interface Props {
  title?: string;
  subtitle?: string;
}

const {
  title = "Contact Us",
  subtitle = "Send us a message and we'll get back to you as soon as possible"
} = Astro.props;
---

<!-- Contact Modal -->
<div id="contactModal" class="fixed inset-0 z-50 opacity-0 pointer-events-none overflow-y-auto transition-opacity duration-300 ease-in-out">
  <div class="flex items-center justify-center min-h-screen p-4">
    <!-- Backdrop -->
    <div id="contactModalBackdrop" class="fixed inset-0 bg-black/50 backdrop-blur-sm opacity-0 transition-opacity duration-300 ease-in-out"></div>

    <!-- Modal Content -->
    <div class="relative bg-white dark:bg-gray-900 rounded-xl shadow-xl max-w-md w-full mx-auto transform translate-y-8 opacity-0 transition-all duration-300 ease-in-out">
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold text-gray-900 dark:text-white">{title}</h3>
          <button id="closeContactModal" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <p class="text-gray-600 dark:text-gray-300 mb-4">
          {subtitle}
        </p>

        <form id="contactForm" class="space-y-4">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Your Name</label>
            <input
              type="text"
              id="name"
              name="name"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white"
              placeholder="Enter your name"
              required
            />
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email Address</label>
            <input
              type="email"
              id="email"
              name="email"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white"
              placeholder="Enter your email"
              required
            />
          </div>

          <div>
            <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Subject</label>
            <input
              type="text"
              id="subject"
              name="subject"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white"
              placeholder="What is your message about?"
              required
            />
          </div>

          <div>
            <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Message</label>
            <textarea
              id="message"
              name="message"
              rows="4"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white"
              placeholder="How can we help you?"
              required
            ></textarea>
          </div>

          <div class="pt-2">
            <button
              type="submit"
              id="submitContact"
              class="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent rounded-lg shadow-sm font-medium text-white bg-black dark:bg-white dark:text-black hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-150"
            >
              Send Message
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </form>

        <div id="contactStatus" class="mt-4 hidden">
          <div id="successMessage" class="p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hidden">
            Thank you for your message! We'll get back to you soon.
          </div>
          <div id="errorMessage" class="p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-lg hidden">
            There was an error sending your message. Please try again.
          </div>
        </div>

        <p class="text-sm text-gray-500 dark:text-gray-400 mt-6 text-center">
          Our team typically responds within 24-48 business hours.
        </p>
      </div>
    </div>
  </div>
</div>

<script>
  // Define the global function type
  declare global {
    interface Window {
      openContactModal: () => void;
    }
  }

  document.addEventListener('DOMContentLoaded', () => {
    const modal = document.getElementById('contactModal');
    const backdrop = document.getElementById('contactModalBackdrop');
    const closeButton = document.getElementById('closeContactModal');
    const form = document.getElementById('contactForm') as HTMLFormElement;
    const submitButton = document.getElementById('submitContact') as HTMLButtonElement;
    const contactStatus = document.getElementById('contactStatus');
    const successMessage = document.getElementById('successMessage');
    const errorMessage = document.getElementById('errorMessage');

    // Check if all elements exist
    if (!modal || !backdrop || !closeButton || !form || !submitButton || !contactStatus || !successMessage || !errorMessage) {
      console.error('Contact modal: One or more required elements not found');
      return;
    }

    // Open modal function with transition
    function openContactModal() {
      if (!modal || !backdrop) return;

      // First make the modal visible but still transparent
      modal.classList.remove('pointer-events-none');

      // Force a reflow to ensure the transition works
      void modal.offsetWidth;

      // Add overflow hidden to body
      document.body.classList.add('overflow-hidden');

      // Fade in the modal and backdrop
      modal.classList.add('opacity-100');
      backdrop.classList.add('opacity-100');

      // Animate in the modal content
      const modalContent = modal.querySelector('.relative');
      if (modalContent) {
        setTimeout(() => {
          modalContent.classList.remove('translate-y-8', 'opacity-0');
        }, 50); // Small delay to ensure the transition works
      }
    }

    // Close modal function with transition
    function closeContactModal() {
      if (!modal || !backdrop || !contactStatus || !successMessage || !errorMessage) return;

      // Fade out the modal and backdrop
      modal.classList.remove('opacity-100');
      backdrop.classList.remove('opacity-100');

      // Animate out the modal content
      const modalContent = modal.querySelector('.relative');
      if (modalContent) {
        modalContent.classList.add('translate-y-8', 'opacity-0');
      }

      // Wait for the transition to complete before hiding completely
      setTimeout(() => {
        modal.classList.add('pointer-events-none');
        document.body.classList.remove('overflow-hidden');

        // Reset form and messages
        form.reset();
        contactStatus.classList.add('hidden');
        successMessage.classList.add('hidden');
        errorMessage.classList.add('hidden');
      }, 300); // Match the duration in the CSS
    }

    // Close modal when clicking close button
    closeButton.addEventListener('click', closeContactModal);

    // Close modal when clicking outside
    modal.addEventListener('click', (e: MouseEvent) => {
      // Only close if clicking directly on the modal wrapper (not its children)
      if (e.target === modal) {
        closeContactModal();
      }
    });

    // Handle form submission
    form.addEventListener('submit', async (e: SubmitEvent) => {
      e.preventDefault();

      if (!submitButton || !contactStatus || !successMessage || !errorMessage) return;

      // Disable submit button and show loading state
      submitButton.disabled = true;
      submitButton.innerHTML = `
        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white dark:text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Sending...
      `;

      // Get form data
      const formData = new FormData(form);
      const name = formData.get('name');
      const email = formData.get('email');
      const subject = formData.get('subject');
      const message = formData.get('message');

      try {
        // Send data to Netlify function
        const response = await fetch('/.netlify/functions/contact-form', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name,
            email,
            subject,
            message
          }),
        });

        const result = await response.json();

        // Show appropriate message
        contactStatus.classList.remove('hidden');
        if (response.ok) {
          successMessage.classList.remove('hidden');
          errorMessage.classList.add('hidden');

          // Auto close after success (2 seconds)
          setTimeout(() => {
            closeContactModal();
          }, 2000);
        } else {
          successMessage.classList.add('hidden');
          errorMessage.classList.remove('hidden');
          errorMessage.textContent = result.error || 'There was an error sending your message. Please try again.';
        }
      } catch (error) {
        // Show error message
        contactStatus.classList.remove('hidden');
        successMessage.classList.add('hidden');
        errorMessage.classList.remove('hidden');
        errorMessage.textContent = 'Network error. Please check your connection and try again.';
        console.error('Contact submission error:', error);
      } finally {
        // Reset button state
        submitButton.disabled = false;
        submitButton.innerHTML = `
          Send Message
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        `;
      }
    });

    // Make the openContactModal function available globally
    window.openContactModal = openContactModal;
  });
</script>
