---
import Container from "./Container.astro";
---

<div id="features">
  <Container>
    <div class="text-center mb-16">
      <h2
        class="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl mb-6"
      >
        Empower Your Job Search with Our Advanced Tools
      </h2>
      <p class="text-gray-600 dark:text-gray-300 max-w-3xl mx-auto text-xl">
        Unlock the full potential of your job search journey with our suite of tools. From crafting the perfect resume to acing your
        interview, we provide everything you need to stand out and succeed in
        your career path.
      </p>
    </div>

    <script>
      import { isAuthenticated } from "../lib/authStore";

      // Intercept tool link clicks to check authentication
      document.addEventListener("DOMContentLoaded", () => {
        // Find all tool links in features section
        const toolLinks = document.querySelectorAll("[data-tool-href]");

        toolLinks.forEach((link) => {
          link.addEventListener("click", async (e) => {
            e.preventDefault();

            // Get the target URL
            const targetUrl = link.getAttribute("data-tool-href");

            // Check authentication
            const authenticated = await isAuthenticated();

            if (!targetUrl) return;

            if (authenticated) {
              // User is authenticated, navigate directly to the tool
              window.location.href = targetUrl;
            } else {
              // User is not authenticated, redirect to login with return URL
              const encodedRedirect = encodeURIComponent(targetUrl);
              window.location.href = `/login?redirect=${encodedRedirect}`;
            }
          });
        });
      });
    </script>
  </Container>
</div>

<Container>
  <!-- Job research -->
  <section>
    <div
      class="mx-auto space-y-16 bg-purple-50 dark:bg-gray-950 p-4 sm:p-8 border border-purple-400 dark:border-purple-600 rounded-3xl shadow-[0_4px_16px_0_rgba(168,85,247,0.16)] dark:shadow-[0_4px_16px_0_rgba(168,85,247,0.22)]"
    >
      <h2
        class="text-black dark:text-white relative z-10 max-w-xl text-4xl font-medium lg:text-5xl"
      >
        Job Research made simple.
      </h2>
      <div class="grid gap-6 sm:grid-cols-2 md:gap-12 lg:gap-24">
        <!-- Description-->
        <div class="relative z-10 space-y-4">
          <p class="text-black dark:text-white">
            Gain a comprehensive understanding of job roles, required skills,
            salary ranges, company culture, and career growth with our
            smart analysis tool.
          </p>
          <p class="text-black dark:text-white">
            Our tool analyzes job data to deliver personalized, actionable
            insights, empowering your career decisions.
          </p>

          <div class="grid grid-cols-2 gap-3 pt-6 sm:gap-4">
            <div class="space-y-3">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M14.615 1.595a.75.75 0 0 1 .359.852L12.982 9.75h7.268a.75.75 0 0 1 .548 1.262l-10.5 11.25a.75.75 0 0 1-1.272-.71l1.992-7.302H3.75a.75.75 0 0 1-.548-1.262l10.5-11.25a.75.75 0 0 1 .913-.143Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Instant Access
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Get thorough and detailed research delivered in just seconds.
              </p>
            </div>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M16.5 7.5h-9v9h9v-9Z"></path>
                  <path
                    fill-rule="evenodd"
                    d="M8.25 2.25A.75.75 0 0 1 9 3v.75h2.25V3a.75.75 0 0 1 1.5 0v.75H15V3a.75.75 0 0 1 1.5 0v.75h.75a3 3 0 0 1 3 3v.75H21A.75.75 0 0 1 21 9h-.75v2.25H21a.75.75 0 0 1 0 1.5h-.75V15H21a.75.75 0 0 1 0 1.5h-.75v.75a3 3 0 0 1-3 3h-.75V21a.75.75 0 0 1-1.5 0v-.75h-2.25V21a.75.75 0 0 1-1.5 0v-.75H9V21a.75.75 0 0 1-1.5 0v-.75h-.75a3 3 0 0 1-3-3v-.75H3A.75.75 0 0 1 3 15h.75v-2.25H3a.75.75 0 0 1 0-1.5h.75V9H3a.75.75 0 0 1 0-1.5h.75v-.75a3 3 0 0 1 3-3h.75V3a.75.75 0 0 1 .75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h10.5a.75.75 0 0 1 .75.75v10.5a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V6.75Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Data-Driven Analysis
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Researches and consolidates job insights from across the web and
                presents them in a concise format.
              </p>
            </div>
          </div>
          <button
            class="p-2 text-lg px-4 font-bold bg-purple-500 dark:bg-white rounded-full text-white dark:text-black hover:bg-gray-950 hover:scale-105 dark:hover:bg-gray-100 transition-colors transition-transform duration-300 tls-button tls-button--primary tls-button--sm"
            data-tool-href="/job-research"
          >
            Try Job Research
          </button>
        </div>
        <div class="relative mt-6 sm:mt-0">
          <div
            class="absolute -inset-20 bg-[linear-gradient(to_right,var(--ui-border-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--ui-border-color)_1px,transparent_1px)] bg-[size:24px_24px] sm:-inset-40"
          >
          </div>
          <div class="absolute -inset-20 bg-gradient-to-b sm:-inset-40"></div>
          <div class="absolute -inset-20 bg-gradient-to-r sm:-inset-40"></div>
          <div
            class="tls-shadow-md rounded-card relative overflow-hidden shadow-gray-950/[0.03]"
          >
            <img
              class="relative dark:hidden rounded-3xl"
              src="images/tools/job-analysis.png"
              alt=""
            />
            <img
              class="relative hidden dark:block rounded-3xl filter invert"
              src="images/tools/job-analysis.png"
              alt=""
            />
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Resume Tailoring -->
  <section class="py-16">
    <div
      class="mx-auto space-y-16 bg-green-50 dark:bg-gray-950 p-4 sm:p-8 border border-green-400 dark:border-green-600 rounded-3xl shadow-[0_4px_16px_0_rgba(16,185,129,0.16)] dark:shadow-[0_4px_16px_0_rgba(16,185,129,0.22)]"
    >
      <h2
        class="text-black dark:text-white relative z-10 max-w-xl text-4xl font-medium lg:text-5xl"
      >
        Tailor Your Resume for Any Job.
      </h2>
      <div class="grid gap-6 sm:grid-cols-2 md:gap-12 lg:gap-24">
        <div class="relative mt-6 sm:mt-0">
          <div
            class="absolute -inset-20 bg-[linear-gradient(to_right,var(--ui-border-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--ui-border-color)_1px,transparent_1px)] bg-[size:24px_24px] sm:-inset-40"
          >
          </div>
          <div class="absolute -inset-20 bg-gradient-to-b sm:-inset-40"></div>
          <div class="absolute -inset-20 bg-gradient-to-r sm:-inset-40"></div>
          <div
            class="tls-shadow-md rounded-card relative overflow-hidden shadow-gray-950/[0.03]"
          >
            <img
              class="relative dark:hidden rounded-3xl"
              src="images/tools/resume.png"
              alt=""
            />
            <img
              class="relative hidden dark:block rounded-3xl filter invert"
              src="images/tools/resume.png"
              alt=""
            />
          </div>
        </div>

        <!-- Gemini ecosystem -->
        <div class="relative z-10 space-y-4">
          <p class="text-black dark:text-white">
            Tailor your existing resume for any job in minutes. Our tool helps you optimize your resume with tailored content and professional templates.
          </p>
          <p class="text-black dark:text-white">
            Our tool analyzes job descriptions to optimize your resume with
            targeted keywords, ensuring it passes ATS and impresses recruiters.
          </p>
          <div class="grid grid-cols-2 gap-3 pt-6 sm:gap-4">
            <div class="space-y-3">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M14.615 1.595a.75.75 0 0 1 .359.852L12.982 9.75h7.268a.75.75 0 0 1 .548 1.262l-10.5 11.25a.75.75 0 0 1-1.272-.71l1.992-7.302H3.75a.75.75 0 0 1-.548-1.262l10.5-11.25a.75.75 0 0 1 .913-.143Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Keyword Optimization
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Identifies relevant keywords and experiences, making sure the
                tailored resume is focused on the exact needs of the job.
              </p>
            </div>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M16.5 7.5h-9v9h9v-9Z"></path>
                  <path
                    fill-rule="evenodd"
                    d="M8.25 2.25A.75.75 0 0 1 9 3v.75h2.25V3a.75.75 0 0 1 1.5 0v.75H15V3a.75.75 0 0 1 1.5 0v.75h.75a3 3 0 0 1 3 3v.75H21A.75.75 0 0 1 21 9h-.75v2.25H21a.75.75 0 0 1 0 1.5h-.75V15H21a.75.75 0 0 1 0 1.5h-.75v.75a3 3 0 0 1-3 3h-.75V21a.75.75 0 0 1-1.5 0v-.75h-2.25V21a.75.75 0 0 1-1.5 0v-.75H9V21a.75.75 0 0 1-1.5 0v-.75h-.75a3 3 0 0 1-3-3v-.75H3A.75.75 0 0 1 3 15h.75v-2.25H3a.75.75 0 0 1 0-1.5h.75V9H3a.75.75 0 0 1 0-1.5h.75v-.75a3 3 0 0 1 3-3h.75V3a.75.75 0 0 1 .75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h10.5a.75.75 0 0 1 .75.75v10.5a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V6.75Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Adaptive Content
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Generate different versions of your resume depending on the
                specific job you are applying for, providing flexibility and
                customization.
              </p>
            </div>
          </div>
          <button
            class="p-2 text-lg font-bold px-4 bg-green-500 dark:bg-white rounded-full text-white dark:text-black hover:bg-gray-950 hover:scale-105 dark:hover:bg-gray-100 transition-colors transition-transform duration-300 tls-button tls-button--primary tls-button--sm"
            data-tool-href="/resume"
          >
            Tailor My Resume Now
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Resume Builder -->
  <section class="py-16">
    <div
      class="mx-auto space-y-16 bg-blue-50 dark:bg-gray-950 p-4 sm:p-8 border border-blue-400 dark:border-blue-600 rounded-3xl shadow-[0_4px_16px_0_rgba(59,130,246,0.16)] dark:shadow-[0_4px_16px_0_rgba(59,130,246,0.22)]"
    >
      <h2
        class="text-black dark:text-white relative z-10 max-w-xl text-4xl font-medium lg:text-5xl"
      >
        Build a Professional Resume from Scratch.
      </h2>
      <div class="grid gap-6 sm:grid-cols-2 md:gap-12 lg:gap-24">
        <div class="relative mt-6 sm:mt-0">
          <div
            class="absolute -inset-20 bg-[linear-gradient(to_right,var(--ui-border-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--ui-border-color)_1px,transparent_1px)] bg-[size:24px_24px] sm:-inset-40"
          >
          </div>
          <div class="absolute -inset-20 bg-gradient-to-b sm:-inset-40"></div>
          <div class="absolute -inset-20 bg-gradient-to-r sm:-inset-40"></div>
          <div
            class="tls-shadow-md rounded-card relative overflow-hidden shadow-gray-950/[0.03]"
          >
            <img
              class="relative dark:hidden rounded-3xl"
              src="images/tools/resume.png"
              alt=""
            />
            <img
              class="relative hidden dark:block rounded-3xl filter invert"
              src="images/tools/resume.png"
              alt=""
            />
          </div>
        </div>

        <div class="relative z-10 space-y-4">
          <p class="text-black dark:text-white">
            Create a stunning resume with our intuitive builder. Choose from professional templates, customize layouts, and add your details with ease.
          </p>
          <p class="text-black dark:text-white">
            Our resume builder guides you through every step, from contact information to work experience, ensuring you have a polished, professional resume in minutes.
          </p>
          <div class="grid grid-cols-2 gap-3 pt-6 sm:gap-4">
            <div class="space-y-3">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M14.615 1.595a.75.75 0 0 1 .359.852L12.982 9.75h7.268a.75.75 0 0 1 .548 1.262l-10.5 11.25a.75.75 0 0 1-1.272-.71l1.992-7.302H3.75a.75.75 0 0 1-.548-1.262l10.5-11.25a.75.75 0 0 1 .913-.143Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Easy to Use
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Our step-by-step process makes it simple to build a professional resume, even with no prior experience.
              </p>
            </div>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M16.5 7.5h-9v9h9v-9Z"></path>
                  <path
                    fill-rule="evenodd"
                    d="M8.25 2.25A.75.75 0 0 1 9 3v.75h2.25V3a.75.75 0 0 1 1.5 0v.75H15V3a.75.75 0 0 1 1.5 0v.75h.75a3 3 0 0 1 3 3v.75H21A.75.75 0 0 1 21 9h-.75v2.25H21a.75.75 0 0 1 0 1.5h-.75V15H21a.75.75 0 0 1 0 1.5h-.75v.75a3 3 0 0 1-3 3h-.75V21a.75.75 0 0 1-1.5 0v-.75h-2.25V21a.75.75 0 0 1-1.5 0v-.75H9V21a.75.75 0 0 1-1.5 0v-.75h-.75a3 3 0 0 1-3-3v-.75H3A.75.75 0 0 1 3 15h.75v-2.25H3a.75.75 0 0 1 0-1.5h.75V9H3a.75.75 0 0 1 0-1.5h.75v-.75a3 3 0 0 1 3-3h.75V3a.75.75 0 0 1 .75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h10.5a.75.75 0 0 1 .75.75v10.5a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V6.75Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Professional Templates
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Choose from a variety of professionally designed templates to create a resume that stands out.
              </p>
            </div>
          </div>
          <button
            class="p-2 text-lg font-bold px-4 bg-blue-500 dark:bg-white rounded-full text-white dark:text-black hover:bg-gray-950 hover:scale-105 dark:hover:bg-gray-100 transition-colors transition-transform duration-300 tls-button tls-button--primary tls-button--sm"
            data-tool-href="/resume-builder"
          >
            Create My Resume
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Resume Builder -->
  <section class="py-16">
    <div
      class="mx-auto space-y-16 bg-blue-50 dark:bg-gray-950 p-4 sm:p-8 border border-blue-400 dark:border-blue-600 rounded-3xl shadow-[0_4px_16px_0_rgba(59,130,246,0.16)] dark:shadow-[0_4px_16px_0_rgba(59,130,246,0.22)]"
    >
      <h2
        class="text-black dark:text-white relative z-10 max-w-xl text-4xl font-medium lg:text-5xl"
      >
        Build a Professional Resume from Scratch.
      </h2>
      <div class="grid gap-6 sm:grid-cols-2 md:gap-12 lg:gap-24">
        <div class="relative mt-6 sm:mt-0">
          <div
            class="absolute -inset-20 bg-[linear-gradient(to_right,var(--ui-border-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--ui-border-color)_1px,transparent_1px)] bg-[size:24px_24px] sm:-inset-40"
          >
          </div>
          <div class="absolute -inset-20 bg-gradient-to-b sm:-inset-40"></div>
          <div class="absolute -inset-20 bg-gradient-to-r sm:-inset-40"></div>
          <div
            class="tls-shadow-md rounded-card relative overflow-hidden shadow-gray-950/[0.03]"
          >
            <img
              class="relative dark:hidden rounded-3xl"
              src="images/tools/resume.png"
              alt=""
            />
            <img
              class="relative hidden dark:block rounded-3xl filter invert"
              src="images/tools/resume.png"
              alt=""
            />
          </div>
        </div>

        <div class="relative z-10 space-y-4">
          <p class="text-black dark:text-white">
            Create a stunning resume with our intuitive builder. Choose from professional templates, customize layouts, and add your details with ease.
          </p>
          <p class="text-black dark:text-white">
            Our resume builder guides you through every step, from contact information to work experience, ensuring you have a polished, professional resume in minutes.
          </p>
          <div class="grid grid-cols-2 gap-3 pt-6 sm:gap-4">
            <div class="space-y-3">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M14.615 1.595a.75.75 0 0 1 .359.852L12.982 9.75h7.268a.75.75 0 0 1 .548 1.262l-10.5 11.25a.75.75 0 0 1-1.272-.71l1.992-7.302H3.75a.75.75 0 0 1-.548-1.262l10.5-11.25a.75.75 0 0 1 .913-.143Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Easy to Use
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Our step-by-step process makes it simple to build a professional resume, even with no prior experience.
              </p>
            </div>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M16.5 7.5h-9v9h9v-9Z"></path>
                  <path
                    fill-rule="evenodd"
                    d="M8.25 2.25A.75.75 0 0 1 9 3v.75h2.25V3a.75.75 0 0 1 1.5 0v.75H15V3a.75.75 0 0 1 1.5 0v.75h.75a3 3 0 0 1 3 3v.75H21A.75.75 0 0 1 21 9h-.75v2.25H21a.75.75 0 0 1 0 1.5h-.75V15H21a.75.75 0 0 1 0 1.5h-.75v.75a3 3 0 0 1-3 3h-.75V21a.75.75 0 0 1-1.5 0v-.75h-2.25V21a.75.75 0 0 1-1.5 0v-.75H9V21a.75.75 0 0 1-1.5 0v-.75h-.75a3 3 0 0 1-3-3v-.75H3A.75.75 0 0 1 3 15h.75v-2.25H3a.75.75 0 0 1 0-1.5h.75V9H3a.75.75 0 0 1 0-1.5h.75v-.75a3 3 0 0 1 3-3h.75V3a.75.75 0 0 1 .75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h10.5a.75.75 0 0 1 .75.75v10.5a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V6.75Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Professional Templates
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Choose from a variety of professionally designed templates to create a resume that stands out.
              </p>
            </div>
          </div>
          <button
            class="p-2 text-lg font-bold px-4 bg-blue-500 dark:bg-white rounded-full text-white dark:text-black hover:bg-gray-950 hover:scale-105 dark:hover:bg-gray-100 transition-colors transition-transform duration-300 tls-button tls-button--primary tls-button--sm"
            data-tool-href="/resume-builder"
          >
            Create My Resume
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Cover Letter -->
  <section
    class="mx-auto w-full space-y-16 bg-yellow-50 dark:bg-gray-950 p-4 sm:p-8 border border-yellow-400 dark:border-yellow-600 rounded-3xl shadow-[0_4px_16px_0_rgba(251,191,36,0.16)] dark:shadow-[0_4px_16px_0_rgba(251,191,36,0.22)] overflow-hidden"
  >
    <div class="space-y-16">
      <h2
        class="text-black dark:text-white relative z-10 max-w-xl text-4xl font-medium lg:text-5xl"
      >
        Effortless Cover Letters.
      </h2>
      <div class="grid gap-6 sm:grid-cols-2 md:gap-12 lg:gap-24">
        <div class="relative z-10 space-y-4">
          <p class="text-black dark:text-white">
            Craft compelling cover letters in mere moments, using our platform
            to generate bespoke content that precisely aligns with each job
            description.
          </p>
          <p class="text-black dark:text-white">
            Analyzes job posting to automatically generate a customized cover
            letter by highlighting relevant skills and experiences, ensuring
            each application perfectly matches the specific role requirements.
          </p>

          <div class="grid grid-cols-2 gap-3 pt-6 sm:gap-4">
            <div class="space-y-3">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M14.615 1.595a.75.75 0 0 1 .359.852L12.982 9.75h7.268a.75.75 0 0 1 .548 1.262l-10.5 11.25a.75.75 0 0 1-1.272-.71l1.992-7.302H3.75a.75.75 0 0 1-.548-1.262l10.5-11.25a.75.75 0 0 1 .913-.143Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Smart matching
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                We scan the job description and pulls out the most relevant
                skills and experiences from your resume.
              </p>
            </div>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M16.5 7.5h-9v9h9v-9Z"></path>
                  <path
                    fill-rule="evenodd"
                    d="M8.25 2.25A.75.75 0 0 1 9 3v.75h2.25V3a.75.75 0 0 1 1.5 0v.75H15V3a.75.75 0 0 1 1.5 0v.75h.75a3 3 0 0 1 3 3v.75H21A.75.75 0 0 1 21 9h-.75v2.25H21a.75.75 0 0 1 0 1.5h-.75V15H21a.75.75 0 0 1 0 1.5h-.75v.75a3 3 0 0 1-3 3h-.75V21a.75.75 0 0 1-1.5 0v-.75h-2.25V21a.75.75 0 0 1-1.5 0v-.75H9V21a.75.75 0 0 1-1.5 0v-.75h-.75a3 3 0 0 1-3-3v-.75H3A.75.75 0 0 1 3 15h.75v-2.25H3a.75.75 0 0 1 0-1.5h.75V9H3a.75.75 0 0 1 0-1.5h.75v-.75a3 3 0 0 1 3-3h.75V3a.75.75 0 0 1 .75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h10.5a.75.75 0 0 1 .75.75v10.5a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V6.75Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Template Variety
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Choose from a wide range of templates that are designed to match
                the industry you are applying to.
              </p>
            </div>
          </div>
          <button
            class="p-2 text-lg font-bold px-4 bg-yellow-500 dark:bg-white rounded-full text-white dark:text-black hover:bg-gray-950 hover:scale-105 dark:hover:bg-gray-100 transition-colors transition-transform duration-300 tls-button tls-button--primary tls-button--sm"
            data-tool-href="/cover-letter"
          >
            Generate My Cover Letter
          </button>
        </div>
        <div class="relative mt-6 sm:mt-0">
          <div
            class="absolute -inset-20 bg-[linear-gradient(to_right,var(--ui-border-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--ui-border-color)_1px,transparent_1px)] bg-[size:24px_24px] sm:-inset-40"
          >
          </div>
          <div class="absolute -inset-20 bg-gradient-to-b sm:-inset-40"></div>
          <div class="absolute -inset-20 bg-gradient-to-r sm:-inset-40"></div>
          <div
            class="tls-shadow-md rounded-card relative overflow-hidden shadow-gray-950/[0.03]"
          >
            <img
              class="relative dark:hidden rounded-3xl"
              src="images/tools/cover-letter.png"
              alt=""
            />
            <img
              class="relative hidden dark:block rounded-3xl filter invert"
              src="images/tools/cover-letter.png"
              alt=""
            />
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Job Tracker -->
  <section class="py-16">
    <div
      class="mx-auto space-y-16 bg-rose-50 dark:bg-gray-950 p-4 sm:p-8 border border-rose-400 dark:border-rose-600 rounded-3xl shadow-[0_4px_16px_0_rgba(244,63,94,0.16)] dark:shadow-[0_4px_16px_0_rgba(244,63,94,0.22)]"
    >
      <h2
        class="text-black dark:text-white relative z-10 max-w-xl text-4xl font-medium lg:text-5xl"
      >
        Streamline Your Job Search.
      </h2>
      <div class="grid gap-6 sm:grid-cols-2 md:gap-12 lg:gap-24">
        <div class="relative mt-6 sm:mt-0">
          <div
            class="absolute -inset-20 bg-[linear-gradient(to_right,var(--ui-border-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--ui-border-color)_1px,transparent_1px)] bg-[size:24px_24px] sm:-inset-40"
          >
          </div>
          <div class="absolute -inset-20 bg-gradient-to-b sm:-inset-40"></div>
          <div class="absolute -inset-20 bg-gradient-to-r sm:-inset-40"></div>
          <div
            class="tls-shadow-md rounded-card relative overflow-hidden shadow-gray-950/[0.03]"
          >
            <img
              class="relative dark:hidden rounded-3xl"
              src="images/tools/job-tracker.png"
              alt=""
            />
            <img
              class="relative hidden dark:block rounded-3xl filter invert"
              src="images/tools/job-tracker.png"
              alt=""
            />
          </div>
        </div>

        <!-- Gemini ecosystem -->
        <div class="relative z-10 space-y-4">
          <p class="text-black dark:text-white">
            Track all your job applications with a visual pipeline, reminders,
            and statistical insights to help you stay organized and focused.
          </p>
          <p class="text-black dark:text-white">
            You can save many applications and track them in one place.
          </p>

          <div class="grid grid-cols-2 gap-3 pt-6 sm:gap-4">
            <div class="space-y-3">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M14.615 1.595a.75.75 0 0 1 .359.852L12.982 9.75h7.268a.75.75 0 0 1 .548 1.262l-10.5 11.25a.75.75 0 0 1-1.272-.71l1.992-7.302H3.75a.75.75 0 0 1-.548-1.262l10.5-11.25a.75.75 0 0 1 .913-.143Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Efficient Tracking
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Manage applications with speed and precision.
              </p>
            </div>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M16.5 7.5h-9v9h9v-9Z"></path>
                  <path
                    fill-rule="evenodd"
                    d="M8.25 2.25A.75.75 0 0 1 9 3v.75h2.25V3a.75.75 0 0 1 1.5 0v.75H15V3a.75.75 0 0 1 1.5 0v.75h.75a3 3 0 0 1 3 3v.75H21A.75.75 0 0 1 21 9h-.75v2.25H21a.75.75 0 0 1 0 1.5h-.75V15H21a.75.75 0 0 1 0 1.5h-.75v.75a3 3 0 0 1-3 3h-.75V21a.75.75 0 0 1-1.5 0v-.75h-2.25V21a.75.75 0 0 1-1.5 0v-.75H9V21a.75.75 0 0 1-1.5 0v-.75h-.75a3 3 0 0 1-3-3v-.75H3A.75.75 0 0 1 3 15h.75v-2.25H3a.75.75 0 0 1 0-1.5h.75V9H3a.75.75 0 0 1 0-1.5h.75v-.75a3 3 0 0 1 3-3h.75V3a.75.75 0 0 1 .75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h10.5a.75.75 0 0 1 .75.75v10.5a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V6.75Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Comprehensive Overview
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Gain a clear view of your job search progress.
              </p>
            </div>
          </div>
          <button
            class="p-2 text-lg font-bold px-4 bg-rose-500 dark:bg-white rounded-full text-white dark:text-black hover:bg-gray-950 hover:scale-105 dark:hover:bg-gray-100 transition-colors transition-transform duration-300 tls-button tls-button--primary tls-button--sm"
            data-tool-href="/job-tracker"
          >
            Track My Applications
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Interview Prep -->
  <section>
    <div
      class="mx-auto space-y-16 bg-orange-50 dark:bg-gray-950 p-4 sm:p-8 border border-yellow-400 dark:border-yellow-600 rounded-3xl shadow-[0_4px_16px_0_rgba(251,191,36,0.16)] dark:shadow-[0_4px_16px_0_rgba(251,191,36,0.22)]"
    >
      <h2
        class="text-black dark:text-white relative z-10 max-w-xl text-4xl font-medium lg:text-5xl"
      >
        Prepare like a pro with personalized feedback.
      </h2>
      <div class="grid gap-6 sm:grid-cols-2 md:gap-12 lg:gap-24">
        <!-- Gemini ecosystem -->
        <div class="relative z-10 space-y-4">
          <p class="text-black dark:text-white">
            Ace your interviews with targeted practice and tailored feedback.
            Input your resume and the job description, and our system generates
            likely interview questions. Practice your answers, submit them, and
            receive personalized feedback to improve your responses and boost
            your confidence.
          </p>

          <div class="grid grid-cols-2 gap-3 pt-6 sm:gap-4">
            <div class="space-y-3">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M14.615 1.595a.75.75 0 0 1 .359.852L12.982 9.75h7.268a.75.75 0 0 1 .548 1.262l-10.5 11.25a.75.75 0 0 1-1.272-.71l1.992-7.302H3.75a.75.75 0 0 1-.548-1.262l10.5-11.25a.75.75 0 0 1 .913-.143Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Tailored Question Bank
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Practice with role specific questions to your target
                organisation.
              </p>
            </div>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M16.5 7.5h-9v9h9v-9Z"></path>
                  <path
                    fill-rule="evenodd"
                    d="M8.25 2.25A.75.75 0 0 1 9 3v.75h2.25V3a.75.75 0 0 1 1.5 0v.75H15V3a.75.75 0 0 1 1.5 0v.75h.75a3 3 0 0 1 3 3v.75H21A.75.75 0 0 1 21 9h-.75v2.25H21a.75.75 0 0 1 0 1.5h-.75V15H21a.75.75 0 0 1 0 1.5h-.75v.75a3 3 0 0 1-3 3h-.75V21a.75.75 0 0 1-1.5 0v-.75h-2.25V21a.75.75 0 0 1-1.5 0v-.75H9V21a.75.75 0 0 1-1.5 0v-.75h-.75a3 3 0 0 1-3-3v-.75H3A.75.75 0 0 1 3 15h.75v-2.25H3a.75.75 0 0 1 0-1.5h.75V9H3a.75.75 0 0 1 0-1.5h.75v-.75a3 3 0 0 1 3-3h.75V3a.75.75 0 0 1 .75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h10.5a.75.75 0 0 1 .75.75v10.5a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V6.75Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Realistic Practice
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Refine and improve your interview responses with instant AI
                feedback.
              </p>
            </div>
          </div>
          <button
            class="p-2 text-lg font-bold px-4 bg-orange-500 dark:bg-white rounded-full text-white dark:text-black hover:bg-gray-950 hover:scale-105 dark:hover:bg-gray-100 transition-colors transition-transform duration-300 tls-button tls-button--primary tls-button--sm"
            data-tool-href="/interview-prep"
          >
            Start Practicing
          </button>
        </div>

        <div class="relative mt-6 sm:mt-0">
          <div
            class="absolute -inset-20 bg-[linear-gradient(to_right,var(--ui-border-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--ui-border-color)_1px,transparent_1px)] bg-[size:24px_24px] sm:-inset-40"
          >
          </div>
          <div class="absolute -inset-20 bg-gradient-to-b sm:-inset-40"></div>
          <div class="absolute -inset-20 bg-gradient-to-r sm:-inset-40"></div>
          <div
            class="tls-shadow-md rounded-card relative overflow-hidden shadow-gray-950/[0.03]"
          >
            <img
              class="relative dark:hidden rounded-3xl"
              src="images/tools/interview-prep.png"
              alt=""
            />
            <img
              class="relative hidden dark:block rounded-3xl filter invert"
              src="images/tools/interview-prep.png"
              alt=""
            />
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- LinkedIn Optimiser -->
  <section
    class="mx-auto w-full space-y-16 mt-16 bg-blue-50 dark:bg-gray-950 p-4 sm:p-8 border border-blue-400 dark:border-blue-600 rounded-3xl shadow-[0_4px_16px_0_rgba(56,189,248,0.16)] dark:shadow-[0_4px_16px_0_rgba(56,189,248,0.22)] overflow-hidden"
  >
    <div class="space-y-16">
      <h2
        class="text-black dark:text-white relative z-10 max-w-xl text-4xl font-medium lg:text-5xl"
      >
        Optimise LinkedIn profile with expert tips.
      </h2>
      <div class="grid gap-6 sm:grid-cols-2 md:gap-12 lg:gap-24">
        <div class="relative mt-6 sm:mt-0">
          <div
            class="absolute -inset-20 bg-[linear-gradient(to_right,var(--ui-border-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--ui-border-color)_1px,transparent_1px)] bg-[size:24px_24px] sm:-inset-40"
          >
          </div>
          <div class="absolute -inset-20 bg-gradient-to-b sm:-inset-40"></div>
          <div class="absolute -inset-20 bg-gradient-to-r sm:-inset-40"></div>
          <div
            class="tls-shadow-md rounded-card rounded-3xl relative overflow-hidden shadow-gray-950/[0.03]"
          >
            <img
              class="relative dark:hidden rounded-3xl"
              src="images/tools/linkedin-optimiser.png"
              alt=""
            />
            <img
              class="relative hidden dark:block rounded-3xl filter invert"
              src="images/tools/linkedin-optimiser.png"
              alt=""
            />
          </div>
        </div>
        <div class="relative z-10 space-y-4">
          <p class="text-black dark:text-white">
            Enhance your LinkedIn presence with data-driven recommendations and
            strategic guidance.
          </p>

          <div class="grid grid-cols-2 gap-3 pt-6 sm:gap-4">
            <div class="space-y-3">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M14.615 1.595a.75.75 0 0 1 .359.852L12.982 9.75h7.268a.75.75 0 0 1 .548 1.262l-10.5 11.25a.75.75 0 0 1-1.272-.71l1.992-7.302H3.75a.75.75 0 0 1-.548-1.262l10.5-11.25a.75.75 0 0 1 .913-.143Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Data-Driven Insights
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Leverage analytics for profile improvement.
              </p>
            </div>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M16.5 7.5h-9v9h9v-9Z"></path>
                  <path
                    fill-rule="evenodd"
                    d="M8.25 2.25A.75.75 0 0 1 9 3v.75h2.25V3a.75.75 0 0 1 1.5 0v.75H15V3a.75.75 0 0 1 1.5 0v.75h.75a3 3 0 0 1 3 3v.75H21A.75.75 0 0 1 21 9h-.75v2.25H21a.75.75 0 0 1 0 1.5h-.75V15H21a.75.75 0 0 1 0 1.5h-.75v.75a3 3 0 0 1-3 3h-.75V21a.75.75 0 0 1-1.5 0v-.75h-2.25V21a.75.75 0 0 1-1.5 0v-.75H9V21a.75.75 0 0 1-1.5 0v-.75h-.75a3 3 0 0 1-3-3v-.75H3A.75.75 0 0 1 3 15h.75v-2.25H3a.75.75 0 0 1 0-1.5h.75V9H3a.75.75 0 0 1 0-1.5h.75v-.75a3 3 0 0 1 3-3h.75V3a.75.75 0 0 1 .75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h10.5a.75.75 0 0 1 .75.75v10.5a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V6.75Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Strategic Optimization
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Maximize your professional visibility.
              </p>
            </div>
          </div>
          <button
            class="p-2 text-lg font-bold px-4 bg-blue-700 dark:bg-white rounded-full text-white dark:text-black hover:bg-gray-950 hover:scale-105 dark:hover:bg-gray-100 transition-colors transition-transform duration-300 tls-button tls-button--primary tls-button--sm"
            data-tool-href="/tools/linkedin-optimiser"
          >
            Optimise My LinkedIn Profile
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- AI Chat -->
  <section>
    <div
      class="mx-auto space-y-16 mt-16 bg-lime-50 dark:bg-gray-950 p-4 sm:p-8 border border-teal-400 dark:border-teal-600 rounded-3xl shadow-[0_4px_16px_0_rgba(16,185,129,0.16)] dark:shadow-[0_4px_16px_0_rgba(16,185,129,0.22)]"
    >
      <h2
        class="text-black dark:text-white relative z-10 max-w-xl text-4xl font-medium lg:text-5xl"
      >
        Getting personalised advice has never been easier.
      </h2>
      <div class="grid gap-6 sm:grid-cols-2 md:gap-12 lg:gap-24">
        <div class="relative mt-6 sm:mt-0">
          <div
            class="absolute -inset-20 bg-[linear-gradient(to_right,var(--ui-border-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--ui-border-color)_1px,transparent_1px)] bg-[size:24px_24px] sm:-inset-40"
          >
          </div>
          <div class="absolute -inset-20 bg-gradient-to-b sm:-inset-40"></div>
          <div class="absolute -inset-20 bg-gradient-to-r sm:-inset-40"></div>
          <div
            class="tls-shadow-md rounded-card rounded-3xl relative overflow-hidden shadow-gray-950/[0.03]"
          >
            <img
              class="relative dark:hidden rounded-3xl"
              src="images/tools/ai-chat.png"
              alt=""
            />
            <img
              class="relative hidden dark:block rounded-3xl filter invert"
              src="images/tools/ai-chat.png"
              alt=""
            />
          </div>
        </div>

        <!-- Gemini ecosystem -->
        <div class="relative z-10 space-y-4">
          <p class="text-black dark:text-white">
            Receive tailored career guidance instantly. Ask any career-related
            question, and get personalized advice, actionable steps,
            and expert insights to help you achieve your professional goals.
          </p>

          <div class="grid grid-cols-2 gap-3 pt-6 sm:gap-4">
            <div class="space-y-3">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M14.615 1.595a.75.75 0 0 1 .359.852L12.982 9.75h7.268a.75.75 0 0 1 .548 1.262l-10.5 11.25a.75.75 0 0 1-1.272-.71l1.992-7.302H3.75a.75.75 0 0 1-.548-1.262l10.5-11.25a.75.75 0 0 1 .913-.143Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Instant Career Advice
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Get answers to your career questions immediately.
              </p>
            </div>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <svg
                  class="size-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M16.5 7.5h-9v9h9v-9Z"></path>
                  <path
                    fill-rule="evenodd"
                    d="M8.25 2.25A.75.75 0 0 1 9 3v.75h2.25V3a.75.75 0 0 1 1.5 0v.75H15V3a.75.75 0 0 1 1.5 0v.75h.75a3 3 0 0 1 3 3v.75H21A.75.75 0 0 1 21 9h-.75v2.25H21a.75.75 0 0 1 0 1.5h-.75V15H21a.75.75 0 0 1 0 1.5h-.75v.75a3 3 0 0 1-3 3h-.75V21a.75.75 0 0 1-1.5 0v-.75h-2.25V21a.75.75 0 0 1-1.5 0v-.75H9V21a.75.75 0 0 1-1.5 0v-.75h-.75a3 3 0 0 1-3-3v-.75H3A.75.75 0 0 1 3 15h.75v-2.25H3a.75.75 0 0 1 0-1.5h.75V9H3a.75.75 0 0 1 0-1.5h.75v-.75a3 3 0 0 1 3-3h.75V3a.75.75 0 0 1 .75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h10.5a.75.75 0 0 1 .75.75v10.5a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V6.75Z"
                    clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-black dark:text-white text-sm font-medium">
                  Actionable Insights
                </h3>
              </div>
              <p class="text-black dark:text-white text-sm">
                Receive clear, practical steps to advance your career.
              </p>
            </div>
          </div>
          <button
            class="p-2 text-lg font-bold px-4 bg-lime-500 dark:bg-white rounded-full text-white dark:text-black hover:bg-gray-950 hover:scale-105 dark:hover:bg-gray-100 transition-colors transition-transform duration-300 tls-button tls-button--primary tls-button--sm"
            data-tool-href="/login"
          >
            Ask a Question
          </button>
        </div>
      </div>
    </div>
  </section>
</Container>
