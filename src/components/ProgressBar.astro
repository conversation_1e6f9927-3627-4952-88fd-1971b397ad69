---
interface Props {
  id?: string;
  progress?: number;
  color?: 'primary' | 'success' | 'warning' | 'error' | 'info';
  height?: 'xs' | 'sm' | 'md' | 'lg';
  showPercentage?: boolean;
  indeterminate?: boolean;
  className?: string;
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';
}

const {
  id,
  progress = 0,
  color = 'primary',
  height = 'md',
  showPercentage = false,
  indeterminate = false,
  className = '',
  rounded = 'full'
} = Astro.props;

// Ensure progress is between 0 and 100
const normalizedProgress = Math.min(Math.max(progress, 0), 100);

// Color mappings
const colorMap = {
  primary: 'bg-primary',
  success: 'bg-green-500',
  warning: 'bg-yellow-500',
  error: 'bg-red-500',
  info: 'bg-blue-500'
};

// Height mappings
const heightMap = {
  xs: 'h-1',
  sm: 'h-2',
  md: 'h-3',
  lg: 'h-4'
};

// Rounded mappings
const roundedMap = {
  none: 'rounded-none',
  sm: 'rounded',
  md: 'rounded-md',
  lg: 'rounded-lg',
  full: 'rounded-full'
};

const barColor = colorMap[color] || colorMap.primary;
const barHeight = heightMap[height] || heightMap.md;
const barRounded = roundedMap[rounded] || roundedMap.full;

// Generate unique ID if not provided
const uniqueId = id || `progress-bar-${Math.random().toString(36).substring(2, 9)}`;
---

<div class={`w-full ${className}`}>
  {showPercentage && (
    <div class="flex justify-between mb-1">
      <span class="text-sm font-medium text-gray-700 dark:text-gray-300" id={`${uniqueId}-label`}>
        Progress
      </span>
      <span class="text-sm font-medium text-gray-700 dark:text-gray-300" id={`${uniqueId}-percentage`}>
        {normalizedProgress}%
      </span>
    </div>
  )}
  
  <div 
    class={`w-full ${barHeight} bg-gray-200 dark:bg-gray-700 ${barRounded} overflow-hidden`}
    role="progressbar" 
    aria-valuenow={indeterminate ? undefined : normalizedProgress} 
    aria-valuemin="0" 
    aria-valuemax="100"
    id={uniqueId}
  >
    {indeterminate ? (
      <div 
        class={`${barHeight} ${barColor} ${barRounded} animate-progress-indeterminate`}
        style="width: 40%;"
        id={`${uniqueId}-bar`}
      ></div>
    ) : (
      <div 
        class={`${barHeight} ${barColor} ${barRounded} transition-all duration-300 ease-out`}
        style={`width: ${normalizedProgress}%;`}
        id={`${uniqueId}-bar`}
      ></div>
    )}
  </div>
</div>

<script define:vars={{ uniqueId }}>
  // Function to update progress
  function updateProgress(newProgress) {
    const progressBar = document.getElementById(`${uniqueId}-bar`);
    const percentageLabel = document.getElementById(`${uniqueId}-percentage`);
    const progressContainer = document.getElementById(uniqueId);
    
    if (progressBar) {
      // Ensure progress is between 0 and 100
      const normalizedProgress = Math.min(Math.max(newProgress, 0), 100);
      
      // Update the width
      progressBar.style.width = `${normalizedProgress}%`;
      
      // Update the aria value
      if (progressContainer) {
        progressContainer.setAttribute('aria-valuenow', normalizedProgress.toString());
      }
      
      // Update percentage label if it exists
      if (percentageLabel) {
        percentageLabel.textContent = `${Math.round(normalizedProgress)}%`;
      }
    }
  }
  
  // Function to set indeterminate state
  function setIndeterminate(isIndeterminate) {
    const progressBar = document.getElementById(`${uniqueId}-bar`);
    const progressContainer = document.getElementById(uniqueId);
    
    if (progressBar) {
      if (isIndeterminate) {
        progressBar.style.width = '40%';
        progressBar.classList.add('animate-progress-indeterminate');
        
        if (progressContainer) {
          progressContainer.removeAttribute('aria-valuenow');
        }
      } else {
        progressBar.classList.remove('animate-progress-indeterminate');
        // Reset to 0% or another value
        updateProgress(0);
      }
    }
  }
  
  // Expose functions to window for external access
  if (window.progressBarFunctions === undefined) {
    window.progressBarFunctions = {};
  }
  
  window.progressBarFunctions[uniqueId] = {
    updateProgress,
    setIndeterminate
  };
</script>
