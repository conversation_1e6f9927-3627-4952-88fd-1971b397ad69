---
interface Props {
  type?: 'text' | 'title' | 'paragraph' | 'card' | 'avatar' | 'button' | 'custom';
  lines?: number;
  width?: string;
  height?: string;
  className?: string;
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';
}

const {
  type = 'text',
  lines = 1,
  width,
  height,
  className = '',
  rounded = 'md'
} = Astro.props;

// Rounded mappings
const roundedMap = {
  none: 'rounded-none',
  sm: 'rounded',
  md: 'rounded-md',
  lg: 'rounded-lg',
  full: 'rounded-full'
};

const roundedClass = roundedMap[rounded] || roundedMap.md;

// Base skeleton class
const baseClass = `animate-pulse bg-gray-200 dark:bg-gray-700 ${roundedClass}`;

// Width and height styles
const widthStyle = width ? `width: ${width};` : '';
const heightStyle = height ? `height: ${height};` : '';
const inlineStyle = (widthStyle || heightStyle) ? `style="${widthStyle} ${heightStyle}"` : '';

// Generate skeleton based on type
let skeletonContent = '';

switch (type) {
  case 'title':
    skeletonContent = `<div class="${baseClass} h-7 w-1/3 mb-4" ${inlineStyle}></div>`;
    break;
  case 'paragraph':
    skeletonContent = Array(lines).fill(0).map((_, i) => {
      const width = i === lines - 1 ? 'w-2/3' : 'w-full';
      return `<div class="${baseClass} h-4 ${width} mb-2" ${inlineStyle}></div>`;
    }).join('');
    break;
  case 'card':
    skeletonContent = `
      <div class="${baseClass} p-6 ${className}" ${inlineStyle}>
        <div class="flex items-center space-x-4 mb-4">
          <div class="rounded-full bg-gray-300 dark:bg-gray-600 h-12 w-12"></div>
          <div class="flex-1 space-y-2">
            <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
            <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
          </div>
        </div>
        <div class="space-y-3">
          <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded"></div>
          <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded"></div>
          <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-5/6"></div>
        </div>
      </div>
    `;
    break;
  case 'avatar':
    skeletonContent = `<div class="${baseClass} rounded-full h-12 w-12" ${inlineStyle}></div>`;
    break;
  case 'button':
    skeletonContent = `<div class="${baseClass} h-10 w-24" ${inlineStyle}></div>`;
    break;
  case 'text':
  default:
    skeletonContent = Array(lines).fill(0).map((_, i) => {
      const width = i === lines - 1 && lines > 1 ? 'w-4/5' : 'w-full';
      return `<div class="${baseClass} h-4 ${width} ${i < lines - 1 ? 'mb-2' : ''}" ${inlineStyle}></div>`;
    }).join('');
    break;
}
---

<div class={className} set:html={skeletonContent} />
