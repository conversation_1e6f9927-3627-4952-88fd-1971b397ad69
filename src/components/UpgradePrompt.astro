---
/**
 * UpgradePrompt Component
 * 
 * A reusable modal component for prompting users to upgrade their subscription
 * when they reach usage limits or try to access premium features.
 * 
 * Usage:
 * <UpgradePrompt 
 *   id="uniqueId"
 *   featureName="resume generations" 
 *   customMessage="Custom message here" 
 *   isOpen={false} 
 * />
 */

interface Props {
  /**
   * Unique ID for this instance of the upgrade prompt
   * Required when multiple upgrade prompts might exist on the same page
   */
  id?: string;
  
  /**
   * Name of the feature being limited (e.g., "resume generations", "cover letters")
   * Used to customize the default message
   */
  featureName?: string;
  
  /**
   * Optional custom message to display instead of the default
   */
  customMessage?: string;
  
  /**
   * Whether the modal should be open by default
   * @default false
   */
  isOpen?: boolean;
}

const { 
  id = "upgradePrompt", 
  featureName = "feature", 
  customMessage, 
  isOpen = false 
} = Astro.props;

const defaultMessage = `You've reached the maximum number of ${featureName} for your current plan.`;
const message = customMessage || defaultMessage;
---

<div 
  id={id}
  class:list={[
    "fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4 transition-all duration-300",
    isOpen ? "opacity-100 pointer-events-auto" : "opacity-0 pointer-events-none"
  ]}
>
  <!-- Modal Content -->
  <div 
    class="bg-white dark:bg-gray-900 rounded-2xl p-8 max-w-md w-full text-center shadow-xl transform transition-all duration-300 scale-100"
    id={`${id}-content`}
  >
    <!-- Usage Limit Icon -->
    <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/30 mb-4">
      <svg class="h-6 w-6 text-yellow-600 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
      </svg>
    </div>
    
    <h2 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
      Usage Limit Reached
    </h2>
    
    <p class="text-gray-600 dark:text-gray-400 mb-6">
      {message}
    </p>
    
    <div class="flex flex-col sm:flex-row justify-center gap-3">
      <button
        id={`${id}-upgrade-button`}
        class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-2.5 rounded-full bg-black dark:bg-white text-white dark:text-black font-semibold text-sm hover:bg-gray-800 dark:hover:bg-gray-100 transition duration-300 ease-in-out"
      >
        Upgrade to Pro
      </button>
      
      <button
        id={`${id}-close-button`}
        class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-2.5 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 font-semibold text-sm hover:bg-gray-300 dark:hover:bg-gray-600 transition duration-300 ease-in-out"
      >
        Maybe Later
      </button>
    </div>
  </div>
</div>

<script define:vars={{ id }}>
  // Get modal elements
  const modal = document.getElementById(id);
  const upgradeButton = document.getElementById(`${id}-upgrade-button`);
  const closeButton = document.getElementById(`${id}-close-button`);
  
  // Handle upgrade button click
  upgradeButton?.addEventListener('click', () => {
    window.location.href = '/pricing';
  });
  
  // Handle close button click
  closeButton?.addEventListener('click', () => {
    hideModal();
  });
  
  // Close modal when clicking outside
  modal?.addEventListener('click', (e) => {
    if (e.target === modal) {
      hideModal();
    }
  });
  
  // Close on escape key
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && modal && !modal.classList.contains('opacity-0')) {
      hideModal();
    }
  });
  
  // Function to hide the modal
  function hideModal() {
    if (modal) {
      modal.classList.remove('opacity-100', 'pointer-events-auto');
      modal.classList.add('opacity-0', 'pointer-events-none');
    }
  }
  
  // Function to show the modal
  function showModal() {
    if (modal) {
      modal.classList.remove('opacity-0', 'pointer-events-none');
      modal.classList.add('opacity-100', 'pointer-events-auto');
    }
  }
  
  // Expose functions to window for external access
  if (window.upgradePromptFunctions === undefined) {
    window.upgradePromptFunctions = {};
  }
  
  window.upgradePromptFunctions[id] = {
    show: showModal,
    hide: hideModal
  };
</script>
