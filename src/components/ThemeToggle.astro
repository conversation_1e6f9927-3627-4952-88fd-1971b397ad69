---
// Theme Toggle Component
---

<button
  id="theme-toggle"
  aria-label="Toggle dark mode"
  class="p-2.5 rounded-full bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-300 shadow-md"
>
  <svg
    id="theme-toggle-dark-icon"
    class="w-5 h-5 hidden dark:block text-gray-500 dark:text-gray-400"
    fill="currentColor"
    viewBox="0 0 20 20"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"
    ></path>
  </svg>
  <svg
    id="theme-toggle-light-icon"
    class="w-5 h-5 block dark:hidden text-gray-500"
    fill="currentColor"
    viewBox="0 0 20 20"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 100 2h1z"
      fill-rule="evenodd"
      clip-rule="evenodd"></path>
  </svg>
</button>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    const themeToggle = document.getElementById("theme-toggle");
    const darkIcon = document.getElementById("theme-toggle-dark-icon");
    const lightIcon = document.getElementById("theme-toggle-light-icon");

    function toggleTheme() {
      // Get current theme state
      const isDarkMode = document.documentElement.classList.contains("dark");
      const newTheme = isDarkMode ? "light" : "dark";

      // Apply the new theme
      document.documentElement.classList.toggle("dark", newTheme === "dark");

      // Always update localStorage with the new theme to ensure consistency
      localStorage.setItem("color-theme", newTheme);

      // Update icon visibility - be explicit rather than toggling
      if (newTheme === "dark") {
        darkIcon?.classList.remove("hidden");
        lightIcon?.classList.add("hidden");
      } else {
        lightIcon?.classList.remove("hidden");
        darkIcon?.classList.add("hidden");
      }

      // Dispatch a custom event that other components can listen for
      window.dispatchEvent(
        new CustomEvent("theme-changed", {
          detail: { theme: newTheme },
        })
      );
    }

    // Initial theme setup - be consistent with Layout.astro
    const savedTheme = localStorage.getItem("color-theme");
    const systemPrefersDark = window.matchMedia(
      "(prefers-color-scheme: dark)"
    ).matches;

    // Apply the theme based on localStorage or system preference
    const isDark = savedTheme === "dark" || (!savedTheme && systemPrefersDark);
    document.documentElement.classList.toggle("dark", isDark);

    // Make sure we always have a theme value in localStorage
    if (!savedTheme) {
      localStorage.setItem("color-theme", isDark ? "dark" : "light");
    }

    // Update icon visibility
    if (isDark) {
      darkIcon?.classList.remove("hidden");
      lightIcon?.classList.add("hidden");
    } else {
      lightIcon?.classList.remove("hidden");
      darkIcon?.classList.add("hidden");
    }

    // Add click event listener
    themeToggle?.addEventListener("click", toggleTheme);
  });
</script>
