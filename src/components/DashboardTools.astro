---
import Container from "./Container.astro";
---
<style is:global>
  /* Remove focus outline for all elements */
  *:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Ensure no blue border on click */
  *:active {
    outline: none !important;
    border: none !important;
  }
</style>
<style>
  @keyframes subtle-float {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  .group:hover {
    animation: subtle-float 2s ease-in-out infinite;
  }
</style>
<section>
  <Container>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
<!-- Job research -->
  <a
    href="/job-research"
    class="block transform hover:scale-105 transition-transform duration-500 rounded-2xl"
  >
    <div
      class="bg-rose-100 dark:bg-rose-900 backdrop-blur-xl rounded-2xl p-8 lg:p-10 h-full relative border border-rose-400 dark:border-rose-600 shadow-[0_4px_16px_0_rgba(244,63,94,0.16)] dark:shadow-[0_4px_16px_0_rgba(244,63,94,0.22)]"
    >
      <div class="absolute top-4 right-4">
        <span
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-rose-100 dark:bg-rose-900/30 text-rose-600 dark:text-rose-400"
        >
          New
        </span>
      </div>
      <div class="flex flex-col h-full">
        <div class="flex-grow space-y-4">
          <div class="flex items-center justify-between">
            <h3
              class="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white"
            >
              Job Research
            </h3>
          </div>
          <p class="text-sm lg:text-base text-gray-600 dark:text-gray-300">
            Powerful job research to decode roles, assess skills, and
            optimize your application strategy.
          </p>
          <div
            class="flex items-center space-x-2 text-sm text-gray-500 dark:text-white"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"
              ></path>
            </svg>
            <span>400+ profiles researched</span>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-between">
          <span
            class="inline-flex items-center text-black dark:text-white hover:text-primary/80 transition-colors"
          >
            Explore <span class="ml-2">→</span>
          </span>
          <div
            id="jobAnalysisCounter"
            class="flex items-center gap-1.5 px-2.5 py-1.5 bg-rose-200/80 dark:bg-rose-200/90 rounded-full text-xs font-medium text-rose-800 dark:text-rose-900 shadow-sm transition-all duration-500 hover:shadow-md"
          >
            <span class="loading-text">Loading...</span>
            <span class="loaded-text hidden"></span>
            <div class="w-12 h-1 ml-1 bg-rose-300/50 rounded-full overflow-hidden">
              <div class="jobAnalysisProgress h-full bg-rose-500 rounded-full" style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a>

  <!-- Resume Builder -->
  <a
    href="/resume-builder"
    class="block transform hover:scale-105 transition-transform duration-500 rounded-2xl"
  >
    <div
      class="bg-emerald-100 dark:bg-emerald-900 backdrop-blur-xl rounded-2xl p-8 lg:p-10 h-full relative border border-emerald-400 dark:border-emerald-600 shadow-[0_4px_16px_0_rgba(52,211,153,0.16)] dark:shadow-[0_4px_16px_0_rgba(52,211,153,0.22)]"
    >
      <div class="absolute top-4 right-4">
        <span
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400"
        >
          New
        </span>
      </div>
      <div class="flex flex-col h-full">
        <div class="flex-grow space-y-4">
          <div class="flex items-center justify-between">
            <h3
              class="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white"
            >
              Resume Builder
            </h3>
          </div>
          <p class="text-sm lg:text-base text-gray-600 dark:text-gray-300">
            Build a professional resume from scratch with our step-by-step guide.
          </p>
          <div
            class="flex items-center space-x-2 text-sm text-gray-500 dark:text-white"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
              <path
                fill-rule="evenodd"
                d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                clip-rule="evenodd"></path>
            </svg>
            <span>200+ resumes created</span>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-between">
          <span
            class="inline-flex items-center text-black dark:text-white hover:text-primary/80 transition-colors"
          >
            Explore <span class="ml-2">→</span>
          </span>
        </div>
      </div>
    </div>
  </a>

  <!-- Resume Tailor -->
  <a
    href="/resume"
    class="block transform hover:scale-105 transition-transform duration-500 rounded-2xl"
  >
    <div
      class="bg-purple-100 dark:bg-purple-900 backdrop-blur-xl rounded-2xl p-8 lg:p-10 h-full relative border border-purple-400 dark:border-purple-600 shadow-[0_4px_16px_0_rgba(168,85,247,0.16)] dark:shadow-[0_4px_16px_0_rgba(168,85,247,0.22)]"
    >
      <div class="absolute top-4 right-4">
        <span
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400"
        >
          New
        </span>
      </div>
      <div class="flex flex-col h-full">
        <div class="flex-grow space-y-4">
          <div class="flex items-center justify-between">
            <h3
              class="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white"
            >
              Tailor Resumes
            </h3>
          </div>
          <p class="text-sm lg:text-base text-gray-600 dark:text-gray-300">
            Craft tailored, ATS-optimized resumes that make you stand out. 
          </p>
          <div
            class="flex items-center space-x-2 text-sm text-gray-500 dark:text-white"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
              <path
                fill-rule="evenodd"
                d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                clip-rule="evenodd"></path>
            </svg>
            <span>600+ resumes tailored</span>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-between">
          <span
            class="inline-flex items-center text-black dark:text-white hover:text-primary/80 transition-colors"
          >
            Explore <span class="ml-2">→</span>
          </span>
          <div
            id="resumeGenerationCounter"
            class="flex items-center gap-1.5 px-2.5 py-1.5 bg-purple-200/80 dark:bg-purple-200/90 rounded-full text-xs font-medium text-purple-800 dark:text-purple-900 shadow-sm transition-all duration-500 hover:shadow-md"
          >
            <span class="loading-text">Loading...</span>
            <span class="loaded-text hidden"></span>
            <div class="w-12 h-1 ml-1 bg-purple-300/50 rounded-full overflow-hidden">
              <div class="resumeGenerationProgress h-full bg-purple-500 rounded-full" style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a>

  <!-- Cover Letter Generator -->
  <a
    href="/cover-letter"
    class="block transform hover:scale-105 transition-transform duration-500 rounded-2xl"
  >
    <div
      class="bg-teal-100 dark:bg-teal-900 backdrop-blur-xl rounded-2xl p-8 lg:p-10 h-full relative border border-teal-400 dark:border-teal-600 shadow-[0_4px_16px_0_rgba(20,184,166,0.16)] dark:shadow-[0_4px_16px_0_rgba(20,184,166,0.22)]"
    >
      <div class="absolute top-4 right-4">
        <span
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400"
        >
          Popular
        </span>
      </div>
      <div class="flex flex-col h-full">
        <div class="flex-grow space-y-4">
          <div class="flex items-center justify-between">
            <h3
              class="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white"
            >
              Craft Cover Letters
            </h3>
          </div>
          <p class="text-sm lg:text-base text-gray-600 dark:text-gray-300">
            Write compelling, job-specific cover letters with tailored
            personalization.
          </p>
          <div
            class="flex items-center space-x-2 text-sm text-gray-500 dark:text-white"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"></path>
            </svg>
            <span>150+ cover letters generated</span>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-between">
          <span
            class="inline-flex items-center text-black dark:text-white hover:text-primary/80 transition-colors"
          >
            Explore <span class="ml-2">→</span>
          </span>
          <div
            id="coverLetterCounter"
            class="flex items-center gap-1.5 px-2.5 py-1.5 bg-teal-200/80 dark:bg-teal-200/90 rounded-full text-xs font-medium text-teal-800 dark:text-teal-900 shadow-sm transition-all duration-500 hover:shadow-md"
          >
            <span class="loading-text">Loading...</span>
            <span class="loaded-text hidden"></span>
            <div class="w-12 h-1 ml-1 bg-teal-300/50 rounded-full overflow-hidden">
              <div class="coverLetterProgress h-full bg-teal-500 rounded-full" style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a>

  <!-- Track Job Applications -->
  <a
    href="/job-tracker"
    class="block transform hover:scale-105 transition-transform duration-500 rounded-2xl"
  >
    <div
      class="bg-blue-100 dark:bg-blue-900 backdrop-blur-xl rounded-2xl p-8 lg:p-10 h-full relative border border-blue-400 dark:border-blue-600 shadow-[0_4px_16px_0_rgba(56,189,248,0.16)] dark:shadow-[0_4px_16px_0_rgba(56,189,248,0.22)]"
    >
      <div class="flex flex-col h-full">
        <div class="flex-grow space-y-4">
          <div class="flex items-center justify-between">
            <h3
              class="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white"
            >
              Track Job Applications
            </h3>
          </div>
          <p class="text-sm lg:text-base text-gray-600 dark:text-gray-300">
            Stay organized and track every application, interview, and
            follow-up in one place
          </p>
          <div
            class="flex items-center space-x-2 text-sm text-gray-500 dark:text-white"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"></path>
            </svg>
            <span>100+ job applications tracked</span>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-between">
          <span
            class="inline-flex items-center text-black dark:text-white hover:text-primary/80 transition-colors"
          >
            Explore <span class="ml-2">→</span>
          </span>
          <div
            id="jobTrackerCounter"
            class="flex items-center gap-1.5 px-2.5 py-1.5 bg-blue-200/80 dark:bg-blue-200/90 rounded-full text-xs font-medium text-blue-800 dark:text-blue-900 shadow-sm transition-all duration-500 hover:shadow-md"
          >
            <span class="loading-text">Loading...</span>
            <span class="loaded-text hidden"></span>
            <div class="w-12 h-1 ml-1 bg-blue-300/50 rounded-full overflow-hidden">
              <div class="jobTrackerProgress h-full bg-blue-500 rounded-full" style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a>
  <a
    href="/interview-prep"
    class="block transform hover:scale-105 transition-transform duration-500 rounded-2xl"
  >
    <div
      class="bg-amber-100 dark:bg-amber-900 backdrop-blur-xl rounded-2xl p-8 lg:p-10 h-full relative border border-amber-400 dark:border-amber-600 shadow-[0_4px_16px_0_rgba(251,191,36,0.16)] dark:shadow-[0_4px_16px_0_rgba(251,191,36,0.22)]"
    >
      <div class="flex flex-col h-full">
        <div class="flex-grow space-y-4">
          <div class="flex items-center justify-between">
            <h3
              class="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white"
            >
              Interview Prep
            </h3>
          </div>
          <p class="text-sm lg:text-base text-gray-600 dark:text-white">
            Sharpen your interview skills with practice questions and expert
            tips.
          </p>
          <div
            class="flex items-center space-x-2 text-sm text-gray-500 dark:text-white"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"></path>
            </svg>
            <span>50+ interviews prepared</span>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-between">
          <span
            class="inline-flex items-center text-black dark:text-white hover:text-primary/80 transition-colors"
          >
            Explore <span class="ml-2">→</span>
          </span>
          <div
            id="interviewPrepCounter"
            class="flex items-center gap-1.5 px-2.5 py-1.5 bg-amber-200/80 dark:bg-amber-200/90 rounded-full text-xs font-medium text-amber-800 dark:text-amber-900 shadow-sm transition-all duration-500 hover:shadow-md"
          >
            <span class="loading-text">Loading...</span>
            <span class="loaded-text hidden"></span>
            <div class="w-12 h-1 ml-1 bg-amber-300/50 rounded-full overflow-hidden">
              <div class="interviewPrepProgress h-full bg-amber-500 rounded-full" style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a>
  <a
    href="/linkedin"
    class="block transform hover:scale-105 transition-transform duration-500 rounded-2xl"
  >
    <div
      class="bg-violet-100 dark:bg-violet-900 backdrop-blur-xl rounded-2xl p-8 lg:p-10 h-full relative border border-violet-400 dark:border-violet-600 shadow-[0_4px_16px_0_rgba(168,85,247,0.18)] dark:shadow-[0_4px_16px_0_rgba(168,85,247,0.24)]"
    >
      <div class="flex flex-col h-full">
        <div class="flex-grow space-y-4">
          <div class="absolute top-4 right-4">
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-violet-100 text-violet-800 dark:bg-violet-900/30 dark:text-violet-400"
            >
              Beta
            </span>
          </div>
          <div class="flex items-center justify-between">
            <h3
              class="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white"
            >
              Optimise LinkedIn
            </h3>
          </div>
          <p class="text-sm lg:text-base text-gray-600 dark:text-white">
            Optimise your LinkedIn profile for maximum impact and
            visibility.
          </p>
          <div
            class="flex items-center space-x-2 text-sm text-gray-500 dark:text-white"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"></path>
            </svg>
            <span>150+ profiles optimised</span>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-between">
          <span
            class="inline-flex items-center text-black dark:text-white hover:text-primary/80 transition-colors"
          >
            Explore <span class="ml-2">→</span>
          </span>
          <div
            id="linkedinOptimisationCounter"
            class="flex items-center gap-1.5 px-2.5 py-1.5 bg-violet-200/80 dark:bg-violet-200/90 rounded-full text-xs font-medium text-violet-800 dark:text-violet-900 shadow-sm transition-all duration-500 hover:shadow-md"
          >
            <span class="loading-text">Loading...</span>
            <span class="loaded-text hidden"></span>
            <div class="w-12 h-1 ml-1 bg-violet-300/50 rounded-full overflow-hidden">
              <div class="linkedinOptimisationProgress h-full bg-violet-500 rounded-full" style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a>
  <!-- Guide Mode -->
  <a
    href="/guide"
    class="block transform hover:scale-105 transition-transform duration-500 rounded-2xl"
  >
    <div
      class="bg-pink-100 dark:bg-pink-900 backdrop-blur-xl rounded-2xl p-8 lg:p-10 h-full relative border border-pink-400 dark:border-pink-600 shadow-[0_4px_16px_0_rgba(236,72,153,0.16)] dark:shadow-[0_4px_16px_0_rgba(236,72,153,0.22)]"
    >
      <div class="absolute top-4 right-4">
        <span
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400"
        >
          New
        </span>
      </div>
      <div class="flex flex-col h-full">
        <div class="flex-grow space-y-4">
          <div class="flex items-center justify-between">
            <h3
              class="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white"
            >
              Get Guidance
            </h3>
          </div>
          <p class="text-sm lg:text-base text-gray-600 dark:text-gray-300">
            Job searching can be lonely. Chat for 24/7 career support.
          </p>

        </div>
        <div class="mt-6 flex items-center justify-between">
          <span
            class="inline-flex items-center text-black dark:text-white hover:text-primary/80 transition-colors"
          >
            Explore <span class="ml-2">→</span>
          </span>
        </div>
      </div>
    </div>
  </a>
</div>
  </Container>
</section>
<script>
  import { authService } from "../lib/auth";
  import { TierManagementService } from "../lib/tierManagement";
  document.addEventListener("DOMContentLoaded", async () => {
    try {
      const user = await authService.getCurrentUser();
      if (user) {
        const userTierProfile = await TierManagementService.getUserTierProfile(
          user.uid
        );

        // Hide counters for pro users
        if (userTierProfile.currentTier === "pro" || userTierProfile.currentTier === "pro_quarterly") {
          const counters = [
            "jobAnalysisCounter",
            "resumeGenerationCounter",
            "coverLetterCounter",
            "interviewPrepCounter",
            "linkedinOptimisationCounter",
            "jobTrackerCounter",
          ];

          counters.forEach((counterId) => {
            const counterElement = document.getElementById(counterId);
            if (counterElement) {
              counterElement.style.display = "none";
            }
          });
        }
      }
    } catch (error) {
      console.error("Error checking user tier:", error);
    }
  });
</script>
<script>
  import { getAuth, onAuthStateChanged } from "firebase/auth";
  import { TierManagementService } from "../lib/tierManagement";
  // Add smooth transitions to all tool links
  document.addEventListener("DOMContentLoaded", () => {
    const toolLinks = document.querySelectorAll('a[href^="/"]');

    toolLinks.forEach(link => {
      link.addEventListener('click', (e) => { // Remove explicit type annotation
        // Skip if modifier keys are pressed (new tab, etc.)
        const mouseEvent = e as MouseEvent; // Cast event to MouseEvent inside the function
        if (mouseEvent.ctrlKey || mouseEvent.metaKey || mouseEvent.shiftKey) return;

        const href = link.getAttribute('href');
        if (!href) return;

        // Skip if it's an anchor link
        if (href.startsWith('#')) return;

        // Prevent default navigation
        e.preventDefault();

        // Navigate with transition
        window.location.href = href;
      });
    });
  });

  const featureCounters = {
    jobAnalysis: "jobAnalysis",
    resumeGeneration: "resumeGeneration",
    coverLetter: "coverLetterGeneration",
    jobTracker: "jobTrackers",
    interviewPrep: "interviewPrep",
    linkedinOptimisation: "linkedinOptimization",
  } as const;

  // Function to update all counters with usage data
  async function updateAllCounters(userId: string) {
    try {
      // Get all feature usage in a single call
      const allUsage = await TierManagementService.getAllFeatureUsage(userId);
      const limit = 5; // Default limit for free tier

      // Update all counters with the retrieved data
      for (const [elementId, featureKey] of Object.entries(featureCounters)) {
        const counter = allUsage[featureKey]?.usageCount || 0;
        const counterElement = document.getElementById(`${elementId}Counter`);

        if (counterElement) {
          // Find the loading and loaded span elements
          const loadingSpan = counterElement.querySelector('.loading-text');
          const loadedSpan = counterElement.querySelector('.loaded-text');

          if (loadingSpan && loadedSpan) {
            // Hide loading text, show loaded text
            loadingSpan.classList.add('hidden');
            loadedSpan.classList.remove('hidden');
            loadedSpan.textContent = `${counter}/${limit}`;
          } else {
             // Fallback if spans not found (e.g., initial hardcoded span)
             const spanElement = counterElement.querySelector('span');
             if (spanElement) {
                spanElement.textContent = `${counter}/${limit}`;
             }
          }


          // Update the progress bar
          const progressBar = counterElement.querySelector(`.${elementId}Progress`);
          if (progressBar) {
            const percentage = Math.min(100, (counter / limit) * 100);
            progressBar.setAttribute('style', `width: ${percentage}%`);

            // Add warning color if approaching limit
            if (percentage > 80) {
              progressBar.classList.add('bg-red-500');
            } else {
              progressBar.classList.remove('bg-red-500');
            }
          }
        }
      }
    } catch (error) {
      console.error("Error fetching all feature usage counts:", error);
      // Optionally show an error state in the counters
      for (const [elementId] of Object.entries(featureCounters)) {
         const counterElement = document.getElementById(`${elementId}Counter`);
         if (counterElement) {
            const loadingSpan = counterElement.querySelector('.loading-text');
            const loadedSpan = counterElement.querySelector('.loaded-text');
            if (loadingSpan) loadingSpan.textContent = 'Error';
            if (loadedSpan) loadedSpan.textContent = 'Error';
         }
      }
    }
  }

  // Update counters when user auth state changes
  onAuthStateChanged(getAuth(), async (user) => {
    if (user) {
      await updateAllCounters(user.uid);
    }
  });

  // Also update counters when the page loads or becomes visible
  document.addEventListener("DOMContentLoaded", async () => {
    const user = getAuth().currentUser;
    if (user) {
      await updateAllCounters(user.uid);
    }
  });

  // Optional: Update when the page becomes visible again (e.g., after switching tabs)
  document.addEventListener("visibilitychange", async () => {
    if (document.visibilityState === "visible") {
      const user = getAuth().currentUser;
      if (user) {
        await updateAllCounters(user.uid);
      }
    }
  });
</script>