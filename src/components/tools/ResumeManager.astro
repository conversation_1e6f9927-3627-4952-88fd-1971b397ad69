---
import Container from "../Container.astro";
import ErrorModal from "../ErrorModal.astro";
import LoadingSpinner from "../LoadingSpinner.astro";
import ProgressBar from "../ProgressBar.astro";
import ButtonLoader from "../ButtonLoader.astro";
---

<section class="relative py-8">
  <!-- Background Gradient Elements -->
  <div
    aria-hidden="true"
    class="absolute inset-0 grid grid-cols-2 -space-x-52 opacity-20 dark:opacity-20 transition-opacity duration-300 ease-in-out"
  >
    <div
      class="blur-[106px] h-56 bg-gradient-to-br from-primary to-purple-400 dark:from-blue-700 dark:to-indigo-600 opacity-40 dark:opacity-20"
    >
    </div>
    <div
      class="blur-[106px] h-32 bg-gradient-to-r from-cyan-400 to-sky-300 dark:to-indigo-600 dark:from-emerald-500 opacity-40 dark:opacity-20"
    >
    </div>
  </div>

  <Container>
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Tips and Information Panel -->
      <div class="lg:col-span-1 space-y-6">
        <!-- Resume Hub Information -->
        <div
          class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg p-6"
        >
          <div class="flex items-center space-x-4 mb-4">
            <div class="bg-gray-100/80 dark:bg-gray-700/50 p-3 rounded-xl">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-7 w-7 text-gray-600 dark:text-gray-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="1.5"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                ></path>
              </svg>
            </div>
            <h2
              class="text-xl md:text-2xl font-bold text-gray-800 dark:text-white"
            >
              Resume Hub
            </h2>
          </div>

          <p class="text-gray-600 text-sm md:text-base dark:text-gray-300 mb-5">
            Centralize and manage all your professional documents in one secure
            place.
          </p>

          <div
            class="flex space-x-4 mt-4"
          >
            <button
              id="resumeUploadButton"
              class="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-white/90 dark:bg-gray-700/50 text-gray-800 dark:text-white rounded-xl
                                   border border-gray-200/50 dark:border-gray-600/30
                                   hover:bg-gray-50 dark:hover:bg-gray-700/70
                                   shadow-sm hover:shadow transition-all duration-300
                                   focus:outline-none"
            >
              <!-- File upload icon -->
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="1.5"
                  d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M16 12l-4-4m0 0l-4 4m4-4v12"
                ></path>
              </svg>
              <span class="text-sm font-medium">Upload File</span>
            </button>
            <button
              id="addResumeBtn"
              class="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-white/90 dark:bg-gray-700/50 text-gray-800 dark:text-white rounded-xl
                                   border border-gray-200/50 dark:border-gray-600/30
                                   hover:bg-gray-50 dark:hover:bg-gray-700/70
                                   shadow-sm hover:shadow transition-all duration-300"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M12 5v14m-7-7h14"></path>
              </svg>
              <span class="text-sm font-medium">Create New</span>
            </button>

            <input id="resumeFileInput" type="file" class="hidden" />
            <div
              id="resumeUploadLoading"
              class="hidden text-gray-600 dark:text-gray-300 text-sm mt-2"
            >
              Uploading...
            </div>
          </div>
        </div>

        <!-- Quick Tips Section -->
        <div
          class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg p-6 hidden sm:block"
        >
          <h4 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
            Quick Tips
          </h4>
          <div class="space-y-4">
            <div class="flex items-start space-x-3">
              <div class="mt-0.5 bg-blue-100/50 dark:bg-blue-900/30 p-1.5 rounded-lg">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 text-blue-500 dark:text-blue-400 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300">
                Tailor resumes for different jobs
              </p>
            </div>
            <div class="flex items-start space-x-3">
              <div class="mt-0.5 bg-blue-100/50 dark:bg-blue-900/30 p-1.5 rounded-lg">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 text-blue-500 dark:text-blue-400 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300">
                Update skills regularly
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- Resume List Panel -->
      <div class="lg:col-span-2 space-y-6">
        <div class="bg-transparent rounded-2xl sm:p-0">
          <div id="resumesList" class="grid gap-5 w-full">
            <!-- Initial Loading State -->
            <div id="resumeLoadingState" class="bg-white/80 dark:bg-gray-800/40 backdrop-blur-xl rounded-2xl p-6 border border-gray-200/50 dark:border-gray-700/30 flex flex-col items-center justify-center text-gray-500 dark:text-gray-300 w-full mx-auto shadow-md">
              <LoadingSpinner size="md" color="primary" className="mb-3" />
              <p class="text-base font-medium">Loading resumes...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Container>

  <!-- Loader UI -->
  <div
    id="uploadLoader"
    class="hidden fixed inset-0 items-center justify-center bg-black/50 backdrop-blur-sm z-[9999]"
  >
    <div class="bg-white/90 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl p-6 max-w-md w-full shadow-xl border border-gray-200/50 dark:border-gray-700/30">
      <div class="flex flex-col items-center justify-center space-y-5">
        <LoadingSpinner
          size="xl"
          color="primary"
          className="mb-4"
        />
        <div class="text-center">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2" id="loaderMessage">Processing your resume...</h3>
          <p class="text-gray-600 dark:text-gray-300 text-sm" id="loaderSubMessage">This may take a few moments</p>
        </div>
        <ProgressBar
          id="uploadProgressBar"
          progress={0}
          color="primary"
          height="xs"
          rounded="full"
          className="w-full"
        />
      </div>
    </div>
  </div>

  <!-- Resume Modal -->
  <div
    id="resumeModal"
    class="fixed inset-0 bg-black/40 backdrop-blur-sm z-50 hidden items-center justify-center p-4 overflow-y-auto transition-opacity duration-300"
  >
    <div
      class="bg-white/90 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl w-full max-w-4xl mx-auto shadow-2xl border border-gray-200/50
               dark:border-gray-700/30 transform ease-in-out
               max-h-[90vh] flex flex-col transition-transform duration-300 scale-95 opacity-0"
      id="resumeModalContent"
    >
      <div
        class="flex items-center justify-between p-6 border-b border-gray-200/50 dark:border-gray-700/30"
      >
        <h3
          class="text-2xl font-bold text-gray-800 dark:text-white"
          id="modalTitle"
        >
          Add New Resume
        </h3>
        <button
          id="closeModalBtn"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-7 w-7"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
              d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="flex-grow overflow-y-auto p-6 space-y-6">
        <form id="resumeForm" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label
                class="block text-sm font-medium text-gray-600 dark:text-gray-300 mb-2"
              >
                Resume Name
              </label>
              <input
                type="text"
                id="resumeName"
                class="w-full px-4 py-3 rounded-xl bg-white/80 dark:bg-gray-700/80
                                   border border-gray-200/50 dark:border-gray-600/30 focus:border-blue-500 focus:ring
                                   focus:ring-blue-500/20 transition-all duration-300"
                required
                placeholder="e.g., Software Engineer Resume"
              />
            </div>
          </div>
          <div>
            <label
              class="block text-sm font-medium text-gray-600 dark:text-gray-300 mb-2"
            >
              Resume Content
            </label>
            <textarea
              id="resumeContent"
              rows="15"
              class="w-full px-4 py-3 rounded-xl bg-white/80 dark:bg-gray-700/80
                               border border-gray-200/50 dark:border-gray-600/30 focus:border-blue-500 focus:ring
                               focus:ring-blue-500/20 transition-all duration-300 resize-y"
              required
              placeholder="Paste your resume content here..."></textarea>
          </div>
        </form>
      </div>

      <div
        class="p-6 border-t border-gray-200/50 dark:border-gray-700/30 flex justify-end space-x-4"
      >
        <button
          type="button"
          id="cancelResumeBtn"
          class="px-6 py-3 text-sm text-gray-600 dark:text-gray-300
                       bg-white/80 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/30
                       hover:bg-gray-50 dark:hover:bg-gray-700/70 rounded-xl transition-all duration-300"
        >
          Cancel
        </button>
        <button
          type="submit"
          form="resumeForm"
          class="px-6 py-3 text-sm bg-white/90 dark:bg-gray-700/50 text-gray-800 dark:text-white
                       border border-gray-200/50 dark:border-gray-600/30
                       rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700/70
                       shadow-sm hover:shadow transition-all duration-300
                       focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:ring-offset-2"
        >
          Save Resume
        </button>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <div
    id="deleteConfirmModal"
    class="fixed inset-0 bg-black/40 backdrop-blur-sm z-50 hidden items-center justify-center p-4 overflow-y-auto transition-opacity duration-300"
  >
    <div
      class="bg-white/90 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl w-full max-w-md mx-auto shadow-2xl border border-gray-200/50
               dark:border-gray-700/30 transform transition-all duration-300 ease-in-out
               flex flex-col p-6 text-center scale-95 opacity-0"
      id="deleteConfirmModalContent"
    >
      <div class="mb-4">
        <div class="mx-auto w-16 h-16 bg-red-100/50 dark:bg-red-900/30 rounded-full flex items-center justify-center mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-8 w-8 text-red-500 dark:text-red-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            ></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">
          Delete Resume
        </h3>
        <p class="text-gray-600 dark:text-gray-300 mb-6">
          Are you sure you want to delete this resume? This action cannot be
          undone.
        </p>
      </div>
      <div class="flex justify-center space-x-4">
        <button
          id="cancelDeleteBtn"
          class="px-6 py-2.5 bg-white/80 dark:bg-gray-700/50 text-gray-700 dark:text-gray-300
                 border border-gray-200/50 dark:border-gray-600/30
                 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700/70 transition-all duration-300"
        >
          Cancel
        </button>
        <button
          id="confirmDeleteBtn"
          class="px-6 py-2.5 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400
                 border border-red-100/50 dark:border-red-800/30
                 rounded-xl hover:bg-red-100 dark:hover:bg-red-900/30 transition-all duration-300"
        >
          Delete
        </button>
      </div>
    </div>
  </div>

  <!-- Error Modal Component -->
  <ErrorModal
    id="resumeManagerErrorModal"
    title="Error"
    message="An error occurred. Please try again."
    isOpen={false}
    zIndex={60}
  />

  <script>
    import { PersistentDocumentService } from "../../lib/persistentDocumentService";
    import { authService } from "../../lib/auth";
    import "../../scripts/resumeManagerOptimistic.js"; // Keep this import
    // Declare the keepLoaderVisible property on the Window interface
    declare global {
      interface Window {
        keepLoaderVisible?: boolean;
      }
    }

    interface Resume {
      id: string;
      name: string;
      content: string;
      lastUpdated: number;
      userId?: string;
      createdAt: number;
    }

    let resumes: Resume[] = [];
    let editingResumeId: string | null = null;
    let currentResumeIdToDelete: string | null = null;
    // Pagination state variables
    let currentPage = 1;
    let itemsPerPage = 4; // Changed from 3 to 4 resumes per page
    let totalPages = 1;

    async function loadResumes() {
      const loadingElement = document.getElementById('resumeLoadingState'); // Get loading element

      try {
        // Store the current page before loading
        const previousPage = currentPage;

        // Load resumes from the service
        resumes = await PersistentDocumentService.loadAllResumes();

        // Sort resumes by lastUpdated (newest first)
        resumes.sort((a, b) => b.lastUpdated - a.lastUpdated);

        // Calculate total pages
        totalPages = Math.ceil(resumes.length / itemsPerPage);

        // Ensure current page is valid
        if (previousPage > totalPages && totalPages > 0) {
          // If we were on a page that no longer exists, go to the last page
          currentPage = totalPages;
        } else if (totalPages > 0 && previousPage <= totalPages) {
          // Otherwise, stay on the same page if it's still valid
          currentPage = previousPage;
        } else {
          // Default to page 1
          currentPage = 1;
        }

        // Hide loading state before rendering
        if (loadingElement) {
          loadingElement.classList.add('hidden');
        }

        renderResumeList(resumes);
      } catch (error) {
        console.error("Failed to load resumes:", error);

        // Hide loading state even on error
        if (loadingElement) {
          loadingElement.classList.add('hidden');
        }

        // Use our centralized error handling
        import("../../lib/errorHandling").then(({ showErrorModal }) => {
          showErrorModal(
            "resumeManagerErrorModal",
            "Error Loading Resumes",
            "Failed to load your resumes. Please try again."
          );
        }).catch(err => {
          console.error("Failed to import error handling utility:", err);
          // Fallback to alert if import fails
          alert("Failed to load resumes. Please try again.");
        });
      }
    }

    function renderResumeList(resumes: Resume[]) {
      const resumesList = document.getElementById("resumesList");
      if (!resumesList) {
        console.error("resumesList element not found!");
        return;
      }

      // Clear existing content
      resumesList.innerHTML = "";

      if (resumes.length === 0) {
        resumesList.innerHTML = `
                <div class="bg-white/80 dark:bg-gray-800/40 backdrop-blur-xl rounded-2xl p-6 border border-gray-200/50 dark:border-gray-700/30 flex flex-col items-center justify-center text-gray-500 dark:text-gray-300 w-full mx-auto shadow-md">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 dark:text-gray-500 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="text-base font-medium">No resumes added yet</p>
                    <p class="text-sm mt-1">Create your first resume to get started</p>
                </div>
            `;
        return;
      }

      // Get resumes for current page
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = Math.min(startIndex + itemsPerPage, resumes.length);
      const currentResumes = resumes.slice(startIndex, endIndex);

      // Render resumes for current page
      currentResumes.forEach((resume) => {
        const resumeCard = createResumeCard(resume);
        resumesList.appendChild(resumeCard);
      });

      // Add pagination controls if needed
      const shouldShowPagination = resumes.length > itemsPerPage; // Only show pagination when we have more than 4 resumes

      if (shouldShowPagination) {
        const paginationContainer = document.createElement("div");
        paginationContainer.className =
          "flex justify-center items-center mt-8 space-x-4 p-4 bg-white/50 dark:bg-gray-800/30 backdrop-blur-sm rounded-xl border border-gray-200/30 dark:border-gray-700/20 shadow-sm";
        paginationContainer.id = "paginationControls"; // Add ID for easier debugging

        // Previous page button
        const prevButton = document.createElement("button");
        prevButton.className = `
                    px-3.5 py-2
                    bg-white/90 dark:bg-gray-700/50
                    text-gray-700 dark:text-gray-300
                    rounded-xl
                    flex items-center gap-1.5
                    text-sm
                    border border-gray-200/50 dark:border-gray-600/30
                    ${currentPage === 1 ? "opacity-50" : "hover:bg-gray-50 dark:hover:bg-gray-700/70 cursor-pointer"}
                    shadow hover:shadow-md
                    transition-all duration-300
                    focus:outline-none
                    focus:ring-2 focus:ring-primary/30
                `;
        prevButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 19l-7-7 7-7" />
                    </svg>
                    <span>Previous</span>
                `;

        // Important: Only disable the button if we're on the first page
        const isFirstPage = currentPage === 1;
        prevButton.disabled = isFirstPage;

        // Add a data attribute to help with debugging
        prevButton.setAttribute('data-page', currentPage.toString());
        prevButton.setAttribute('data-total', totalPages.toString());

        // Add a more direct click handler
        prevButton.onclick = function(e) {
          e.preventDefault();
          e.stopPropagation();

          console.log("Previous button clicked via onclick, current page:", currentPage, "total pages:", totalPages);

          if (currentPage > 1) {
            currentPage--;
            console.log("Decrementing to page:", currentPage);
            renderResumeList(resumes);
          } else {
            console.log("Already on first page, not decrementing");
          }

          return false; // Prevent default and stop propagation
        };

        // Page indicator
        const pageIndicator = document.createElement("div");
        pageIndicator.className = "text-sm font-medium text-gray-700 dark:text-gray-300 bg-white/80 dark:bg-gray-800/40 backdrop-blur-xl px-4 py-2 rounded-xl border border-gray-200/50 dark:border-gray-700/30";
        pageIndicator.textContent = `Page ${currentPage} of ${totalPages}`;

        // Next page button
        const nextButton = document.createElement("button");
        nextButton.className = `
                    px-3.5 py-2
                    bg-white/90 dark:bg-gray-700/50
                    text-gray-700 dark:text-gray-300
                    rounded-xl
                    flex items-center gap-1.5
                    text-sm
                    border border-gray-200/50 dark:border-gray-600/30
                    ${currentPage === totalPages ? "opacity-50" : "hover:bg-gray-50 dark:hover:bg-gray-700/70 cursor-pointer"}
                    shadow hover:shadow-md
                    transition-all duration-300
                    focus:outline-none
                    focus:ring-2 focus:ring-primary/30
                `;
        nextButton.innerHTML = `
                    <span>Next</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5l7 7-7 7" />
                    </svg>
                `;

        // Important: Only disable the button if we're on the last page
        const isLastPage = currentPage === totalPages;
        nextButton.disabled = isLastPage;

        // Add a data attribute to help with debugging
        nextButton.setAttribute('data-page', currentPage.toString());
        nextButton.setAttribute('data-total', totalPages.toString());

        // Add a more direct click handler
        nextButton.onclick = function(e) {
          e.preventDefault();
          e.stopPropagation();

          console.log("Next button clicked via onclick, current page:", currentPage, "total pages:", totalPages);

          if (currentPage < totalPages) {
            currentPage++;
            console.log("Incrementing to page:", currentPage);
            renderResumeList(resumes);
          } else {
            console.log("Already on last page, not incrementing");
          }

          return false; // Prevent default and stop propagation
        };

        // Add direct page links for better navigation
        const pageLinksContainer = document.createElement("div");
        pageLinksContainer.className = "flex items-center space-x-2 mx-2";

        // Only show page links if we have more than one page
        if (totalPages > 1) {
          // Determine which pages to show (show up to 5 pages)
          const pagesToShow: (number | string)[] = [];
          const maxVisiblePages = 5;

          if (totalPages <= maxVisiblePages) {
            // If we have 5 or fewer pages, show all of them
            for (let i = 1; i <= totalPages; i++) {
              pagesToShow.push(i);
            }
          } else {
            // Always include page 1
            pagesToShow.push(1);

            // Calculate the range of pages to show around the current page
            let startPage = Math.max(2, currentPage - 1);
            let endPage = Math.min(totalPages - 1, currentPage + 1);

            // Adjust if we're near the beginning or end
            if (currentPage <= 2) {
              endPage = Math.min(totalPages - 1, 4);
            } else if (currentPage >= totalPages - 1) {
              startPage = Math.max(2, totalPages - 3);
            }

            // Add ellipsis if needed
            if (startPage > 2) {
              pagesToShow.push("...");
            }

            // Add the range of pages
            for (let i = startPage; i <= endPage; i++) {
              pagesToShow.push(i);
            }

            // Add ellipsis if needed
            if (endPage < totalPages - 1) {
              pagesToShow.push("...");
            }

            // Always include the last page
            if (totalPages > 1) {
              pagesToShow.push(totalPages);
            }
          }

          // Create the page links
          pagesToShow.forEach(page => {
            if (page === "...") {
              // Add ellipsis
              const ellipsis = document.createElement("span");
              ellipsis.className = "text-gray-500 dark:text-gray-400 px-1";
              ellipsis.textContent = "...";
              pageLinksContainer.appendChild(ellipsis);
            } else {
              // Add page link
              const pageLink = document.createElement("button");
              const isCurrentPage = page === currentPage;

              pageLink.className = `
                w-8 h-8 flex items-center justify-center rounded-lg text-sm
                ${isCurrentPage
                  ? "bg-blue-500 text-white"
                  : "bg-white/90 dark:bg-gray-700/50 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700/70"}
                border border-gray-200/50 dark:border-gray-600/30
                transition-all duration-300
              `;
              pageLink.textContent = page.toString();
              pageLink.setAttribute("data-page", page.toString());

              if (!isCurrentPage) {
                pageLink.onclick = function(e) {
                  e.preventDefault();
                  e.stopPropagation();

                  // Ensure page is a number
                  if (typeof page === 'number') {
                    currentPage = page;
                    renderResumeList(resumes);
                  }

                  return false;
                };
              }

              pageLinksContainer.appendChild(pageLink);
            }
          });
        }

        // Append pagination controls
        paginationContainer.appendChild(prevButton);
        paginationContainer.appendChild(pageLinksContainer);
        paginationContainer.appendChild(nextButton);
        resumesList.appendChild(paginationContainer);
      }
    }

    // Utility function to generate a consistent color based on a string
    function stringToColor(str: string): string {
      const colors = [
        "bg-blue-500",
        "bg-green-500",
        "bg-purple-500",
        "bg-pink-500",
        "bg-indigo-500",
        "bg-teal-500",
        "bg-orange-500",
        "bg-red-500",
        "bg-cyan-500",
      ];

      // Use a simple hash to consistently map the string to a color
      let hash = 0;
      for (let i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash);
      }

      return colors[Math.abs(hash) % colors.length];
    }

    function createResumeCard(resume: Resume) {
      const card = document.createElement("div");
      card.className = `
            relative bg-white/80 dark:bg-gray-800/40
            backdrop-blur-xl
            border-0
            rounded-2xl
            p-3 sm:p-5
            transition-all duration-300
            shadow-[0_8px_30px_rgba(0,0,0,0.12)] hover:shadow-[0_15px_40px_rgba(0,0,0,0.12)] dark:shadow-[0_15px_35px_rgba(0,0,0,0.4)]
            text-gray-900 dark:text-gray-100
            hover:translate-y-[-3px]
            group
            mb-6
        `;
      card.setAttribute("data-resume-id", resume.id);

      // Create a single row container for all elements
      const rowContainer = document.createElement("div");
      rowContainer.className = "flex flex-col sm:flex-row sm:items-center justify-between w-full gap-3";

      // Left side with resume icon and name
      const leftSide = document.createElement("div");
      leftSide.className = "flex items-center space-x-2 flex-grow w-full sm:w-auto";

      // Resume icon
      const initialCircle = document.createElement("div");
      initialCircle.className = `
            w-10 h-10 rounded-xl
            flex items-center justify-center
            text-white
            ${stringToColor(resume.name)}
            bg-opacity-80 dark:bg-opacity-70
            shadow-sm
            flex-shrink-0
        `;

      // Professional document/resume icon SVG
      const documentIcon = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "svg"
      );
      documentIcon.setAttribute("xmlns", "http://www.w3.org/2000/svg");
      documentIcon.setAttribute("class", "h-6 w-6");
      documentIcon.setAttribute("fill", "none");
      documentIcon.setAttribute("viewBox", "0 0 24 24");
      documentIcon.setAttribute("stroke", "currentColor");
      documentIcon.setAttribute("stroke-width", "1.5");

      const documentPath = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "path"
      );
      documentPath.setAttribute("stroke-linecap", "round");
      documentPath.setAttribute("stroke-linejoin", "round");
      documentPath.setAttribute(
        "d",
        "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
      );

      documentIcon.appendChild(documentPath);
      initialCircle.appendChild(documentIcon);

      // Name and date container
      const nameContainer = document.createElement("div");
      nameContainer.className = "flex flex-col";

      // Resume name
      const nameElement = document.createElement("h3");
      nameElement.className = "text-base font-medium text-gray-900 dark:text-white"; // Adjusted font size and weight
      nameElement.textContent = resume.name;

      // Date information container
      const dateContainer = document.createElement("div");
      dateContainer.className = "flex flex-wrap items-center gap-x-3 gap-y-1 text-xs text-gray-500 dark:text-gray-400";

      // Date added
      const addedDateElement = document.createElement("span");
      const addedDate = resume.createdAt
        ? new Date(resume.createdAt).toLocaleDateString()
        : "Unknown";
      addedDateElement.textContent = `Added: ${addedDate}`;

      // Last updated
      const updatedDateElement = document.createElement("span");
      const updatedDate = resume.lastUpdated
        ? new Date(resume.lastUpdated).toLocaleDateString()
        : "Unknown";
      updatedDateElement.textContent = `Updated: ${updatedDate}`;

      // Add all date elements to container
      dateContainer.appendChild(addedDateElement);
      dateContainer.appendChild(updatedDateElement);

      // Add name and date container to nameContainer
      nameContainer.appendChild(nameElement);
      nameContainer.appendChild(dateContainer);

      // Add icon and name container to left side
      leftSide.appendChild(initialCircle);
      leftSide.appendChild(nameContainer);

      // Right side with action buttons
      const rightSide = document.createElement("div");
      rightSide.className = "flex items-center space-x-3 flex-shrink-0 self-end sm:self-auto";

      // Edit button
      const editButton = document.createElement("button");
      editButton.className = `
            edit-resume-btn
            px-2 sm:px-3 py-1.5
            bg-white/90 dark:bg-gray-700/50
            text-gray-700 dark:text-gray-300
            rounded-xl
            flex items-center gap-1
            text-xs font-medium
            border-0
            hover:bg-gray-50 dark:hover:bg-gray-700/70
            shadow-[0_2px_10px_rgba(0,0,0,0.08)] hover:shadow-[0_8px_20px_rgba(0,0,0,0.12)]
            transition-all duration-300
            focus:outline-none
            focus:ring-2 focus:ring-primary/30
        `;
      editButton.setAttribute("data-id", resume.id);
      editButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <span class="hidden sm:inline">Edit</span>
        `;

      // Delete button
      const deleteButton = document.createElement("button");
      deleteButton.className = `
            delete-resume-btn
            px-2 sm:px-3 py-1.5
            bg-red-50 dark:bg-red-900/20
            text-red-600 dark:text-red-400
            rounded-xl
            flex items-center gap-1
            text-xs font-medium
            border-0
            hover:bg-red-100 dark:hover:bg-red-900/30
            shadow-[0_2px_10px_rgba(220,38,38,0.08)] hover:shadow-[0_8px_20px_rgba(220,38,38,0.12)]
            transition-all duration-300
            focus:outline-none
            focus:ring-2 focus:ring-red-300/30
        `;
      deleteButton.setAttribute("data-id", resume.id);
      deleteButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            <span class="hidden sm:inline">Delete</span>
        `;

      // Copy button
      const copyButton = document.createElement("button");
      copyButton.className = `
            px-2 sm:px-3 py-1.5
            bg-white/90 dark:bg-gray-700/50
            text-gray-700 dark:text-gray-300
            rounded-xl
            flex items-center gap-1
            text-xs font-medium
            border-0
            hover:bg-gray-50 dark:hover:bg-gray-700/70
            shadow-[0_2px_10px_rgba(0,0,0,0.08)] hover:shadow-[0_8px_20px_rgba(0,0,0,0.12)]
            transition-all duration-300
            focus:outline-none
            focus:ring-2 focus:ring-primary/30
      `;
      copyButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            <span class="hidden sm:inline">Copy</span>
      `;
      copyButton.addEventListener("click", (e) => {
        e.stopPropagation();
        navigator.clipboard.writeText(resume.content || "");

        // Show feedback
        const originalText = copyButton.innerHTML;
        copyButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M5 13l4 4L19 7" />
            </svg>
            <span class="hidden sm:inline text-green-500">Copied!</span>
        `;
        copyButton.classList.add("text-green-500");

        setTimeout(() => {
          copyButton.innerHTML = originalText;
          copyButton.classList.remove("text-green-500");
        }, 2000);
      });

      // Add buttons to right side
      rightSide.appendChild(editButton);
      rightSide.appendChild(copyButton);
      rightSide.appendChild(deleteButton);

      // Add left and right sides to the row container
      rowContainer.appendChild(leftSide);
      rowContainer.appendChild(rightSide);

      // Add the row container to the card
      card.appendChild(rowContainer);

      // Add content preview section
      const previewContainer = document.createElement("div");
      previewContainer.className = "mt-3 pt-3 border-t border-gray-100/50 dark:border-gray-700/30";

      // Content preview
      const previewElement = document.createElement("p");
      previewElement.className = "text-xs text-gray-600 dark:text-gray-400 line-clamp-2 font-light";

      // Get a preview of the content (first 150 characters)
      const contentPreview = resume.content
        ? resume.content.substring(0, 150) + (resume.content.length > 150 ? "..." : "")
        : "No content available";
      previewElement.textContent = contentPreview;

      // Add preview to container
      previewContainer.appendChild(previewElement);

      // Add preview container to card
      card.appendChild(previewContainer);

      return card;
    }

    function showDeleteConfirmModal(resumeId: string) {
      console.log("showDeleteConfirmModal called with ID:", resumeId);

      const deleteConfirmModal = document.getElementById("deleteConfirmModal");
      const deleteConfirmModalContent = document.getElementById("deleteConfirmModalContent");

      console.log("Modal elements:", {
        deleteConfirmModal: deleteConfirmModal ? "found" : "not found",
        deleteConfirmModalContent: deleteConfirmModalContent ? "found" : "not found"
      });

      if (deleteConfirmModal && deleteConfirmModalContent) {
        // Store the current resume ID
        currentResumeIdToDelete = resumeId;

        // Set initial state for animation
        deleteConfirmModalContent.classList.add("scale-95", "opacity-0");
        deleteConfirmModalContent.classList.remove("scale-100", "opacity-100");

        // Show modal
        deleteConfirmModal.classList.remove("hidden");
        deleteConfirmModal.classList.add("flex");
        console.log("Modal displayed with flex");

        // Animate modal in
        setTimeout(() => {
          console.log("Animating modal in");
          deleteConfirmModalContent.classList.remove("scale-95", "opacity-0");
          deleteConfirmModalContent.classList.add("scale-100", "opacity-100");
        }, 10);
      } else {
        console.error("Could not find delete confirmation modal elements");
      }
    }

    function hideDeleteConfirmModal() {
      const deleteConfirmModal = document.getElementById("deleteConfirmModal");
      if (deleteConfirmModal) {
        const deleteConfirmModalContent = document.getElementById("deleteConfirmModalContent");

        // Animate modal out
        if (deleteConfirmModalContent) {
          deleteConfirmModalContent.classList.add("scale-95", "opacity-0");
          deleteConfirmModalContent.classList.remove(
            "scale-100",
            "opacity-100"
          );
        }

        // Hide modal after animation
        setTimeout(() => {
          deleteConfirmModal.classList.remove("flex");
          deleteConfirmModal.classList.add("hidden");

          // Reset the current resume ID
          currentResumeIdToDelete = null;
        }, 300);
      }
    }

    async function confirmDeleteResume() {
      if (currentResumeIdToDelete) {
        try {
          await PersistentDocumentService.deleteResume(currentResumeIdToDelete);

          // Check if we need to adjust the current page after deletion
          if (
            resumes.length % itemsPerPage === 1 &&
            currentPage === totalPages &&
            currentPage > 1
          ) {
            currentPage--;
          }

          await loadResumes();
        } catch (error) {
          console.error("Failed to delete resume:", error);
          alert("Failed to delete resume. Please try again.");
        } finally {
          // Always hide the modal
          hideDeleteConfirmModal();
        }
      }
    }

    // This function is now handled directly in the event listener

    async function handleResumeSubmit(event: Event) {
      event.preventDefault();
      event.stopPropagation();

      // Check if we're keeping the loader visible
      console.log("handleResumeSubmit called, keepLoaderVisible =", window.keepLoaderVisible);

      const resumeModal = document.getElementById("resumeModal");
      const nameInput = resumeModal?.querySelector(
        "#resumeName"
      ) as HTMLInputElement;
      const contentInput = resumeModal?.querySelector(
        "#resumeContent"
      ) as HTMLTextAreaElement;
      const submitButton = resumeModal?.querySelector(
        'button[type="submit"]'
      ) as HTMLButtonElement;

      // Disable the submit button to prevent double submission
      if (submitButton) {
        submitButton.disabled = true;
        submitButton.classList.add("opacity-70");
      }

      const resumeData = {
        name: nameInput?.value.trim() || "",
        content: contentInput?.value.trim() || "",
        lastUpdated: Date.now(),
        createdAt: Date.now(),
      };

      if (!resumeData.name || !resumeData.content) {
        // Show error modal using our centralized error handling
        import("../../lib/errorHandling").then(({ showErrorModal }) => {
          showErrorModal(
            "resumeManagerErrorModal",
            "Missing Information",
            "Please fill in both name and content fields."
          );
        }).catch(err => {
          console.error("Failed to import error handling utility:", err);
          // Fallback to alert if import fails
          alert("Please fill in both name and content fields.");
        });
        return;
      }

      try {
        if (editingResumeId) {
          // Updating existing resume
          await PersistentDocumentService.updateResume(
            editingResumeId,
            {
              name: resumeData.name,
              content: resumeData.content,
              lastUpdated: resumeData.lastUpdated,
            }
          );
        } else {
          // Adding new resume
          const newResume = await PersistentDocumentService.createResume(resumeData);

          // Immediately open the newly added resume for editing
          if (newResume && newResume.id) {
            editingResumeId = newResume.id;
          }

          // Navigate to the last page if a new resume is added
          const newTotalPages = Math.ceil((resumes.length + 1) / itemsPerPage);
          if (newTotalPages > totalPages) {
            currentPage = newTotalPages;
          }
        }

        // Reset form and close modal
        if (nameInput) nameInput.value = "";
        if (contentInput) contentInput.value = "";
        editingResumeId = null;

        // Reload resumes to reflect changes
        await loadResumes();

        // Close the modal
        hideModal();

        // Hide the loader if it was shown during file upload
        if (window.keepLoaderVisible) {
          console.log("Resume card should be rendered now, waiting a moment before hiding loader");

          // Make sure the loader is visible
          const uploadLoader = document.getElementById("uploadLoader");
          if (uploadLoader && uploadLoader.classList.contains("hidden")) {
            console.log("Loader was hidden, showing it again in handleResumeSubmit");
            uploadLoader.classList.remove("hidden");
            uploadLoader.classList.add("flex");
          }

          // Add a longer delay to ensure the resume card is fully rendered
          await new Promise(resolve => setTimeout(resolve, 1000));

          console.log("Hiding loader after resume card is rendered");
          if (uploadLoader) {
            uploadLoader.classList.add("hidden");
            uploadLoader.classList.remove("flex");
          }
          window.keepLoaderVisible = false;
        }
      } catch (error) {
        console.error("Failed to save resume:", error);

        // Show error modal using our centralized error handling
        import("../../lib/errorHandling").then(({ showErrorModal }) => {
          showErrorModal(
            "resumeManagerErrorModal",
            "Save Error",
            "Failed to save resume. Please try again."
          );
        }).catch(err => {
          console.error("Failed to import error handling utility:", err);
          // Fallback to alert if import fails
          alert("Failed to save resume. Please try again.");
        });

        // Hide the loader if it was shown during file upload
        if (window.keepLoaderVisible) {
          console.log("Error occurred, waiting a moment before hiding loader");

          // Make sure the loader is visible
          const uploadLoader = document.getElementById("uploadLoader");
          if (uploadLoader && uploadLoader.classList.contains("hidden")) {
            console.log("Loader was hidden, showing it again in error handler");
            uploadLoader.classList.remove("hidden");
            uploadLoader.classList.add("flex");
          }

          // Add a longer delay before hiding the loader
          await new Promise(resolve => setTimeout(resolve, 1000));

          console.log("Hiding loader after error");
          if (uploadLoader) {
            uploadLoader.classList.add("hidden");
            uploadLoader.classList.remove("flex");
          }
          window.keepLoaderVisible = false;
        }
      } finally {
        // Re-enable the submit button regardless of success or failure
        if (submitButton) {
          submitButton.disabled = false;
          submitButton.classList.remove("opacity-70");
        }
      }
    }

    function showModal() {
      const modal = document.getElementById("resumeModal");
      const modalContent = document.getElementById("resumeModalContent");
      const modalTitle = document.getElementById("modalTitle");

      if (modal && modalContent && modalTitle) {
        // Set initial state for animation
        modalContent.classList.add("scale-95", "opacity-0");
        modalContent.classList.remove("scale-100", "opacity-100");

        // Show modal
        modal.classList.remove("hidden");
        modal.classList.add("flex");

        // Update title
        modalTitle.textContent = editingResumeId
          ? "Edit Resume"
          : "Add New Resume";

        const submitButton = modal.querySelector(
          'button[type="submit"]'
        ) as HTMLButtonElement;
        if (submitButton) {
          submitButton.textContent = editingResumeId ? "Update Resume" : "Save Resume";
        }

        // Allow the flex display to take effect before adding animations
        setTimeout(() => {
          modalContent.classList.remove("scale-95", "opacity-0");
          modalContent.classList.add("scale-100", "opacity-100");
        }, 10);
      }
    }

    function hideModal() {
      const modal = document.getElementById("resumeModal");
      const modalContent = document.getElementById("resumeModalContent");
      const form = document.getElementById("resumeForm") as HTMLFormElement;

      if (modal && modalContent) {
        // Start hiding animation
        modalContent.classList.add("scale-95", "opacity-0");
        modalContent.classList.remove("scale-100", "opacity-100");

        // Wait for animation to complete before hiding modal
        setTimeout(() => {
          modal.classList.remove("flex");
          modal.classList.add("hidden");

          // Reset form and editing state
          if (form) form.reset();
          editingResumeId = null;

          const submitButton = modal.querySelector(
            'button[type="submit"]'
          ) as HTMLButtonElement;
          if (submitButton) {
            submitButton.textContent = "Save Resume"; // Reset to default "Save Resume"
          }

          // Dispatch an event to indicate the modal has been closed
          document.dispatchEvent(new CustomEvent('resumeManagerClosed'));
        }, 300);
      }
    }

    // Note: Removed the automatic hideModal() listener for resumeManagerClosed
    // to prevent conflicts with the modal opening/closing logic

    async function handleResumeFileUpload(event: Event) {
      const fileInput = event.target as HTMLInputElement;
      const file = fileInput.files?.[0];

      if (!file) {
        console.error("No file selected");
        return;
      }

      // Get loader elements
      const uploadLoader = document.getElementById("uploadLoader");
      const uploadButton = document.getElementById("resumeUploadButton");
      const loaderMessage = document.getElementById("loaderMessage");
      const loaderSubMessage = document.getElementById("loaderSubMessage");
      const progressBar = document.getElementById("uploadProgressBar");

      // Make sure loader is visible
      if (uploadLoader) {
        uploadLoader.classList.remove("hidden");
        uploadLoader.classList.add("flex");
        console.log("Loader displayed in handleResumeFileUpload");
      }

      // Update loader messages
      if (loaderMessage) loaderMessage.textContent = "Preparing your resume...";
      if (loaderSubMessage) loaderSubMessage.textContent = `Processing ${file.name}`;

      // Set initial progress
      if (progressBar) progressBar.style.width = "10%";

      // Disable upload button
      if (uploadButton) {
        (uploadButton as HTMLButtonElement).disabled = true;
      }

      try {
        // Convert file to base64
        const fileReader = new FileReader();
        fileReader.readAsDataURL(file);

        fileReader.onload = async () => {
          // Update progress
          if (progressBar) progressBar.style.width = "30%";
          if (loaderMessage) loaderMessage.textContent = "Converting file...";

          const base64File = fileReader.result as string;
          const base64Data = base64File.split(",")[1]; // Remove data URL prefix

          try {
            // Generate a unique file name
            const originalFileName = file.name.replace(/\.[^/.]+$/, ""); // Remove file extension
            const fileExtension = file.name.split(".").pop(); // Get file extension
            const randomString = Math.random().toString(36).substring(2, 5); // Random 3-letter string
            const finalFileName = `${originalFileName}_${randomString}.${fileExtension}`;

            // Update progress
            if (progressBar) progressBar.style.width = "50%";
            if (loaderMessage) loaderMessage.textContent = "Uploading resume...";

            // Ensure user is authenticated before making the call
            const user = await authService.getCurrentUser();
            if (!user) {
              throw new Error("Authentication required to upload and process files.");
            }
            const idToken = await user.getIdToken();

            // Send to server-side API
            const response = await fetch("/.netlify/functions/upload-resume", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${idToken}` // Include the ID token
              },
              body: JSON.stringify({
                fileBase64: base64Data,
                fileName: finalFileName,
                fileType: file.type,
              }),
            });

            // Update progress
            if (progressBar) progressBar.style.width = "70%";
            if (loaderMessage) loaderMessage.textContent = "Processing content...";

            // Parse response
            const result = await response.json();

            // Check for error in response
            if (!response.ok) {
              throw new Error(result.error || "Unknown server error");
            }

            if (result.success) {
              // Update progress
              if (progressBar) progressBar.style.width = "90%";
              if (loaderMessage) loaderMessage.textContent = "Creating resume...";

              // Get resume data from response
              const resumeData = result.data;
              const resumeName = finalFileName.replace(/\.[^/.]+$/, ""); // Remove file extension
              const resumeContent = resumeData.text;

              // Create resume directly without using the form
              try {
                // Create the resume object
                const newResumeData = {
                  name: resumeName,
                  content: resumeContent,
                  lastUpdated: Date.now(),
                  createdAt: Date.now(),
                };

                // Update progress
                if (progressBar) progressBar.style.width = "100%";
                if (loaderMessage) loaderMessage.textContent = "Adding to your collection...";

                // Save the resume directly
                await PersistentDocumentService.createResume(newResumeData);

                // Reload the resume list
                await loadResumes();

                // Keep the loader visible for a moment to show success
                if (loaderMessage) loaderMessage.textContent = "Resume added successfully!";
                if (loaderSubMessage) loaderSubMessage.textContent = "Your resume is now available";

                // Wait a moment before hiding the loader
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Hide the loader
                if (uploadLoader) {
                  uploadLoader.classList.add("hidden");
                  uploadLoader.classList.remove("flex");
                  console.log("Loader hidden after successful resume creation");
                }
              } catch (error) {
                console.error("Failed to create resume:", error);
                if (loaderMessage) loaderMessage.textContent = "Failed to create resume";
                if (loaderSubMessage) loaderSubMessage.textContent = "Please try again";

                // Keep the loader visible for a moment to show the error
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Hide the loader
                if (uploadLoader) {
                  uploadLoader.classList.add("hidden");
                  uploadLoader.classList.remove("flex");
                }
              }
            } else {
              // Handle error response
              if (loaderMessage) loaderMessage.textContent = "Upload failed";
              if (loaderSubMessage) loaderSubMessage.textContent = result.error || "Unknown error";

              console.error("Resume upload failed:", result);

              // Show error modal using our centralized error handling
              import("../../lib/errorHandling").then(({ showErrorModal }) => {
                showErrorModal(
                  "resumeManagerErrorModal",
                  "Upload Failed",
                  result.error || "Failed to upload resume. Please try again."
                );
              }).catch(err => {
                console.error("Failed to import error handling utility:", err);
              });

              // Keep the loader visible for a moment to show the error
              await new Promise(resolve => setTimeout(resolve, 2000));

              // Hide the loader
              if (uploadLoader) {
                uploadLoader.classList.add("hidden");
                uploadLoader.classList.remove("flex");
              }
            }
          } catch (error) {
            // Network or parsing errors
            if (loaderMessage) loaderMessage.textContent = "Error occurred";
            if (loaderSubMessage) {
              loaderSubMessage.textContent = error instanceof Error ? error.message : "Unknown error";
            }

            console.error("Resume upload error:", error);

            // Show error modal using our centralized error handling
            import("../../lib/errorHandling").then(({ showErrorModal }) => {
              showErrorModal(
                "resumeManagerErrorModal",
                "Upload Error",
                error instanceof Error ? error.message : "An error occurred during upload. Please try again."
              );
            }).catch(err => {
              console.error("Failed to import error handling utility:", err);
            });

            // Keep the loader visible for a moment to show the error
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Hide the loader
            if (uploadLoader) {
              uploadLoader.classList.add("hidden");
              uploadLoader.classList.remove("flex");
            }
          }
        };
      } catch (error) {
        console.error("Resume upload error:", error);

        if (loaderMessage) loaderMessage.textContent = "Error occurred";
        if (loaderSubMessage) loaderSubMessage.textContent = "Failed to process file";

        // Show error modal using our centralized error handling
        import("../../lib/errorHandling").then(({ showErrorModal }) => {
          showErrorModal(
            "resumeManagerErrorModal",
            "File Processing Error",
            "Failed to process the resume file. Please try again with a different file."
          );
        }).catch(err => {
          console.error("Failed to import error handling utility:", err);
        });

        // Keep the loader visible for a moment to show the error
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Hide the loader
        if (uploadLoader) {
          uploadLoader.classList.add("hidden");
          uploadLoader.classList.remove("flex");
        }
      } finally {
        // Reset progress bar for next time
        if (progressBar) progressBar.style.width = "0%";

        // Re-enable upload button
        if (uploadButton) {
          (uploadButton as HTMLButtonElement).disabled = false;
        }

        // Clear the file input value to allow re-uploading the same file
        fileInput.value = "";
      }
    }

    // File handling is managed through the upload process

    // Add event listeners
    document.addEventListener("DOMContentLoaded", () => {
      loadResumes();

      // Prevent modal from closing when clicking on modal content
      const resumeModalContent = document.getElementById("resumeModalContent");
      if (resumeModalContent) {
        resumeModalContent.addEventListener("click", (e) => {
          e.stopPropagation();
        });
      }

      // Close modal when clicking on backdrop
      const resumeModal = document.getElementById("resumeModal");
      if (resumeModal) {
        resumeModal.addEventListener("click", (e) => {
          if (e.target === resumeModal) {
            hideModal();
          }
        });
      }

      // Add event listener for the showResumeManager event
      document.addEventListener('showResumeManager', (event) => {
        // Dispatch an event to indicate that the resume manager has been shown
        // This prevents the fallback modal from being created
        document.dispatchEvent(new CustomEvent('resumeManagerShown'));

        // Get the callback from the event detail
        const { onResumeSelected } = (event as CustomEvent).detail;

        // Use the existing showModal function to display the modal
        showModal();

        // Set up a callback for when a resume is selected
        const resumesList = document.getElementById("resumesList");
        if (resumesList) {
          const resumeClickHandler = (e: MouseEvent) => {
            const target = e.target as HTMLElement;
            const resumeCard = target.closest('[data-resume-id]') as HTMLElement;

            // Check if the click was on an edit or delete button
            const isActionButton = target.closest('.edit-resume-btn') || target.closest('.delete-resume-btn') || target.closest('.copy-resume-btn');

            if (resumeCard && !isActionButton) { // Only proceed if it's a resume card and not an action button
              const resumeId = resumeCard.getAttribute('data-resume-id');
              if (resumeId) {
                const selectedResume = resumes.find(r => r.id === resumeId);
                if (selectedResume) {
                  // Call the callback with the selected resume
                  onResumeSelected(selectedResume);
  
                  // Hide the modal using the existing function
                  hideModal();
  
                  // Remove the event listener
                  resumesList.removeEventListener('click', resumeClickHandler);
                }
              }
            }
          };

          // Add the event listener
          resumesList.addEventListener('click', resumeClickHandler);
        }
      });

      const resumeForm = document.getElementById("resumeForm");
      if (resumeForm) {
        resumeForm.addEventListener("submit", handleResumeSubmit);
      }

      const addResumeBtn = document.getElementById("addResumeBtn");
      if (addResumeBtn) {
        addResumeBtn.addEventListener("click", (e) => {
          e.preventDefault();
          e.stopPropagation();
          editingResumeId = null;
          const resumeModal = document.getElementById("resumeModal");
          const submitButton = resumeModal?.querySelector(
            'button[type="submit"]'
          ) as HTMLButtonElement;
          if (submitButton) {
            submitButton.textContent = "Save Resume"; // Reset to default "Save Resume" for new resume
          }
          showModal();
        });
      }

      const uploadResumeBtn = document.getElementById("resumeUploadButton");
      if (uploadResumeBtn) {
        uploadResumeBtn.addEventListener("click", (e) => {
          e.preventDefault();
          e.stopPropagation();
          const fileInput = document.getElementById(
            "resumeFileInput"
          ) as HTMLInputElement;
          if (fileInput) fileInput.click();
        });
      }

      const fileInput = document.getElementById("resumeFileInput");
      if (fileInput) {
        // Process file when selected
        fileInput.addEventListener("change", (event) => {
          const input = event.target as HTMLInputElement;
          if (input.files && input.files.length > 0) {
            // Process the file - loader will be shown inside handleResumeFileUpload
            handleResumeFileUpload(event);
          }
        });
      }

      const closeModalBtn = document.getElementById("closeModalBtn");
      if (closeModalBtn) {
        closeModalBtn.addEventListener("click", (e) => {
          e.preventDefault();
          e.stopPropagation();
          hideModal();
        });
      }

      const cancelResumeBtn = document.getElementById("cancelResumeBtn");
      if (cancelResumeBtn) {
        cancelResumeBtn.addEventListener("click", (e) => {
          e.preventDefault();
          e.stopPropagation();
          hideModal();
        });
      }

      const cancelDeleteBtn = document.getElementById("cancelDeleteBtn");
      if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener("click", (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log("Cancel delete button clicked");
          hideDeleteConfirmModal();
        });
      }

      const confirmDeleteBtn = document.getElementById("confirmDeleteBtn");
      if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener("click", (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log("Confirm delete button clicked");
          confirmDeleteResume();
        });
      }

      // Prevent delete modal from closing when clicking on modal content
      const deleteConfirmModalContent = document.getElementById("deleteConfirmModalContent");
      if (deleteConfirmModalContent) {
        deleteConfirmModalContent.addEventListener("click", (e) => {
          e.stopPropagation();
        });
      }

      // Close delete modal when clicking on backdrop
      const deleteConfirmModal = document.getElementById("deleteConfirmModal");
      if (deleteConfirmModal) {
        deleteConfirmModal.addEventListener("click", (e) => {
          if (e.target === deleteConfirmModal) {
            hideDeleteConfirmModal();
          }
        });
      }

      // Event delegation for resume list actions
      const resumesList = document.getElementById("resumesList");
      if (resumesList) {
        resumesList.addEventListener("click", async (event) => {
          const target = event.target as HTMLElement;

          // Delete resume handler
          const deleteButton = target.closest(".delete-resume-btn");
          if (deleteButton) {
            event.preventDefault();
            event.stopPropagation();

            console.log("Delete button clicked");

            const resumeId = deleteButton.getAttribute("data-id");
            if (resumeId) {
              console.log("Showing delete modal for resume ID:", resumeId);
              showDeleteConfirmModal(resumeId);
            } else {
              console.error("No resume ID found on delete button");
            }
            return;
          }

          // Edit resume handler
          const editButton = target.closest(".edit-resume-btn");
          if (editButton) {
            event.preventDefault();
            event.stopPropagation();

            const resumeId = editButton.getAttribute("data-id");
            if (resumeId) {
              // Find the specific resume
              const resume = resumes.find((r) => r.id === resumeId);
              if (resume) {
                // Populate modal with resume data
                editingResumeId = resumeId;

                const nameInput = document.getElementById(
                  "resumeName"
                ) as HTMLInputElement;
                const contentInput = document.getElementById(
                  "resumeContent"
                ) as HTMLTextAreaElement;
                const resumeModal = document.getElementById("resumeModal");
                const submitButton = resumeModal?.querySelector(
                  'button[type="submit"]'
                ) as HTMLButtonElement;

                if (nameInput && contentInput && submitButton) {
                  nameInput.value = resume.name;
                  contentInput.value = resume.content;
                  submitButton.textContent = "Update Resume";
                }

                showModal();
              }
            }
          }
        });
      }

      // The upload button event listener is already defined above
    });
  </script>
</section>