---
import UpgradePrompt from "../UpgradePrompt.astro";
import ErrorModal from "../ErrorModal.astro";
import LoadingSpinner from "../LoadingSpinner.astro";
import ProgressBar from "../ProgressBar.astro";
import <PERSON><PERSON><PERSON>oader from "../ButtonLoader.astro";
---
<section class="relative py-16 sm:py-24 lg:py-32 bg-white dark:bg-gray-950">
  {/* Background Gradient Elements */}
  <div
    aria-hidden="true"
    class="absolute inset-0 grid grid-cols-2 -space-x-52 opacity-0 dark:opacity-0 transition-opacity duration-300 ease-in-out"
  >
    <div
      class="blur-[106px] h-56 bg-gradient-to-br from-primary to-purple-400 dark:from-blue-700 dark:to-indigo-600 opacity-40 dark:opacity-20"
    >
    </div>
    <div
      class="blur-[106px] h-32 bg-gradient-to-r from-cyan-400 to-sky-300 dark:to-indigo-600 dark:from-emerald-500 opacity-40 dark:opacity-20"
    >
    </div>
  </div>
{/* Main Content Container */}
  <div class="relative w-full">
    <div class="container mx-auto px-3 md:px-6 lg:px-8 max-w-6xl">
      {/* Form Wrapper */}
      <div class="w-full mx-auto">
        <form
          id="resumeForm"
          class="w-full space-y-6 sm:space-y-8 bg-white dark:bg-gray-900/80 backdrop-blur-md rounded-3xl p-4 sm:p-8
                    shadow-xl border-gray-100 border dark:border-gray-700/50
                    transition-all duration-300 ease-in-out hover:shadow-2xl"
        >
          {/* Job Description Section */}
          <div class="grid grid-cols-1 gap-3 sm:gap-6">
            <div class="space-y-1.5 sm:space-y-2">
              <label
                for="jobDescription"
                class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                Job Description <span class="text-red-600"> *</span>
              </label>
              <textarea
                id="jobDescription"
                placeholder="Paste the complete job description here."
                rows="6"
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-700/70 bg-white dark:bg-gray-800/30 rounded-xl
                                dark:text-gray-100 focus:border-primary dark:focus:border-primary focus:ring-1 focus:ring-primary
                                outline-none text-md transition-colors duration-200 resize-y"
                required></textarea>
            </div>
          </div>
{/* Resume Input Section */}
      <div class="grid grid-cols-1 gap-3 sm:gap-6">
        <div class="space-y-1.5 sm:space-y-2">
          <label
            class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Resume <span class="text-red-600">*</span>
          </label>
          <input type="file" id="resumeFileInput" class="hidden" />

          {/* Resume Options Selector */}
          <div
            id="resumeOptionsSelector"
            class="flex flex-col sm:flex-row gap-3 mt-2"
          >
            <button
              type="button"
              id="resumeFileUploadButton"
              class="flex-1 py-4 px-4 border-2 border-gray-300 dark:border-gray-700 rounded-xl
          bg-white/10 dark:bg-gray-800/20 hover:bg-white/20 dark:hover:bg-gray-800/30
          transition-all duration-300 text-center group"
            >
              <div
                class="flex flex-col items-center justify-center space-y-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-7 w-7 text-primary group-hover:text-primary-dark transition-colors duration-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  ></path>
                </svg>
                <span
                  class="block font-medium text-gray-900 dark:text-white"
                  >Upload File</span
                >
                <span class="text-xs text-gray-500 dark:text-gray-400"
                  >PDF, DOCX, TXT</span
                >
              </div>
            </button>

            <button
              type="button"
              id="importResumeButton"
              class="flex-1 py-4 px-4 border-2 border-gray-300 dark:border-gray-700 rounded-xl
          bg-white/10 dark:bg-gray-800/20 hover:bg-white/20 dark:hover:bg-gray-800/30
          transition-all duration-300 text-center group"
            >
              <div
                class="flex flex-col items-center justify-center space-y-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-7 w-7 text-primary group-hover:text-primary-dark transition-colors duration-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
                  ></path>
                </svg>
                <span
                  class="block font-medium text-gray-900 dark:text-white"
                  >Resume Manager</span
                >
                <span class="text-xs text-gray-500 dark:text-gray-400"
                  >Select from saved resumes</span
                >
              </div>
            </button>

            <button
              type="button"
              id="enterManuallyButton"
              class="flex-1 py-4 px-4 border-2 border-gray-300 dark:border-gray-700 rounded-xl
          bg-white/10 dark:bg-gray-800/20 hover:bg-white/20 dark:hover:bg-gray-800/30
          transition-all duration-300 text-center group"
            >
              <div
                class="flex flex-col items-center justify-center space-y-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-7 w-7 text-primary group-hover:text-primary-dark transition-colors duration-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 0L11.828 15H9v-2.828l8.586-8.586z"
                  ></path>
                </svg>
                <span
                  class="block font-medium text-gray-900 dark:text-white"
                  >Enter Manually</span
                >
                <span class="text-xs text-gray-500 dark:text-gray-400"
                  >Paste or type resume</span
                >
              </div>
            </button>
          </div>

          {/* Loader UI */}
          <div
            id="uploadLoader"
            class="hidden fixed inset-0 flex items-center justify-center bg-gray-800/50 backdrop-blur-sm z-50"
          >
            <div class="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-xl">
              <LoadingSpinner
                size="xl"
                color="primary"
                label="Uploading resume..."
                showLabel={true}
                className="flex flex-col items-center gap-3"
              />
            </div>
          </div>

          {/* File Added UI */}
          <div
            id="resumeFileAddedUI"
            class="hidden w-full p-6 border-2 border-gray-300 dark:border-gray-700 rounded-xl bg-white/10 dark:bg-gray-800/20 transition-all duration-300"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6 text-green-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <div>
                  <div
                    class="text-sm font-medium text-gray-900 dark:text-white"
                  >
                    Resume Added
                  </div>
                  <div id="resumeFileName" class="text-xs text-gray-500">
                    Document.pdf
                  </div>
                </div>
              </div>
              <button
                type="button"
                id="changeResumeSource"
                class="text-xs bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-full px-3 py-1 transition-colors duration-200"
                >Change</button
              >
            </div>
          </div>

          {/* Manual Entry UI */}
          <div id="manualEntryContainer" class="hidden w-full">
            <textarea
              id="resumeContent"
              name="resumeContent"
              rows="8"
              placeholder="Paste or type your resume here..."
              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-700/70 bg-white dark:bg-gray-800/30 rounded-xl
                        dark:text-gray-100 focus:border-primary dark:focus:border-primary focus:ring-1 focus:ring-primary
                        outline-none text-md transition-colors duration-200 resize-y"
              required></textarea>
            <div class="flex justify-end mt-2">
              <button
                type="button"
                id="cancelManualEntry"
                class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              >
                Back to options
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Custom Instructions Section */}
      <div class="grid grid-cols-1 gap-3 sm:gap-6">
        <div class="space-y-1.5 sm:space-y-2">
          <label
            for="customInstructions"
            class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Custom Instructions <span
              class="text-xs sm:text-sm text-gray-500 dark:text-gray-400 ml-2"
              >(Optional)</span
            >
          </label>
          <textarea
            id="customInstructions"
            placeholder="Optional instructions to help stand out."
            rows="3"
            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-700/70 bg-white dark:bg-gray-800/30 rounded-xl
                    dark:text-gray-100 focus:border-primary dark:focus:border-primary focus:ring-1 focus:ring-primary
                    outline-none text-md transition-colors duration-200 resize-y"
          ></textarea>
        </div>
      </div>

      {/* Action Buttons Section */}
      <div
        class="flex flex-col sm:flex-row justify-center items-center space-y-2.5 sm:space-y-0 sm:space-x-4 pt-4"
      >
        <button
          type="submit"
          id="generateResumeBtn"
          class="w-full sm:w-auto inline-flex h-11 sm:h-12 items-center justify-center px-6 sm:px-8 py-2 sm:py-3
                    text-sm sm:text-base font-semibold
                    text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700
                    dark:text-white dark:bg-gradient-to-r dark:from-blue-500 dark:to-indigo-500 dark:hover:from-blue-600 dark:hover:to-indigo-600
                    rounded-full
                    transition-all duration-300 ease-in-out
                    transform hover:scale-[1.03] active:scale-95
                    focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900
                    shadow-lg hover:shadow-xl"
        >
          <ButtonLoader
            id="generateButtonLoader"
            isLoading={false}
            loadingText="Generating..."
            spinnerPosition="left"
            spinnerSize="md"
            spinnerColor="white"
          >
            <svg
              class="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
              ><path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg
            >
            Generate Resume
          </ButtonLoader>
        </button>
        <button
          type="button"
          id="clearForm"
          class="w-full sm:w-auto inline-flex h-11 sm:h-12 items-center justify-center px-6 sm:px-8 py-2 sm:py-3
                    text-sm sm:text-base font-semibold
                    text-gray-700 bg-gray-100 hover:bg-gray-200
                    dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600
                    border border-transparent
                    rounded-full
                    transition-all duration-300 ease-in-out
                    transform hover:scale-[1.03] active:scale-95
                    focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-offset-gray-900
                    shadow-md hover:shadow-lg"
        >
          <svg
            class="w-5 h-5 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            ><path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            ></path></svg
          >
          Clear
        </button>
      </div>
    </form>
  </div>

  {/* Result Placeholders */}
  <div id="resumeResult" class="mt-6"></div>
</div>
  </div>
</section>
<!-- Upgrade Prompt Component -->
<UpgradePrompt
id="resumeUpgradePrompt"
featureName="resume generations"
isOpen={false}
/>
<!-- Error Modal Component -->
<ErrorModal
id="resumeErrorModal"
title="Error"
message="An error occurred. Please try again."
isOpen={false}
zIndex={60}
/>
<style>
  /* General Styles */
  textarea.border-red-500,
  input.border-red-500,
  button.border-red-500 {
    border-color: rgb(239 68 68);
  }
  #resumeOptionsSelector button.border-red-500:hover {
    border-color: rgb(239 68 68);
    background-color: rgba(239, 68, 68, 0.05);
  }
  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  /* Removed typing animation styles as per comment in original */
  /* @keyframes typing { ... } */
  /* @keyframes blink-caret { ... } */
</style>
<script>
  // Ambient type declarations for CDN libraries
  declare global {
    interface Window {
      jspdf: any; // You can use a more specific type if you have one, e.g., typeof import('jspdf')
      html2canvas: any; // You can use a more specific type, e.g., typeof import('html2canvas')
    }
  }

  // Add CDN links for jsPDF and html2canvas
  const jspdfScript = document.createElement('script');
  jspdfScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
  document.head.appendChild(jspdfScript);

  const html2canvasScript = document.createElement('script');
  html2canvasScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
  document.head.appendChild(html2canvasScript);

  // Imports
  import { TierManagementService } from "../../lib/tierManagement";
  import { authService } from "../../lib/auth";
  import { PersistentDocumentService } from "../../lib/persistentDocumentService";
  import { showUpgradePrompt, hideUpgradePrompt } from "../../lib/upgradePrompt";
  import { setupResumeInput } from "../../lib/resumeInputHandler";
  import { ErrorHandler } from "../../lib/errorHandler";
  import { LoadingManager } from "../../lib/loadingManager";
  import { FormValidator } from "../../lib/formValidator";
  import { ComponentInitializer } from "../../lib/componentInitializer";
  import { APIClient } from "../../lib/apiClient";

  // Encapsulate all logic within a single DOMContentLoaded listener
  document.addEventListener("DOMContentLoaded", async () => {
    // --- 1. Get DOM Element References ---
    const form = document.getElementById(
      "resumeForm"
    ) as HTMLFormElement | null;
    const resultContainer = document.getElementById("resumeResult");
    const jobDescriptionInput = document.getElementById(
      "jobDescription"
    ) as HTMLTextAreaElement | null;

    // Initialize the resume input handler with custom error handling
    setupResumeInput({
      componentId: "resumeGenerator",
      fileInputId: "resumeFileInput",
      fileUploadButtonId: "resumeFileUploadButton",
      fileAddedUIId: "resumeFileAddedUI",
      fileNameElementId: "resumeFileName",
      changeSourceButtonId: "changeResumeSource",
      optionsSelectorId: "resumeOptionsSelector",
      manualEntryContainerId: "manualEntryContainer",
      enterManuallyButtonId: "enterManuallyButton",
      cancelManualEntryButtonId: "cancelManualEntry",
      resumeContentTextareaId: "resumeContent",
      importResumeButtonId: "importResumeButton",
      // Custom error handler that uses our ErrorHandler utility
      onError: (title: string, message: string) => {
        const errorHandler = new ErrorHandler("resumeGenerator");
        errorHandler.showError(title, message);
      }
    });
    // Note: Generate button elements (icon, spinner, text) are referenced within the submit handler

    // Early exit if essential elements are missing
    if (
      !form ||
      !resultContainer ||
      !jobDescriptionInput
    ) {
      console.error(
        "Essential form elements not found. Initialization aborted."
      );
      return;
    }

    // Check access when the component loads
    await checkResumeAccess();

    // --- 2. Define Helper Functions ---

    // Input validation function
    function validateInput(
      input: HTMLTextAreaElement,
      minLength: number = 10
    ): boolean {
      const value = input.value.trim();
      const isValid = value.length >= minLength;
      let errorMessageEl = input.nextElementSibling as HTMLElement | null;

      if (
        !errorMessageEl ||
        !errorMessageEl.classList.contains("validation-error")
      ) {
        errorMessageEl = document.createElement("div");
        errorMessageEl.classList.add(
          "validation-error",
          "text-red-500",
          "text-sm",
          "mt-1"
        );
        input.parentNode?.insertBefore(errorMessageEl, input.nextSibling);
      }

      input.classList.toggle("border-red-500", !isValid);
      input.classList.toggle("border-gray-200", isValid); // Assuming default is gray-200

      if (!isValid) {
        let errorMessage = "";
        if (input.id === "jobDescription") {
          errorMessage = `Job description must be at least ${minLength} characters long.`;
        } else if (input.id === "resumeContent") {
          errorMessage = `Existing resume content must be at least ${minLength} characters long.`;
        } else {
          errorMessage = `Input must be at least ${minLength} characters long.`;
        }
        errorMessageEl.textContent = errorMessage;
        errorMessageEl.style.display = "block";
      } else {
        errorMessageEl.textContent = "";
        errorMessageEl.style.display = "none";
      }
      return isValid;
    }

    // Check if user can generate a resume using ComponentInitializer
    async function checkResumeAccess(): Promise<{
      canAccess: boolean;
      userId: string | null;
      usageInfo?: { current: number, limit: number };
      usageLimitReached?: boolean;
    }> {
      try {
        // Use ComponentInitializer to check feature access
        const initializer = new ComponentInitializer("resumeGenerator");
        const access = await initializer.checkFeatureAccess();

        // Disable submit button if usage limit is reached
        if (access.usageLimitReached) {
          const submitButton = document.getElementById("generateResumeBtn") as HTMLButtonElement;
          if (submitButton) {
            submitButton.disabled = true;
            submitButton.classList.add("opacity-50", "cursor-not-allowed");
            submitButton.title = "Usage limit reached. Please upgrade your plan.";
          }
        }

        return access;
      } catch (error) {
        // console.error("Error checking resume access:", error); // Keep minimal for prod
        return { canAccess: false, userId: null };
      }
    }

    // Handle button click visual feedback
    function handleButtonClick(button: HTMLButtonElement) {
      const clickSound = new Audio(
        "data:audio/wav;base64,UklGRhQCAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhgAEAAA=="
      ); // Tiny WAV sound
      clickSound.volume = 0.2;
      try {
        clickSound.currentTime = 0;
        clickSound.play();
      } catch (error) {
        /* Ignore sound errors */
      }

      button.classList.add("ring-2", "ring-primary", "ring-offset-2");
      setTimeout(() => {
        button.classList.remove("ring-2", "ring-primary", "ring-offset-2");
      }, 300);
    }

    // --- 3. Initial Setup Logic ---
    const storedJob = localStorage.getItem("currentJob");
    if (storedJob && jobDescriptionInput) {
      try {
        const jobData = JSON.parse(storedJob);
        jobDescriptionInput.value = jobData.jobDescription || "";
        localStorage.removeItem("currentJob");
      } catch (e) {
        console.warn("Could not parse stored job data.");
        localStorage.removeItem("currentJob");
      }
    }

    // --- 4. Event Listener Setup ---

    // Main Form Submission
    form.addEventListener("submit", async (e) => {
      e.preventDefault();

      const { canAccess, userId } = await checkResumeAccess();
      if (!canAccess || !userId) return;

      // Get current job description and resume content
      const currentJobDesc = jobDescriptionInput!;
      const resumeHandler = setupResumeInput({componentId: "resumeGenerator"});
      const resumeContent = resumeHandler.getResumeContent();

      // --- Validation ---
      const jobDescriptionValid = validateInput(currentJobDesc, 20);
      let resumeContentValid = true;

      // Check if resume content is provided
      if (resumeContent.trim().length < 50) {
        // Show error message for missing resume content
        const errorMessage = document.createElement("div");
        errorMessage.id = "resumeErrorMessage";
        errorMessage.className = "text-red-500 text-sm mt-2";
        errorMessage.textContent = "Please provide your resume content.";

        // Find the resume content container and append the error
        const resumeContainer = document.querySelector(".space-y-4.sm\\:space-y-5");
        if (resumeContainer) {
          const existingError = document.getElementById("resumeErrorMessage");
          if (existingError) existingError.remove();
          resumeContainer.appendChild(errorMessage);
        }

        resumeContentValid = false;
      } else {
        // Content is present, clear any previous errors
        const existingError = document.getElementById("resumeErrorMessage");
        if (existingError) existingError.remove();
      }

      if (!jobDescriptionValid || !resumeContentValid) {
        // Scroll to first invalid input
        if (!jobDescriptionValid) {
          currentJobDesc?.scrollIntoView({ behavior: "smooth", block: "center" });
        } else {
          document.getElementById("resumeContent")?.scrollIntoView({ behavior: "smooth", block: "center" });
        }
        return;
      }

      // --- Modal Creation & Display ---
      const modal = document.createElement("div");
      modal.innerHTML = `
        <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4">
            <div class="relative w-[95%] max-w-6xl max-h-[85vh] flex rounded-md flex-col">
                <button id="closeModal" class="absolute -top-2 -right-2 z-20 group p-1.5 bg-gray-700 hover:bg-gray-600 rounded-full transition-colors duration-200">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
                <div class="flex-1 overflow-y-auto p-4 md:p-8 bg-gray-100 dark:bg-gray-900 rounded-md shadow-lg">
                    <div id="streamingContent" class="min-h-[300px] relative text-base font-serif text-gray-900 dark:text-white leading-relaxed tracking-normal">
                        <!-- Initial loading state -->
                        <div id="loadingState" class="animate-pulse text-center text-gray-500 dark:text-gray-400 space-y-4 font-sans">
                            <div class="flex justify-center items-center space-x-3"><svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" /></svg><h3 class="text-lg font-semibold">Optimising in progress</h3></div>
                            <p class="text-sm">Analyzing your job description and existing resume to:</p>
                            <ul class="text-xs space-y-2 max-w-md mx-auto text-left">
                                <li class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg><span>Extract key skills and achievements</span></li>
                                <li class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg><span>Optimize for Applicant Tracking Systems (ATS)</span></li>
                                <li class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg><span>Align resume with job description keywords</span></li>
                                <li class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg><span>Highlight your most impactful experiences</span></li>
                            </ul>
                        </div>
                        <!-- Resume content will be inserted here -->
                        <div id="resumeContentDisplay" class="hidden">
                            <style>
                                /* Apply a more standard sans-serif font stack */
                                body { /* This style applies to the content within #resumeContentDisplay */
                                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
                                    margin: 0;
                                    /* padding: 40px; Removed, .container handles its padding */
                                    background: #f5f5f5; /* Background for the area outside .container */
                                    color: #333; /* Default text color */
                                }

                                .container {
                                    max-width: 1000px;
                                    margin: auto;
                                    background: white;
                                    padding: 15px;
                                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                                }

                                .container h1 {
                                    font-size: 24px;
                                    font-weight: bold;
                                    margin-bottom: 5px;
                                    color: #000;
                                    text-align: center;
                                }

                                .container h2 {
                                    font-size: 18px;
                                    font-weight: bold;
                                    margin-top: 25px; /* Adjusted for better spacing */
                                    margin-bottom: 10px; /* Added margin-bottom */
                                    border-bottom: 1px solid #ccc;
                                    padding-bottom: 5px;
                                    color: #000;
                                }

                                .container p {
                                    font-size: 14px;
                                    line-height: 1.6;
                                    margin-bottom: 10px; /* Default margin for paragraphs */
                                    white-space: normal; /* Ensure normal whitespace handling */
                                    word-wrap: break-word; /* Allow breaking long words */
                                }

                                .container .contact {
                                    font-size: 13px;
                                    line-height: 1.5;
                                    margin-bottom: 20px;
                                    text-align: center;
                                }
                                .container .contact span, .container .contact a {
                                    display: inline-block; /* Helps with spacing if needed */
                                    margin-right: 5px; /* Spacing between contact items */
                                }


                               .container .contact {
                                   text-align: center !important;
                               }

                                .container .section {
                                    margin-bottom: 20px;
                                }

                                .container #resumeSummary {
                                    text-align: left;
                                }

                                .container .job-title {
                                    font-weight: bold;
                                }

                                .container .date {
                                    float: right;
                                    font-style: italic;
                                    font-size: 13px; /* Slightly smaller date */
                                    color: #555;
                                }

                                /* Specific styles for lists within .container to override Tailwind Preflight */
                                .container ul {
                                    list-style-type: disc !important; /* Force disc bullets */
                                    list-style-position: outside !important;
                                    padding-left: 20px !important; /* Indentation for bullets */
                                    margin-top: 5px;
                                    margin-bottom: 15px;
                                    /* margin-left is not needed if padding-left is used */
                                }

                                .container li {
                                    display: list-item !important; /* Essential for showing bullets */
                                    font-size: 14px;
                                    line-height: 1.6;
                                    margin-bottom: 5px; /* Space between list items */
                                    padding-left: 5px; /* Small padding if bullet is too close to text */
                                    white-space: normal; /* Ensure normal whitespace handling */
                                    word-wrap: break-word; /* Allow breaking long words */
                                }

                                .container a {
                                    color: #007bff; /* Standard link blue */
                                    text-decoration: none;
                                }

                                a:hover {
                                    text-decoration: underline;
                                }

                                @media print {
                                    body {
                                        background: white;
                                        padding: 0;
                                    }

                                    .container {
                                        box-shadow: none;
                                        padding: 0;
                                    }

                                    a {
                                        color: black;
                                        text-decoration: none;
                                    }
                                }
                            </style>
<div class="container">

                              <h1 id="resumeName" style="display: block; width: 100%; text-align: center;">Prakhar Tomar</h1>
                              <p class="contact">
                                <span id="resumeTitle">Associate Product Manager</span><br>
                                <span id="resumePhone">+91 70228 14452</span> |
                                <span id="resumeEmail"><EMAIL></span> |
                                <a href="#" id="resumePortfolio">Portfolio</a> |
                                <a href="https://linkedin.com/in/prakhartomar" id="resumeLinkedIn">linkedin.com/in/prakhartomar</a> |
                                <span id="resumeLocation">Bengaluru, India</span>
                               </p>

                            <div class="section">
                                <h2>Summary</h2>
                                <p id="resumeSummary">
                                    Results-driven Associate Product Manager with 3+ years of experience in product strategy, UX, and full-stack development across SaaS, e-commerce, and social impact domains. Proven ability to automate processes, improve efficiency, and lead teams. Passionate about leveraging data-driven insights to optimize experiences.
                                </p>
                            </div>

                            <div class="section">
                                <h2>Work Experience</h2>
                                <div id="resumeWorkExperience">
                                    <!-- Work experience items will be inserted here -->
                                    <p><span class="job-title">Product Manager, GDP SDG Accelerator Toolkit</span> <span class="date">Oct ’22 – Mar ’23</span></p>
                                    <ul>
                                        <li>Conceptualized and led education initiatives...</li>
                                        <li>Developed wireframes and UX designs...</li>
                                    </ul>

                                    <p><span class="job-title">Web Developer Specialist, Lenovo India</span> <span class="date">May ’21 – Jul ’22</span></p>
                                    <ul>
                                        <li>Launched 19+ websites, improved UX and SEO...</li>
                                        <li>Automated report generation, improving efficiency by 90%...</li>
                                    </ul>

                                    <p><span class="job-title">Freelance Full Stack Web Developer</span> <span class="date">Aug ’20 – Mar ’21</span></p>
                                    <ul>
                                        <li>Developed high-profile platforms, analyzed user data...</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="section">
                                <h2>Skills</h2>
                                <div id="resumeSkills">
                                    <!-- Skills will be inserted here -->
                                    <p><strong>Product Skills:</strong> Figma, UX Design, PRD, Roadmaps</p>
                                    <p><strong>Tech Stack:</strong> HTML, CSS, JS, React, Node.js, Express, MongoDB, Python</p>
                                </div>
                            </div>

                            <div class="section">
                                <h2>Certifications</h2>
                                <ul id="resumeCertifications">
                                    <li>Product Management – Udemy</li>
                                    <li>UX Design – Google</li>
                                </ul>
                            </div>

                            <div class="section">
                                <h2>Education</h2>
                                <ul id="resumeEducation">
                                    <li>AI Product Management, Duke University – Mar ’24 – Aug ’24</li>
                                    <li>PG Program in Product Management – Jan ’23 – May ’24</li>
                                    <li>MSc. Computer Science, St. Joseph’s – Apr ’19 – Apr ’21</li>
                                </ul>
                            </div>

                            <div class="section">
                                <h2>Projects</h2>
                                <ul id="resumeProjects">
                                    <li>Flight Assist Platform – Designed a safety app for in-flight decision making.</li>
                                    <li>Dashboard for Automobile Showrooms – Streamlined showroom operations.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="resumeActionButtons" class="flex flex-row justify-center items-center p-4 space-x-3 opacity-0 transition-all duration-500 ease-in-out transform translate-y-4 bg-transparent">
                <button id="copyButton" class="inline-flex items-center justify-center px-4 py-4 text-sm font-semibold bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full transition-colors duration-300"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" /></svg><span class="hidden sm:inline">Copy</span></button>
                <button id="downloadButton" class="inline-flex items-center justify-center px-4 py-4 text-sm font-semibold bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full transition-colors duration-300"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" /></svg><span class="hidden sm:inline">Download</span></button>
                <button id="saveResumeButton" class="inline-flex items-center justify-center px-4 py-4 text-sm font-semibold bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full transition-colors duration-300">Resume Manager</button>
                <button id="coverLetterButton" class="inline-flex items-center justify-center px-4 py-4 text-sm font-semibold text-white bg-black dark:bg-white dark:text-black hover:bg-primary-600 rounded-full transition-colors duration-300">Cover Letter</button>
            </div>
        </div>
    </div>`;
  document.body.appendChild(modal);

  // Get modal elements
  const closeModalButton = modal.querySelector("#closeModal");
  const streamingContentEl = modal.querySelector("#streamingContent");
  const loadingStateEl = modal.querySelector("#loadingState");
  const resumeContentDisplayEl = modal.querySelector("#resumeContentDisplay");

  const downloadButton = modal.querySelector(
    "#downloadButton"
  ) as HTMLButtonElement | null;
  const saveResumeButton = modal.querySelector(
    "#saveResumeButton"
  ) as HTMLButtonElement | null;
  const copyButton = modal.querySelector(
    "#copyButton"
  ) as HTMLButtonElement | null;
  const coverLetterButton = modal.querySelector(
    "#coverLetterButton"
  ) as HTMLButtonElement | null;
  const resumeActionButtons = modal.querySelector("#resumeActionButtons");

  // Resume content elements
  const resumeNameEl = modal.querySelector("#resumeName") as HTMLElement;
  const resumeTitleEl = modal.querySelector("#resumeTitle") as HTMLElement;
  const resumePhoneEl = modal.querySelector("#resumePhone") as HTMLElement;
  const resumeEmailEl = modal.querySelector("#resumeEmail") as HTMLElement;
  const resumePortfolioEl = modal.querySelector("#resumePortfolio") as HTMLAnchorElement;
  const resumeLinkedInEl = modal.querySelector("#resumeLinkedIn") as HTMLAnchorElement;
  const resumeLocationEl = modal.querySelector("#resumeLocation") as HTMLElement;
  const resumeSummaryEl = modal.querySelector("#resumeSummary") as HTMLElement;
  const resumeWorkExperienceEl = modal.querySelector("#resumeWorkExperience") as HTMLElement;
  const resumeSkillsEl = modal.querySelector("#resumeSkills") as HTMLElement;
  const resumeCertificationsEl = modal.querySelector("#resumeCertifications") as HTMLElement;
  const resumeEducationEl = modal.querySelector("#resumeEducation") as HTMLElement;
  const resumeProjectsEl = modal.querySelector("#resumeProjects") as HTMLElement;


  if (
    !closeModalButton ||
    !streamingContentEl ||
    !downloadButton ||
    !saveResumeButton ||
    !copyButton ||
    !coverLetterButton ||
    !resumeActionButtons ||
    !loadingStateEl ||
    !resumeContentDisplayEl ||
    !resumeNameEl || !resumeTitleEl || !resumePhoneEl || !resumeEmailEl ||
    !resumePortfolioEl || !resumeLinkedInEl || !resumeLocationEl ||
    !resumeSummaryEl || !resumeWorkExperienceEl || !resumeSkillsEl ||
    !resumeCertificationsEl || !resumeEducationEl || !resumeProjectsEl
  ) {
    console.error("Modal elements not found");
    modal.remove(); // Clean up broken modal
    return;
  }

  // Modal close listeners
  closeModalButton.addEventListener("click", () => modal.remove());
  modal.addEventListener("click", (event) => {
    const modalDialog = modal.querySelector(".relative.w-\\[95\\%]");
    if (modalDialog && !modalDialog.contains(event.target as Node)) {
      modal.remove();
    }
  });

  // --- API Call Preparation ---

  // --- API Fetch & Streaming ---
  // Set button loading state using the LoadingManager utility
  const loadingManager = new LoadingManager("resumeGenerator");
  loadingManager.setButtonLoading("generateButtonLoader", true, "Generating...");

  try {
    // Get custom instructions if they exist
    const customInstructionsEl = document.getElementById("customInstructions") as HTMLTextAreaElement | null;
    const customInstructions = customInstructionsEl?.value || "";

    // Use the APIClient to make the streaming request
    const response = await APIClient.generateResumeStream({
      jobDescription: currentJobDesc.value,
      resumeContent: resumeContent,
      customInstructions: customInstructions
    });

    if (!response.ok || !response.body) {
      const errorText = await response.text();
      throw new Error(
        `API request failed: ${response.status} ${response.statusText}. ${errorText}`
      );
    }

    let fullResumeData = "";
    const reader = response.body.getReader();
    const decoder = new TextDecoder(); // Default UTF-8

    async function processStream() {
      while (true) {
        const { done, value } = await reader.read();
        // Decode the current chunk. Pass stream: true if expecting more chunks.
        const chunk = decoder.decode(value, { stream: !done });
        const currentChunkLines = chunk.split('\n');

        for (const line of currentChunkLines) {
          if (line.startsWith("data: ")) { // Check for "data: " prefix (note the space)
            // Extract payload after "data: "
            let dataPayload = line.substring("data: ".length);

            // Check for the [DONE] marker specifically by trimming it
            if (dataPayload.trim() === "[DONE]") {
              return; // End of stream signal
            }
            // Append the raw dataPayload, preserving all its original spacing
            fullResumeData += dataPayload;
          }
          // SSE comments (starting with ':') and empty lines are implicitly ignored
        }

        if (done) {
          break;
        }
      }
    }

    await processStream();

    // Once streaming is complete, parse the full JSON and populate the template
    let parsedResume: any;
    try {
        // Trim the entire accumulated string once before parsing,
        // to remove any leading/trailing whitespace from the overall JSON structure.
        const finalJsonToParse = fullResumeData.trim();
        if (!finalJsonToParse) {
            throw new Error("Received empty or whitespace-only resume data from the server.");
        }
        parsedResume = JSON.parse(finalJsonToParse);
    } catch (jsonParseError) {
        console.error("Failed to parse full resume JSON:", jsonParseError);
        console.error("Accumulated data that failed to parse:", fullResumeData); // Log the problematic data
        streamingContentEl.innerHTML = `<p class="text-red-500">Error: Could not parse generated resume data. Please try again. Details: ${jsonParseError.message}</p>`;
        loadingStateEl.classList.add("hidden");
        resumeContentDisplayEl.classList.remove("hidden"); // Show the area for the error message
        return;
    }

    // Hide loading state and show resume content display
    loadingStateEl.classList.add("hidden");
    resumeContentDisplayEl.classList.remove("hidden");

    // Populate the HTML template with parsed data
    resumeNameEl.textContent = parsedResume.name || "Prakhar Tomar";
    resumeTitleEl.textContent = parsedResume.contact?.title || "Associate Product Manager";
    resumePhoneEl.textContent = parsedResume.contact?.phone || "+91 70228 14452";
    resumeEmailEl.textContent = parsedResume.contact?.email || "<EMAIL>";
    resumePortfolioEl.href = parsedResume.contact?.portfolio || "#";
    resumeLinkedInEl.href = parsedResume.contact?.linkedin || "https://linkedin.com/in/prakhartomar";
    resumeLocationEl.textContent = parsedResume.contact?.location || "Bengaluru, India";
    resumeSummaryEl.textContent = parsedResume.summary || "";

    // Populate Work Experience
    if (parsedResume.workExperience && Array.isArray(parsedResume.workExperience)) {
        resumeWorkExperienceEl.innerHTML = parsedResume.workExperience.map((job: any) => `
            <p><span class="job-title">${job.title || ''}</span> <span class="date">${job.date || ''}</span></p>
            <ul>
                ${(job.details || []).map((detail: string) => `<li>${detail}</li>`).join('')}
            </ul>
        `).join('');
    } else {
        resumeWorkExperienceEl.innerHTML = `
            <p><span class="job-title">Product Manager, GDP SDG Accelerator Toolkit</span> <span class="date">Oct ’22 – Mar ’23</span></p>
            <ul>
                <li>Conceptualized and led education initiatives...</li>
                <li>Developed wireframes and UX designs...</li>
            </ul>

            <p><span class="job-title">Web Developer Specialist, Lenovo India</span> <span class="date">May ’21 – Jul ’22</span></p>
            <ul>
                <li>Launched 19+ websites, improved UX and SEO...</li>
                <li>Automated report generation, improving efficiency by 90%...</li>
            </ul>

            <p><span class="job-title">Freelance Full Stack Web Developer</span> <span class="date">Aug ’20 – Mar ’21</span></p>
            <ul>
                <li>Developed high-profile platforms, analyzed user data...</li>
            </ul>
        `;
    }

    // Populate Skills
    if (parsedResume.skills) {
        let skillsHtml = '';
        if (parsedResume.skills.product) {
            skillsHtml += `<p><strong>Product Skills:</strong> ${parsedResume.skills.product}</p>`;
        }
        if (parsedResume.skills.tech) {
            skillsHtml += `<p><strong>Tech Stack:</strong> ${parsedResume.skills.tech}</p>`;
        }
        resumeSkillsEl.innerHTML = skillsHtml || `
            <p><strong>Product Skills:</strong> Figma, UX Design, PRD, Roadmaps</p>
            <p><strong>Tech Stack:</strong> HTML, CSS, JS, React, Node.js, Express, MongoDB, Python</p>
        `;
    } else {
        resumeSkillsEl.innerHTML = `
            <p><strong>Product Skills:</strong> Figma, UX Design, PRD, Roadmaps</p>
            <p><strong>Tech Stack:</strong> HTML, CSS, JS, React, Node.js, Express, MongoDB, Python</p>
        `;
    }

    // Populate Certifications
    if (parsedResume.certifications && Array.isArray(parsedResume.certifications)) {
        resumeCertificationsEl.innerHTML = parsedResume.certifications.map((cert: string) => `<li>${cert}</li>`).join('');
    } else {
        resumeCertificationsEl.innerHTML = `
            <li>Product Management – Udemy</li>
            <li>UX Design – Google</li>
        `;
    }

    // Populate Education
    if (parsedResume.education && Array.isArray(parsedResume.education)) {
        resumeEducationEl.innerHTML = parsedResume.education.map((edu: string) => `<li>${edu}</li>`).join('');
    } else {
        resumeEducationEl.innerHTML = `
            <li>AI Product Management, Duke University – Mar ’24 – Aug ’24</li>
            <li>PG Program in Product Management – Jan ’23 – May ’24</li>
            <li>MSc. Computer Science, St. Joseph’s – Apr ’19 – Apr ’21</li>
        `;
    }

    // Populate Projects
    if (parsedResume.projects && Array.isArray(parsedResume.projects)) {
        resumeProjectsEl.innerHTML = parsedResume.projects.map((project: string) => `<li>${project}</li>`).join('');
    } else {
        resumeProjectsEl.innerHTML = `
            <li>Flight Assist Platform – Designed a safety app for in-flight decision making.</li>
            <li>Dashboard for Automobile Showrooms – Streamlined showroom operations.</li>
        `;
    }


    // --- Post-Streaming Actions ---
    // Track usage using ComponentInitializer
    if (userId) {
      try {
        const initializer = new ComponentInitializer("resumeGenerator");
        await initializer.trackFeatureUsage(userId);
      } catch (trackingError) {
        // Log the error but don't interrupt the user experience
        console.warn("Failed to track resume usage, but content was generated successfully:", trackingError);
      }
    }

    // Show action buttons
    resumeActionButtons.classList.remove("opacity-0", "translate-y-4");
    resumeActionButtons.classList.add("opacity-100", "translate-y-0");

    // --- Modal Button Event Listeners ---
    downloadButton.addEventListener("click", async () => {
      handleButtonClick(downloadButton);
      const resumeContainerToCapture = resumeContentDisplayEl.querySelector('.container') as HTMLElement;

      if (!resumeContainerToCapture) {
        console.error("Resume container for PDF capture not found.");
        alert("Error: Could not find resume content to generate PDF.");
        return;
      }

      // Ensure libraries are loaded
      if (typeof window.jspdf === 'undefined' || typeof window.html2canvas === 'undefined') {
        console.error("jsPDF or html2canvas is not loaded yet.");
        alert("PDF generation library is still loading. Please try again in a moment.");
        // Optionally, you could add a small delay and retry, or disable the button until loaded.
        return;
      }
      const { jsPDF } = window.jspdf; // Destructure to get the jsPDF constructor

      try {
        downloadButton.disabled = true;
        const downloadButtonSpan = downloadButton.querySelector('span');
        if (downloadButtonSpan) {
          downloadButtonSpan.textContent = 'Generating PDF...';
        }

        // Explicitly use window.html2canvas
        // Store original styles to revert later
        const originalContainerWidth = resumeContainerToCapture.style.width;
        const originalContainerMaxWidth = resumeContainerToCapture.style.maxWidth;
        const originalContainerPadding = resumeContainerToCapture.style.padding;
        const originalContainerBoxShadow = resumeContainerToCapture.style.boxShadow;
        const originalContainerBackground = resumeContainerToCapture.style.background;
        const originalContainerBoxSizing = resumeContainerToCapture.style.boxSizing;

        // Apply styles for PDF capture
        resumeContainerToCapture.style.width = '900px'; // A4 width in pixels at 96 DPI
        resumeContainerToCapture.style.maxWidth = '900px';
        resumeContainerToCapture.style.padding = '2px';
        resumeContainerToCapture.style.boxShadow = 'none';
        resumeContainerToCapture.style.background = 'white';
        resumeContainerToCapture.style.boxSizing = 'border-box';

        const canvas = await window.html2canvas(resumeContainerToCapture, {
          scale: 2, // Increase scale for better resolution
          useCORS: true, // If there are any external images/fonts (though unlikely here)
          logging: false, // Disable html2canvas logging in console
          onclone: (documentClone) => {
            // Apply styles to the body of the cloned document for a consistent canvas background
            const body = documentClone.body;
            body.style.margin = '0';
            body.style.padding = '0';
            body.style.background = 'white';
            body.style.fontSize = '12pt'; // Ensure a readable base font size for PDF

            // Explicitly set list styles for html2canvas capture
            const ulElements = documentClone.querySelectorAll('ul');
            ulElements.forEach(ul => {
                ul.style.listStyleType = 'disc';
                ul.style.listStylePosition = 'outside';
                ul.style.paddingLeft = '4px';
            });

            // Explicitly center the resume name and contact info for html2canvas capture
            const resumeNameElement = documentClone.querySelector('#resumeName') as HTMLElement;
            if (resumeNameElement) {
                resumeNameElement.style.textAlign = 'center';
            }
            const contactInfoElement = documentClone.querySelector('.contact') as HTMLElement;
            if (contactInfoElement) {
                contactInfoElement.style.textAlign = 'center';
            }
          }
        });

        // Revert styles after capture
        resumeContainerToCapture.style.width = originalContainerWidth;
        resumeContainerToCapture.style.maxWidth = originalContainerMaxWidth;
        resumeContainerToCapture.style.padding = originalContainerPadding;
        resumeContainerToCapture.style.boxShadow = originalContainerBoxShadow;
        resumeContainerToCapture.style.background = originalContainerBackground;
        resumeContainerToCapture.style.boxSizing = originalContainerBoxSizing;

        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF({
          orientation: 'portrait',
          unit: 'pt', // points
          format: 'a4' // A4 paper size
        });

        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();
        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;

        // Calculate the aspect ratio
        const pageMargin = 2; // Desired margin in points for the PDF

        const contentAreaWidth = pdfWidth - (2 * pageMargin);
        const contentAreaHeight = pdfHeight - (2 * pageMargin);

        let renderWidth = canvasWidth;
        let renderHeight = canvasHeight;
        const canvasAspectRatio = canvasWidth / canvasHeight;

        // Scale to fit contentAreaWidth
        if (renderWidth > contentAreaWidth) {
          renderWidth = contentAreaWidth;
          renderHeight = renderWidth / canvasAspectRatio;
        }

        // If still too tall after fitting width, scale to fit contentAreaHeight
        if (renderHeight > contentAreaHeight) {
          renderHeight = contentAreaHeight;
          renderWidth = renderHeight * canvasAspectRatio;
        }
        
        // Calculate offsets to center the (potentially smaller) renderWidth/Height within the contentArea
        const xOffset = pageMargin + (contentAreaWidth - renderWidth) / 2;
        const yOffset = pageMargin + (contentAreaHeight - renderHeight) / 2;

        pdf.addImage(imgData, 'PNG', xOffset, yOffset, renderWidth, renderHeight);
        pdf.save('generated-resume.pdf');

      } catch (pdfError) {
        console.error("Error generating PDF:", pdfError);
        alert("Failed to generate PDF. Please try again.");
      } finally {
        downloadButton.disabled = false;
        const downloadButtonSpan = downloadButton.querySelector('span');
        if (downloadButtonSpan) {
          downloadButtonSpan.textContent = 'Download';
        }
      }
    });

    saveResumeButton.addEventListener("click", async () => {
      handleButtonClick(saveResumeButton);
      try {
        const user = await authService.getCurrentUser();
        if (!user) return; // Should ideally prompt login

        const resumeName = prompt(
          "Enter a name for this resume:",
          `Resume1`
        );
        if (!resumeName) return;

        await PersistentDocumentService.createResume({
          name: resumeName,
          content: JSON.stringify(parsedResume), // Save the parsed JSON data
          userId: user.uid,
        });

        // Show success modal
        const successModal = document.createElement("div");
        successModal.innerHTML = `<div class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"><div class="bg-white dark:bg-gray-900 rounded-2xl p-6 max-w-md w-full text-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-green-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg><h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Resume Saved</h2><p class="text-gray-600 dark:text-gray-400 mb-6">Your resume has been successfully saved to the Resume Manager.</p><button id="closeSuccessModal" class="bg-black dark:bg-white text-white dark:text-black px-4 py-2 rounded-full">Close</button></div></div>`;
        document.body.appendChild(successModal);
        successModal
          .querySelector("#closeSuccessModal")
          ?.addEventListener("click", () => successModal.remove());
      } catch (error) {
        // Show error modal
        const errorModal = document.createElement("div");
        errorModal.innerHTML = `<div class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"><div class="bg-white dark:bg-gray-900 rounded-2xl p-6 max-w-md w-full text-center"><h2 class="text-2xl font-bold text-red-600 mb-4">Save Error</h2><p class="text-gray-600 dark:text-gray-400 mb-6">Unable to save resume. Please try again later.</p><button id="closeErrorModal" class="bg-primary text-white px-4 py-2 rounded-full">Close</button></div></div>`;
        document.body.appendChild(errorModal);
        errorModal
          .querySelector("#closeErrorModal")
          ?.addEventListener("click", () => errorModal.remove());
      }
    });

    copyButton.addEventListener("click", () => {
      handleButtonClick(copyButton);
      // Copy the text content of the resume display
      const textContent = resumeContentDisplayEl.textContent || "";
      navigator.clipboard.writeText(textContent).then(() => {
        const originalText =
          copyButton.querySelector("span")?.textContent || "Copy";
        const svg = copyButton.innerHTML.match(/<svg.*<\/svg>/)?.[0] || "";
        copyButton.innerHTML = `${svg}<span class="hidden sm:inline">Copied!</span>`;
        copyButton.classList.replace("bg-gray-200", "bg-green-500");
        copyButton.classList.replace(
          "dark:bg-gray-700",
          "dark:bg-green-600"
        );
        setTimeout(() => {
          copyButton.innerHTML = `${svg}<span class="hidden sm:inline">${originalText}</span>`;
          copyButton.classList.replace("bg-green-500", "bg-gray-200");
          copyButton.classList.replace(
            "dark:bg-green-600",
            "dark:bg-gray-700"
          );
        }, 2000);
      });
    });

    coverLetterButton.addEventListener("click", () => {
      handleButtonClick(coverLetterButton);
      window.location.href = "/cover-letter";
    });
  } catch (error) {
    console.error("Resume generation failed:", error);

    // Show error in the streaming content
    streamingContentEl.innerHTML = `<p class="text-red-500">Error generating resume: ${error instanceof Error ? error.message : "Unknown error"}</p>`;

    // Show error modal using our ErrorHandler utility
    const errorHandler = new ErrorHandler("resumeGenerator");
    errorHandler.showError(
      "Resume Generation Error",
      "There was an error generating your resume. Please try again."
    );

    // Optionally hide action buttons on error
    resumeActionButtons.classList.add("hidden");
  } finally {
    // Reset button loading state using the LoadingManager utility
    loadingManager.setButtonLoading("generateButtonLoader", false);
  }
}); // End of form submit listener

// The resume input functionality is now handled by the resumeInputHandler utility

// Clear Form Button
document.getElementById("clearForm")?.addEventListener("click", (e) => {
  e.preventDefault();

  // Clear form fields
  if (jobDescriptionInput) jobDescriptionInput.value = "";

  // Clear custom instructions if it exists
  const customInstructionsInput = document.getElementById("customInstructions") as HTMLTextAreaElement | null;
  if (customInstructionsInput) customInstructionsInput.value = "";

  // Use the resume handler to clear resume content
  setupResumeInput({componentId: "resumeGenerator"}).clearResumeContent();

  // Clear validation states
  jobDescriptionInput?.classList.remove("border-red-500");
  document
    .querySelectorAll(".validation-error")
    .forEach((el) => el.remove());
  const resumeErrorMsg = document.getElementById("resumeErrorMessage");
  if (resumeErrorMsg) resumeErrorMsg.remove();
});
});
</script>
