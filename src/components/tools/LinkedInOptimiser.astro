---
import UpgradePrompt from "../UpgradePrompt.astro";
import ErrorModal from "../ErrorModal.astro";
import ProgressBar from "../ProgressBar.astro";
import ButtonLoader from "../ButtonLoader.astro";
---

<section class="relative py-16 sm:py-24 lg:py-32 bg-white dark:bg-gray-950">
  <div
    aria-hidden="true"
    class="absolute inset-0 grid grid-cols-2 -space-x-52 opacity-0 dark:opacity-0 transition-opacity duration-300 ease-in-out"
  >
    <div
      class="blur-[106px] h-56 bg-gradient-to-br from-primary to-purple-400 dark:from-blue-700 dark:to-indigo-600 opacity-40 dark:opacity-20"
    >
    </div>
    <div
      class="blur-[106px] h-32 bg-gradient-to-r from-cyan-400 to-sky-300 dark:to-indigo-600 dark:from-emerald-500 opacity-40 dark:opacity-20"
    >
    </div>
  </div>
  <div class="relative w-full">
    <div class="container mx-auto px-3 md:px-6 lg:px-8 max-w-6xl">
      <div class="w-full mx-auto">
        <form
          id="linkedinOptimiserForm"
          class="w-full space-y-6 sm:space-y-8 bg-white dark:bg-gray-900/80 backdrop-blur-md rounded-3xl p-4 sm:p-8
                    shadow-xl border-gray-100 border dark:border-gray-700/50
                    transition-all duration-300 ease-in-out hover:shadow-2xl"
        >
          <div class="space-y-4 sm:space-y-5">
            <!-- Success State -->
            <div
              id="linkedinFileAddedUI"
              class="hidden w-full p-6 border-2 border-gray-300 dark:border-gray-700/70 rounded-xl bg-white/10 dark:bg-gray-800/20 min-h-[100px]"
            >
              <div class="flex items-center justify-center mb-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-10 w-10 text-green-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <p
                id="linkedinFileName"
                class="text-center text-lg text-gray-900 dark:text-white font-medium"
              >
                LinkedIn PDF added successfully
              </p>
              <button
                id="changeLinkedinSource"
                class="mt-4 px-4 py-2 bg-black text-white dark:bg-white dark:text-black rounded-full text-sm hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200"
              >
                Upload Another
              </button>
            </div>

            <div id="linkedinOptionsSelector">
              <label
                for="linkedin-upload"
                class="block mb-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                LinkedIn Profile PDF: <span class="text-red-600">*</span>
                <span class="text-xs text-gray-500 dark:text-gray-400"
                  >(You can download your LinkedIn profile as a PDF by going to
                  your profile, clicking on 'Resources', and selecting 'Save to
                  PDF.')</span
                >
              </label>
              <button
                type="button"
                id="linkedinFileUploadButton"
                class="relative flex flex-col items-center justify-center p-2 border-2 border-gray-200 dark:border-gray-700 rounded-xl bg-white/10 dark:bg-gray-800/20 hover:bg-gray-50 dark:hover:bg-gray-700/40 transition-all duration-300 group w-full"
              >
                <div
                  class="flex flex-col items-center justify-center space-y-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-7 w-7 text-primary group-hover:text-primary-dark transition-colors duration-300"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    ></path>
                  </svg>
                  <span class="font-medium text-gray-900 dark:text-white"
                    >Upload File</span
                  >
                  <span id="linkedinUploadLoading" class="text-xs text-gray-500 dark:text-gray-400 mt-1 hidden">Processing...</span>
                  <span class="text-xs text-gray-500 dark:text-gray-400 mt-1"
                    >PDF only</span
                  >
                </div>
              </button>
              <input
                type="file"
                accept="application/pdf"
                id="linkedin-upload"
                required
                style="display: none;"
              />
            </div>
          </div>

          <!-- Resume -->
          <div class="space-y-4 sm:space-y-5">
            <div class="flex justify-between items-center">
              <label
                class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                Resume <span class="text-red-600">*</span>
              </label>
            </div>
          </div>

          <!-- Hidden file input -->
          <input type="file" id="resumeFileInput" style="display: none;" />

          <!-- Resume Options Selector -->
          <div
            id="resumeOptionsSelector"
            class="grid grid-cols-3 md:grid-cols-3 xs:gap-2 sm:gap-4 md:gap-4 lg:gap-4 gap-2"
          >
            <button
              type="button"
              id="resumeFileUploadButton"
              class="relative flex flex-col items-center justify-center p-2 border-2 border-gray-200 dark:border-gray-700 rounded-xl bg-white/10 dark:bg-gray-800/20 hover:bg-gray-50 dark:hover:bg-gray-700/40 transition-all duration-300 group"
            >
              <div class="flex flex-col items-center justify-center space-y-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-7 w-7 text-primary group-hover:text-primary-dark transition-colors duration-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  ></path>
                </svg>
                <span class="font-medium text-gray-900 dark:text-white"
                  >Upload File</span
                >
                <span id="resumeUploadLoading" class="text-xs text-gray-500 dark:text-gray-400 mt-1 hidden">Processing...</span>
                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1"
                  >PDF, DOCX, TXT</span
                >
              </div>
            </button>

            <button
              type="button"
              id="importResumeButton"
              class="relative flex flex-col items-center justify-center p-2 border-2 border-gray-200 dark:border-gray-700 rounded-xl bg-white/10 dark:bg-gray-800/20 hover:bg-gray-50 dark:hover:bg-gray-700/40 transition-all duration-300 group"
            >
              <div class="flex flex-col items-center justify-center space-y-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-7 w-7 text-primary group-hover:text-primary-dark transition-colors duration-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
                  ></path>
                </svg>
                <span class="font-medium text-gray-900 dark:text-white"
                  >Resume Manager</span
                >
                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1"
                  >Select saved resume</span
                >
              </div>
            </button>

            <button
              type="button"
              id="enterManuallyButton"
              class="relative flex flex-col items-center justify-center p-2 border-2 border-gray-200 dark:border-gray-700 rounded-xl bg-white/10 dark:bg-gray-800/20 hover:bg-gray-50 dark:hover:bg-gray-700/40 transition-all duration-300 group"
            >
              <div class="flex flex-col items-center justify-center space-y-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-7 w-7 text-primary group-hover:text-primary-dark transition-colors duration-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  ></path>
                </svg>
                <span class="font-medium text-gray-900 dark:text-white"
                  >Enter Manually</span
                >
                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1"
                  >Paste or type resume</span
                >
              </div>
            </button>
          </div>

          <!-- File Added UI -->
          <div
            id="resumeFileAddedUI"
            class="hidden w-full p-6 border-2 border-gray-300 dark:border-gray-700/70 rounded-xl bg-white/10 dark:bg-gray-800/20 min-h-[100px]"
          >
            <div class="flex items-center justify-center mb-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-10 w-10 text-green-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <p
              id="resumeFileName"
              class="text-center text-lg text-gray-900 dark:text-white font-medium"
            >
              Resume added successfully
            </p>
            <button
              id="changeResumeSource"
              class="mt-4 px-4 py-2 bg-black text-white dark:bg-white dark:text-black rounded-full text-sm hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200"
            >
              Upload Another
            </button>
          </div>

          <!-- Manual Entry UI -->
          <div id="manualEntryContainer" class="hidden w-full">
            <textarea
              id="resumeContent"
              name="resumeContent"
              rows="10"
              placeholder="Paste or type your resume here..."
              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-700/70 bg-white dark:bg-gray-800/30 rounded-xl
                                dark:text-gray-100 focus:border-primary dark:focus:border-primary focus:ring-1 focus:ring-primary
                                outline-none text-md transition-colors duration-200 resize-y"
            ></textarea>
            <div class="flex justify-end mt-2">
              <button
                id="cancelManualEntry"
                class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              >
                Back to options
              </button>
            </div>
          </div>

          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            This helps us generate more relevant interview questions specific to
            your background.
          </p>

          <!-- Hidden Progressive Loading Toggle (Always enabled) -->
          <div class="hidden">
            <input type="checkbox" id="progressiveLoading" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" checked>
          </div>

          <div
            class="flex flex-col sm:flex-row justify-center items-center space-y-2.5 sm:space-y-0 sm:space-x-4 pt-4"
          >
            <button
              type="submit"
              id="optimizeProfileBtn"
              class="w-full sm:w-auto inline-flex h-11 sm:h-12 items-center justify-center px-6 sm:px-8 py-2 sm:py-3
                                text-sm sm:text-base font-semibold
                                text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700
                                dark:text-white dark:bg-gradient-to-r dark:from-blue-500 dark:to-indigo-500 dark:hover:from-blue-600 dark:hover:to-indigo-600
                                rounded-full
                                transition-all duration-300 ease-in-out
                                transform hover:scale-[1.03] active:scale-95
                                focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900
                                shadow-lg hover:shadow-xl"
            >
              <ButtonLoader
                id="optimizeButtonLoader"
                isLoading={false}
                loadingText="Optimizing..."
                spinnerPosition="left"
                spinnerSize="md"
                spinnerColor="white"
              >
                <svg
                  class="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                  ><path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg
                >
                Optimize Profile
              </ButtonLoader>
            </button>
            <button
              type="button"
              id="clearForm"
              class="w-full sm:w-auto inline-flex h-11 sm:h-12 items-center justify-center px-6 sm:px-8 py-2 sm:py-3
                                text-sm sm:text-base font-semibold
                                text-gray-700 bg-gray-100 hover:bg-gray-200
                                dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600
                                border border-transparent
                                rounded-full
                                transition-all duration-300 ease-in-out
                                transform hover:scale-[1.03] active:scale-95
                                focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-offset-gray-900
                                shadow-md hover:shadow-lg"
            >
              <svg
                class="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
                ><path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                ></path></svg
              >
              Clear
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>

<!-- Upgrade Prompt Component -->
<UpgradePrompt
  id="linkedinOptimizationUpgradePrompt"
  featureName="LinkedIn profile optimization"
  isOpen={false}
/>

<!-- Error Modal Component -->
<ErrorModal
  id="linkedinErrorModal"
  title="Error"
  message="An error occurred. Please try again."
  isOpen={false}
  zIndex={60}
/>

<!-- Progressive Loading Container -->
<div
  id="progressiveLoadingContainer"
  class="hidden mt-12 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
>
  <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 text-center">
    <h3 class="text-xl font-semibold mb-6 text-gray-900 dark:text-white">Analyzing Your Profile</h3>

    <!-- Spinner -->
    <div class="flex justify-center mb-6">
      <svg class="animate-spin h-10 w-10 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>
  </div>
</div>

<!-- Results Container -->
<div
  id="linkedinResults"
  class="hidden mt-12 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
>
  <div class="grid grid-cols-1 lg:grid-cols-[320px,1fr] gap-8">
    <!-- Left Column (Scores & Completeness) -->
    <div class="lg:col-span-1 space-y-8">
      <!-- Overall Score Card -->
      <div
        class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 text-center my-4"
      >
        <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
          Overall Profile Score
        </h3>
        <div class="relative w-36 h-36 mx-auto mb-4">
          <svg class="w-full h-full" viewBox="0 0 100 100">
            <!-- Background circle -->
            <circle
              class="text-gray-200 dark:text-gray-700 stroke-current"
              stroke-width="10"
              cx="50"
              cy="50"
              r="40"
              fill="transparent"></circle>
            <!-- Progress circle -->
            <circle
              id="overallScoreCircle"
              class="text-primary-500 progress-ring__circle stroke-current"
              stroke-width="10"
              stroke-linecap="round"
              cx="50"
              cy="50"
              r="40"
              fill="transparent"
              stroke-dasharray="251.2"
              stroke-dashoffset="251.2"
              transform="rotate(-90 50 50)"></circle>
            <!-- Score text -->
            <text
              id="overallScoreText"
              x="50"
              y="50"
              font-family="Verdana"
              font-size="20"
              text-anchor="middle"
              alignment-baseline="middle"
              class="fill-current text-gray-900 dark:text-white font-bold"
              >0</text
            >
          </svg>
        </div>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          How well your profile is optimized based on best practices.
        </p>
      </div>

      <!-- Profile Completeness Card -->
      <div
        class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 my-4"
      >
        <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
          Profile Completeness
        </h3>
        <div
          class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-2"
        >
          <div
            id="completenessBar"
            class="bg-green-500 h-2.5 rounded-full"
            style="width: 0%"
          >
          </div>
        </div>
        <p
          id="completenessText"
          class="text-right text-sm font-medium text-gray-700 dark:text-gray-300 mb-4"
        >
          0% Complete
        </p>
        <div class="space-y-3">
          <div>
            <h4
              class="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-1 flex items-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-1.5 text-red-500"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v4a1 1 0 102 0V7zm-1 8a1 1 0 100-2 1 1 0 000 2z"
                  clip-rule="evenodd"></path>
              </svg>
              Missing Sections
            </h4>
            <ul
              id="missingSections"
              class="text-xs text-gray-600 dark:text-gray-400 pl-4 space-y-0.5 text-left"
            >
              <li>No missing sections found.</li>
            </ul>
          </div>
          <div>
            <h4
              class="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-1 flex items-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-1.5 text-yellow-500"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.21 3.03-1.742 3.03H4.42c-1.532 0-2.493-1.697-1.743-3.03l5.58-9.92zM10 13a1 1 0 100-2 1 1 0 000 2zm-1-4a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                  clip-rule="evenodd"></path>
              </svg>
              Incomplete Sections
            </h4>
            <ul
              id="incompleteSections"
              class="text-xs text-gray-600 dark:text-gray-400 pl-4 space-y-0.5 text-left"
            >
              <li>No incomplete sections found.</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- ATS Optimization Card -->
      <div
        class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 my-4"
      >
        <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
          ATS Optimization
        </h3>
        <div class="flex items-center mb-4">
          <div class="relative w-16 h-16 mr-4">
            <svg class="w-full h-full" viewBox="0 0 100 100">
              <circle
                class="text-gray-200 dark:text-gray-700 stroke-current"
                stroke-width="10"
                cx="50"
                cy="50"
                r="40"
                fill="transparent"></circle>
              <circle
                id="atsScoreCircle"
                class="text-amber-500 progress-ring__circle stroke-current"
                stroke-width="10"
                stroke-linecap="round"
                cx="50"
                cy="50"
                r="40"
                fill="transparent"
                stroke-dasharray="251.2"
                stroke-dashoffset="251.2"
                transform="rotate(-90 50 50)"></circle>
              <text
                id="atsScoreText"
                x="50"
                y="50"
                font-family="Verdana"
                font-size="20"
                text-anchor="middle"
                alignment-baseline="middle"
                class="fill-current text-gray-900 dark:text-white font-bold"
                >0</text
              >
            </svg>
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400 flex-1">
            How well your profile might perform in Applicant Tracking Systems.
          </p>
        </div>
        <div>
          <h4
            class="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2 flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1.5 text-blue-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
              <path
                fill-rule="evenodd"
                d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                clip-rule="evenodd"></path>
            </svg>
            Recommendations
          </h4>
          <ul
            id="atsRecommendations"
            class="text-xs text-gray-600 dark:text-gray-400 pl-4 space-y-1 text-left"
          >
            <li>No specific ATS recommendations.</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Right Column (Detailed Analysis) -->
    <div class="w-full">
      <div class="mx-auto w-full space-y-12 py-4">
        <!-- Section-by-Section Analysis Card -->
        <div
          class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6"
        >
          <h3 class="text-xl font-semibold mb-5 text-gray-900 dark:text-white">
            Section Analysis
          </h3>
          <div
            id="sectionScores"
            class="space-y-5 [&_ul]:list-none [&_ul]:pl-0 [&_ul]:text-left"
          >
            <!-- Populated by JS -->
            <p class="text-sm text-gray-500 dark:text-gray-400 text-left pl-4">
              Loading section analysis...
            </p>
          </div>
        </div>
      </div>

      <!-- Keyword Optimization Card -->
      <div
        class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-4"
      >
        <h3
          class="text-xl text-left font-semibold mb-5 text-gray-900 dark:text-white"
        >
          Keyword Optimization
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4
              class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2 text-red-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Missing Keywords
            </h4>
            <div id="missingKeywords" class="flex flex-wrap gap-2">
              <span
                class="px-2.5 py-1 rounded-full bg-gray-100 dark:bg-gray-700 text-xs text-gray-600 dark:text-gray-300"
                >No missing keywords identified.</span
              >
            </div>
          </div>
          <div>
            <h4
              class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2 text-blue-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                ></path>
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              Placement Suggestions
            </h4>
            <div id="keywordSuggestions" class="space-y-3">
              <p class="text-xs text-gray-500 dark:text-gray-400">
                No specific placement suggestions.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Optimized Summary Card -->
      <div
        class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-4"
      >
        <h3
          class="text-xl font-semibold mb-4 text-gray-900 dark:text-white flex items-center justify-between"
        >
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2 text-green-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              stroke-width="2"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Optimized Summary Suggestion
          </div>
          <button
            id="copySummaryBtn"
            class="text-xs text-primary hover:text-primary-dark flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
            </svg>
            Copy Summary
          </button>
        </h3>
        <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4">
          <p
            id="optimizedSummary"
            class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed"
          >
            No optimized summary generated.
          </p>
        </div>
      </div>

      <!-- Headline Suggestions Card -->
      <div
        class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 my-4"
      >
        <h3
          class="text-xl font-semibold mb-4 text-gray-900 dark:text-white flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2 text-purple-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
            ></path>
          </svg>
          Headline Suggestions
        </h3>
        <div id="headlineSuggestions" class="space-y-4">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            No headline suggestions available.
          </p>
        </div>
      </div>

      <!-- Experience Enhancements Card -->
      <div
        class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 my-4"
      >
        <h3
          class="text-xl font-semibold mb-4 text-gray-900 dark:text-white flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2 text-indigo-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
            ></path>
          </svg>
          Experience Enhancements
        </h3>
        <div id="experienceEnhancements" class="space-y-5">
          <p
            class="text-sm text-gray-500 dark:text-gray-400 list-none text-left pl-4"
          >
            No experience enhancement suggestions.
          </p>
        </div>
      </div>

      <!-- Skills Prioritization Card -->
      <div
        class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 my-4"
      >
        <h3
          class="text-xl font-semibold mb-4 text-gray-900 dark:text-white flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2 text-pink-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
          </svg>
          Skills Prioritization & Endorsements
        </h3>
        <div id="skillEndorsements" class="space-y-4">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            No skill suggestions available.
          </p>
        </div>
      </div>

      <!-- Content & Engagement Strategy Card -->
      <div
        class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 my-4"
      >
        <h3
          class="text-xl font-semibold mb-4 text-gray-900 dark:text-white flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2 text-teal-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"
            ></path>
          </svg>
          Content & Engagement Strategy
        </h3>
        <div id="contentStrategy" class="space-y-4 text-left pl-4">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            No content strategy suggestions.
          </p>
        </div>
      </div>

      <!-- Networking Recommendations Card -->
      <div
        class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 my-4"
      >
        <h3
          class="text-xl font-semibold mb-4 text-gray-900 dark:text-white flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2 text-cyan-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
            ></path>
          </svg>
          Networking Recommendations
        </h3>
        <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4">
          <ul
            id="networkingRecommendations"
            class="text-sm text-gray-700 dark:text-gray-300 list-none text-left pl-4 space-y-2"
          >
            <li>No specific networking recommendations.</li>
          </ul>
        </div>
      </div>

      <!-- Competitive Analysis Card -->
      <div
        class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 my-4"
      >
        <h3
          class="text-xl font-semibold mb-4 text-gray-900 dark:text-white flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2 text-lime-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            ></path>
          </svg>
          Competitive Analysis
        </h3>
        <div class="space-y-5">
          <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4">
            <h4
              class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-2 text-left pl-4"
            >
              Industry Standards
            </h4>
            <p
              id="industryStandards"
              class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed text-left pl-4"
            >
              Analysis unavailable.
            </p>
          </div>
          <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4">
            <h4
              class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-2 text-left pl-4"
            >
              Your Differentiators
            </h4>
            <ul
              id="differentiators"
              class="text-sm text-gray-700 dark:text-gray-300 list-none text-left pl-4 space-y-1"
            >
              <li>Analysis unavailable.</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Retry Analysis Button -->
      <div class="text-center my-6">
        <button
          id="retryAnalysisBtn"
          class="px-4 py-2 bg-primary text-white rounded-full text-sm hover:bg-primary-dark transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Retry Analysis
        </button>
      </div>
    </div>
  </div>
</div>
<script>
  import { authService } from "../../lib/auth";
  import { TierManagementService } from "../../lib/tierManagement";
  import { showUpgradePrompt } from "../../lib/upgradePrompt";
  import { setupResumeInput } from "../../lib/resumeInputHandler";
  import { ErrorHandler } from "../../lib/errorHandler";
  import { LoadingManager } from "../../lib/loadingManager";
  import { ComponentInitializer } from "../../lib/componentInitializer";
  import { APIClient } from "../../lib/apiClient";
  import "../../scripts/linkedinOptimizerOptimistic.js";

  // Declare global type for window
  declare global {
    interface Window {
      resumeInputHandler: any;
    }
  }

  // --- Global State ---
  let linkedinContent = ""; // Store extracted LinkedIn text
  let currentUserId: string | null = null; // Store user ID after initial check
  let currentIdToken: string | null = null; // Store ID token
  let isOptimizing: boolean = false;
  let progressiveLoadingEnabled: boolean = true;

  // --- Helper Functions ---

  /**
   * Displays a modal prompting the user to upgrade their plan.
   */
  function showUpgradeModal() {
    // Use the standardized upgrade prompt component
    showUpgradePrompt("linkedinOptimizationUpgradePrompt");
  }

  /**
   * Displays a generic error modal.
   */
  function showErrorModal(title, message) {
    // Use the standardized error modal component
    if (window.errorModalFunctions && window.errorModalFunctions["linkedinErrorModal"]) {
      // Update the title and message if provided
      const errorModal = document.getElementById("linkedinErrorModal");
      const titleElement = errorModal?.querySelector("h2");
      const messageElement = document.getElementById("linkedinErrorModal-message");

      if (titleElement && title) {
        titleElement.textContent = title;
      }

      if (messageElement && message) {
        messageElement.textContent = message;
      }

      // Show the modal
      window.errorModalFunctions["linkedinErrorModal"].show();
    } else {
      // Fallback to using the errorHandling utility
      import("../../lib/errorHandling").then(({ showErrorModal }) => {
        showErrorModal("linkedinErrorModal", title, message);
      }).catch(err => {
        console.error("Failed to import error handling utility:", err);
        alert(`${title}: ${message}`);
      });
    }
  }

  /**
   * Checks feature access for LinkedIn Optimization using ComponentInitializer.
   * Shows upgrade prompt if access denied.
   * @returns {Promise<{canAccess: boolean, userId: string | null, usageInfo?: {current: number, limit: number}, usageLimitReached?: boolean}>} Access status and user ID
   */
  async function checkLinkedInAccess() {
    try {
      // Create a ComponentInitializer instance
      const initializer = new ComponentInitializer("linkedinOptimiser");

      // Check feature access
      const access = await initializer.checkFeatureAccess();

      // Disable submit button if usage limit is reached
      if (access.usageLimitReached) {
        const submitButton = document.getElementById("analyzeLinkedInBtn") as HTMLButtonElement;
        if (submitButton) {
          submitButton.disabled = true;
          submitButton.classList.add("opacity-50", "cursor-not-allowed");
          submitButton.title = "Usage limit reached. Please upgrade your plan.";
        }
      }

      return access;
    } catch (error) {
      console.error("Error checking feature access:", error);
      showErrorModal(
        "Access Check Error",
        "Could not verify your feature access. Please try again."
      );
      return { canAccess: false, userId: null };
    }
  }

  /**
   * Tracks feature usage for LinkedIn Optimization using ComponentInitializer.
   * Should be called *after* a successful operation (e.g., upload, submit).
   */
  async function trackLinkedInUsage(userId: string) {
    if (!userId) {
      console.warn("Cannot track usage: User ID not provided.");
      return; // Don't block execution, just log a warning
    }
    try {
      // Create a ComponentInitializer instance
      const initializer = new ComponentInitializer("linkedinOptimiser");

      // Track feature usage
      await initializer.trackFeatureUsage(userId);
    } catch (error) {
      // Log the error but don't interrupt the user experience
      console.warn("Failed to track LinkedIn usage, but content was generated successfully:", error);
    }
  }

  /**
   * Reads a file and returns its base64 representation (without data prefix).
   * @param {File} file - The file to read.
   * @returns {Promise<string>} Base64 encoded string.
   */
  function readFileAsBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64File = reader.result as string;
        resolve(base64File.split(",")[1]); // Remove data:mime/type;base64, prefix
      };
      reader.onerror = (error) => reject(error);
      reader.readAsDataURL(file);
    });
  }

  /**
   * Uploads a file (Resume or LinkedIn) to the server endpoint.
   * @param {File} file - The file object.
   * @returns {Promise<object>} The JSON result from the server { success: boolean, data?: { text: string }, error?: string }.
   */
  async function uploadFileToServer(file) {
    if (!currentIdToken) {
      throw new Error("Authentication token not available.");
    }
    try {
      const base64Data = await readFileAsBase64(file);
      const response = await fetch("/.netlify/functions/upload-resume", {
        // Using the same endpoint
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${currentIdToken}`,
        },
        body: JSON.stringify({
          fileBase64: base64Data,
          fileName: file.name,
          fileType: file.type,
        }),
      });

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || `Server error: ${response.status}`);
      }
      return result;
    } catch (error) {
      console.error("File upload error:", error);
      // Rethrow a more user-friendly error message
      throw new Error(
        `Failed to upload ${file.name}. ${error instanceof Error ? error.message : "Unknown upload error"}`
      );
    }
  }

  // --- UI Update Functions (Results Display) ---

  // Helper function to get color class based on score
  const getScoreColorClass = (score) => {
    score = score ?? 0; // Default to 0 if null/undefined
    if (score >= 90) return "text-green-500";
    if (score >= 70) return "text-blue-500";
    if (score >= 50) return "text-amber-500";
    return "text-red-500";
  };

  // Helper function for progress bar background color
  const getProgressBarColorClass = (score) => {
    score = score ?? 0;
    if (score >= 90) return "bg-green-500";
    if (score >= 70) return "bg-blue-500";
    if (score >= 50) return "bg-amber-500";
    return "bg-red-500";
  };

  // Helper function to get priority badge HTML
  const getPriorityBadge = (priority) => {
    const badgeClasses = {
      high: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      medium: "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300",
      low: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
    };

    const priorityLabels = {
      high: "High Priority",
      medium: "Medium Priority",
      low: "Low Priority"
    };

    const badgeClass = badgeClasses[priority] || "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
    const label = priorityLabels[priority] || "Unknown Priority";

    return `<span class="ml-2 text-xs px-2 py-0.5 rounded-full ${badgeClass}">${label}</span>`;
  };

  // Helper function to copy text to clipboard
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (err) {
      console.error('Failed to copy text: ', err);
      return false;
    }
  };

  // Helper function to format time in ms to readable format
  const formatProcessingTime = (ms) => {
    if (ms < 1000) return `${ms}ms`;
    const seconds = Math.floor(ms / 1000);
    const remainingMs = ms % 1000;
    return `${seconds}.${remainingMs.toString().padStart(3, '0')}s`;
  };

  // Function to update radial progress circle
  const setProgress = (circleElement, textElement, score) => {
    if (!circleElement || !textElement) return;
    score = Math.max(0, Math.min(100, score ?? 0)); // Clamp score 0-100, default 0
    const radius = circleElement.r.baseVal.value;
    const circumference = 2 * Math.PI * radius;
    const offset = circumference - (score / 100) * circumference;

    // Ensure initial styles for transition are set if not already via CSS
    circleElement.style.strokeDasharray = `${circumference} ${circumference}`;
    circleElement.style.strokeDashoffset = circumference; // Start empty

    // Apply the target offset and color
    circleElement.style.strokeDashoffset = offset;
    circleElement.classList.remove(
      "text-green-500",
      "text-blue-500",
      "text-amber-500",
      "text-red-500"
    );
    circleElement.classList.add(getScoreColorClass(score));
    textElement.textContent = score;
  };

  // Helper to update list elements safely
  const updateList = (listElement, items, placeholder) => {
    if (listElement) {
      listElement.innerHTML = ""; // Clear previous items
      if (items && Array.isArray(items) && items.length > 0) {
        items.forEach((item) => {
          const li = document.createElement("li");
          li.textContent = item;
          listElement.appendChild(li);
        });
      } else {
        const li = document.createElement("li");
        li.textContent = placeholder;
        li.classList.add("italic", "text-gray-500", "dark:text-gray-400"); // Style placeholder
        listElement.appendChild(li);
      }
    }
  };

  // Function to update progress step in the progressive loading UI
  const updateProgressStep = (step) => {
    // Import the loadingState utility
    import("../../lib/loadingState").then(({ updateProgress }) => {
      // Update progress bar using the utility
      updateProgress("analysisProgressBar", (step - 1) * 33.33);

      // Update circles
      for (let i = 1; i <= 4; i++) {
        const circle = document.getElementById(`step${i}Circle`);
        if (circle) {
          if (i < step) {
            // Completed step
            circle.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-500', 'dark:text-gray-400');
            circle.classList.add('bg-primary', 'text-white');
            circle.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>`;
          } else if (i === step) {
            // Current step
            circle.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-500', 'dark:text-gray-400');
            circle.classList.add('bg-primary', 'text-white');
            circle.innerHTML = `<span class="text-sm font-medium">${i}</span>`;
          } else {
            // Future step
            circle.classList.remove('bg-primary', 'text-white');
            circle.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-500', 'dark:text-gray-400');
            circle.innerHTML = `<span class="text-sm font-medium">${i}</span>`;
          }
        }
      }

      // Update step description
      const stepDescription = document.getElementById('currentStepDescription');
      if (stepDescription) {
        const descriptions = [
          "Processing your LinkedIn profile and resume...",
          "Running basic analysis on your profile...",
          "Generating detailed recommendations...",
          "Analysis complete! Preparing your results..."
        ];
        stepDescription.innerHTML = `<p class="text-gray-700 dark:text-gray-300">${descriptions[step - 1] || "Processing..."}</p>`;
      }

      // Show partial results container for step 2 and beyond
      const partialResultsContainer = document.getElementById('partialResultsContainer');
      if (partialResultsContainer && step >= 2) {
        partialResultsContainer.classList.remove('hidden');
      }
    }).catch(err => {
      console.error("Failed to import loading state utility:", err);

      // Fallback to direct DOM manipulation if import fails
      const progressBar = document.getElementById('analysisProgressBar');
      if (progressBar) {
        progressBar.style.width = `${(step - 1) * 33.33}%`;
      }
    });
  };

  // Function to update partial results in the progressive loading UI
  const updatePartialResults = (data) => {
    if (!data) return;

    // Update score preview
    const previewScoreText = document.getElementById('previewScoreText');
    const previewScoreProgress = document.getElementById('previewScoreProgress');

    if (previewScoreText && previewScoreProgress && data.overallScore !== undefined) {
      const score = data.overallScore;
      previewScoreText.textContent = Math.round(score).toString();

      // Calculate stroke-dashoffset (circle has circumference of 251.2)
      const circumference = 251.2;
      const offset = circumference - (score / 100) * circumference;
      previewScoreProgress.style.strokeDashoffset = offset.toString();
    }

    // Update keywords preview
    const previewKeywords = document.getElementById('previewKeywords');
    if (previewKeywords && data.keywordOptimization?.missingKeywords?.length > 0) {
      previewKeywords.innerHTML = data.keywordOptimization.missingKeywords
        .slice(0, 5)
        .map(keyword => `<span class="inline-block px-2 py-1 rounded-full bg-gray-200 dark:bg-gray-700 text-xs text-gray-600 dark:text-gray-400">${keyword}</span>`)
        .join(' ');
    }
  };

  // Function to setup copy buttons
  const setupCopyButtons = () => {
    // Setup copy buttons for recommendations
    document.querySelectorAll('.copy-recommendations-btn').forEach(button => {
      button.addEventListener('click', async (e) => {
        const section = (e.currentTarget as HTMLElement).getAttribute('data-section');
        const recommendations = document.querySelectorAll(`[data-section="${section}"] li`);

        if (recommendations.length > 0) {
          const text = Array.from(recommendations).map(el => el.textContent).join('\n• ');
          const success = await copyToClipboard(`• ${text}`);

          if (success) {
            // Show success feedback
            const originalText = (e.currentTarget as HTMLElement).innerHTML;
            (e.currentTarget as HTMLElement).innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              Copied!
            `;

            // Reset after 2 seconds
            setTimeout(() => {
              (e.currentTarget as HTMLElement).innerHTML = originalText;
            }, 2000);
          }
        }
      });
    });

    // Setup copy button for optimized summary
    const copySummaryBtn = document.getElementById('copySummaryBtn');
    copySummaryBtn?.addEventListener('click', async () => {
      const summaryText = document.getElementById('optimizedSummary')?.textContent;
      if (summaryText) {
        const success = await copyToClipboard(summaryText);

        if (success) {
          // Show success feedback
          const originalText = copySummaryBtn.innerHTML;
          copySummaryBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            Copied!
          `;

          // Reset after 2 seconds
          setTimeout(() => {
            copySummaryBtn.innerHTML = originalText;
          }, 2000);
        }
      }
    });
  };

  // Function to setup retry button
  const setupRetryButton = () => {
    const retryBtn = document.getElementById('retryAnalysisBtn');
    retryBtn?.addEventListener('click', () => {
      // Scroll back to form
      document.getElementById('linkedinFormContainer')?.scrollIntoView({ behavior: 'smooth' });

      // Hide results
      document.getElementById('linkedinResults')?.classList.add('hidden');
    });
  };

  // Function to update results with API data
  const updateResults = (data) => {
    if (!data) {
      console.warn("No data provided to updateResults");
      showErrorModal(
        "Update Error",
        "Received invalid data format for results."
      );
      return;
    }

    // --- Define Defaults ---
    const defaults = {
      overallScore: 0,
      sectionScores: [],
      profileCompleteness: {
        score: 0,
        missingSections: [],
        incompleteSection: [],
      },
      atsOptimization: { score: 0, recommendations: [] },
      keywordOptimization: { missingKeywords: [], keywordSuggestions: [] },
      optimizedSummary: "",
      headlineSuggestions: [],
      experienceEnhancements: [],
      skillEndorsements: [],
      contentStrategy: [],
      networkingRecommendations: [],
      competitiveAnalysis: { industryStandards: "", differentiators: [] },
    };

    // Merge data with defaults to ensure properties exist
    const results = { ...defaults, ...data };
    // Deep merge for nested objects if necessary (simple example below)
    results.profileCompleteness = {
      ...defaults.profileCompleteness,
      ...(data.profileCompleteness || {}),
    };
    results.atsOptimization = {
      ...defaults.atsOptimization,
      ...(data.atsOptimization || {}),
    };
    results.keywordOptimization = {
      ...defaults.keywordOptimization,
      ...(data.keywordOptimization || {}),
    };
    results.competitiveAnalysis = {
      ...defaults.competitiveAnalysis,
      ...(data.competitiveAnalysis || {}),
    };

    // --- Query Result Elements ---
    const overallScoreCircle = document.getElementById(
      "overallScoreCircle"
    ) as SVGCircleElement | null;
    const overallScoreText = document.getElementById("overallScoreText");
    const sectionScoresContainer = document.getElementById("sectionScores");
    const completenessBar = document.getElementById("completenessBar");
    const completenessText = document.getElementById("completenessText");
    const missingSectionsList = document.getElementById("missingSections");
    const incompleteSectionsList =
      document.getElementById("incompleteSections");
    const atsScoreCircle = document.getElementById(
      "atsScoreCircle"
    ) as SVGCircleElement | null;
    const atsScoreText = document.getElementById("atsScoreText");
    const atsRecommendationsList =
      document.getElementById("atsRecommendations");
    const missingKeywordsContainer = document.getElementById("missingKeywords");
    const keywordSuggestionsContainer =
      document.getElementById("keywordSuggestions");
    const optimizedSummary = document.getElementById("optimizedSummary");
    const headlineContainer = document.getElementById("headlineSuggestions");
    const experienceContainer = document.getElementById(
      "experienceEnhancements"
    );
    const skillsContainer = document.getElementById("skillEndorsements");
    const strategyContainer = document.getElementById("contentStrategy");
    const networkingContainer = document.getElementById(
      "networkingRecommendations"
    );
    const industryStandards = document.getElementById("industryStandards");
    const differentiatorsList = document.getElementById("differentiators");
    const resultsContainer = document.getElementById("linkedinResults");

    // --- Update UI Elements ---

    // Overall Score
    setProgress(overallScoreCircle, overallScoreText, results.overallScore);

    // Section Scores
    if (sectionScoresContainer) {
      sectionScoresContainer.innerHTML = ""; // Clear previous
      if (results.sectionScores.length > 0) {
        results.sectionScores.forEach((section) => {
          const score = section.score ?? 0;
          const colorClass = getProgressBarColorClass(score);
          const scoreColorClass = getScoreColorClass(score);
          const recommendationsHTML =
            Array.isArray(section.recommendations) &&
            section.recommendations.length > 0
              ? `<ul class="mt-2 text-xs text-gray-600 dark:text-gray-400 list-disc list-inside pl-4 space-y-0.5">
                       ${section.recommendations.map((rec) => `<li>${rec || "N/A"}</li>`).join("")}
                     </ul>`
              : '<p class="mt-1 text-xs text-gray-500 dark:text-gray-400 italic">No specific recommendations.</p>';

          // Determine priority badge color and text
          const priorityBadge = section.priority ? getPriorityBadge(section.priority) : '';

          const sectionDiv = document.createElement("div");
          sectionDiv.className =
            "border-b border-gray-200 dark:border-gray-700 pb-4 last:border-0 last:pb-0";
          sectionDiv.innerHTML = `
                      <div class="flex justify-between items-center mb-1">
                          <div class="flex items-center">
                              <h4 class="text-md font-semibold text-gray-900 dark:text-white">${section.section || "Unnamed Section"}</h4>
                              ${priorityBadge}
                          </div>
                          <span class="text-sm font-medium ${scoreColorClass}">${score}/100</span>
                      </div>
                      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                          <div class="${colorClass} h-1.5 rounded-full transition-all duration-500 ease-out" style="width: ${score}%"></div>
                      </div>
                      ${recommendationsHTML}
                      <div class="mt-2 flex justify-end">
                          <button class="copy-recommendations-btn text-xs text-primary hover:text-primary-dark flex items-center" data-section="${section.section}">
                              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                              </svg>
                              Copy Recommendations
                          </button>
                      </div>
                  `;
          sectionScoresContainer.appendChild(sectionDiv);
        });
      } else {
        sectionScoresContainer.innerHTML =
          '<p class="text-sm text-gray-500 dark:text-gray-400">Section analysis data not available.</p>';
      }
    }

    // Profile Completeness
    if (completenessBar && completenessText) {
      const score = results.profileCompleteness.score ?? 0;
      completenessBar.style.width = `${score}%`;
      // Reset classes then add the correct one
      completenessBar.className = `h-full rounded-full transition-all duration-500 ease-out ${getProgressBarColorClass(score)}`;
      completenessText.textContent = `${score}% Complete`;
    }
    updateList(
      missingSectionsList,
      results.profileCompleteness.missingSections,
      "No missing sections found."
    );
    updateList(
      incompleteSectionsList,
      results.profileCompleteness.incompleteSection,
      "No incomplete sections found."
    );

    // ATS Optimization
    setProgress(atsScoreCircle, atsScoreText, results.atsOptimization.score);
    updateList(
      atsRecommendationsList,
      results.atsOptimization.recommendations,
      "No specific ATS recommendations."
    );

    // Keyword Optimization
    if (missingKeywordsContainer) {
      missingKeywordsContainer.innerHTML = ""; // Clear previous
      const keywords = results.keywordOptimization.missingKeywords;
      if (keywords && Array.isArray(keywords) && keywords.length > 0) {
        keywords.forEach((keyword) => {
          const span = document.createElement("span");
          span.className =
            "inline-block px-2.5 py-1 rounded-full bg-red-100 dark:bg-red-900/50 text-xs text-red-700 dark:text-red-300 font-medium mr-1 mb-1";
          span.textContent = keyword;
          missingKeywordsContainer.appendChild(span);
        });
      } else {
        missingKeywordsContainer.innerHTML = `<span class="px-2.5 py-1 rounded-full bg-gray-100 dark:bg-gray-700 text-xs text-gray-600 dark:text-gray-300">No missing keywords identified.</span>`;
      }
    }

    if (keywordSuggestionsContainer) {
      keywordSuggestionsContainer.innerHTML = ""; // Clear previous
      const suggestions = results.keywordOptimization.keywordSuggestions;
      if (suggestions && Array.isArray(suggestions) && suggestions.length > 0) {
        suggestions.forEach((suggestion) => {
          const div = document.createElement("div");
          div.className =
            "p-2.5 rounded-lg bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 mb-2";
          div.innerHTML = `
                      <p class="text-xs font-semibold text-blue-800 dark:text-blue-200">${suggestion.keyword || "N/A"}</p>
                      <p class="text-xs text-blue-600 dark:text-blue-300">${suggestion.placement || "N/A"}</p>
                  `;
          keywordSuggestionsContainer.appendChild(div);
        });
      } else {
        keywordSuggestionsContainer.innerHTML = `<p class="text-xs text-gray-500 dark:text-gray-400 italic">No placement suggestions.</p>`;
      }
    }

    // Optimized Summary
    if (optimizedSummary) {
      optimizedSummary.textContent =
        results.optimizedSummary || "No optimized summary generated.";
      optimizedSummary.parentElement?.classList.toggle(
        "opacity-50",
        !results.optimizedSummary
      );
    }

    // Headline Suggestions
    if (headlineContainer) {
      headlineContainer.innerHTML = ""; // Clear previous
      const suggestions = results.headlineSuggestions;
      if (suggestions && Array.isArray(suggestions) && suggestions.length > 0) {
        suggestions.forEach((headline) => {
          const div = document.createElement("div");
          div.className =
            "headline-suggestion p-3 rounded-lg bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-800 mb-2";
          div.innerHTML = `
                    <div class="flex justify-between items-start mb-1">
                      <p class="headline-text text-sm font-medium text-purple-800 dark:text-purple-200 text-left pl-4">${headline.headline || "N/A"}</p>
                      <button class="copy-headline-btn text-xs text-primary hover:text-primary-dark flex items-center p-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                        </svg>
                      </button>
                    </div>
                    <p class="text-xs text-purple-600 dark:text-purple-300 text-left pl-4">${headline.explanation || "N/A"}</p>
                  `;
          headlineContainer.appendChild(div);
        });
      } else {
        headlineContainer.innerHTML = `<p class="text-sm text-gray-500 dark:text-gray-400 italic">No headline suggestions available.</p>`;
      }
    }

    // Experience Enhancements
    if (experienceContainer) {
      experienceContainer.innerHTML = ""; // Clear previous
      const enhancements = results.experienceEnhancements;

      if (
        enhancements &&
        Array.isArray(enhancements) &&
        enhancements.length > 0
      ) {
        // Check if we have an array of strings or an array of objects
        const isStringArray = typeof enhancements[0] === 'string';

        if (isStringArray) {
          // Handle array of strings (simple list of recommendations)
          const div = document.createElement("div");
          div.className = "p-3 rounded-lg bg-indigo-50 dark:bg-indigo-900/30 border border-indigo-200 dark:border-indigo-800";

          const listHTML = `
            <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">General Experience Recommendations</h4>
            <ul class="text-sm text-gray-700 dark:text-gray-300 list-none pl-4 space-y-2 text-left">
              ${enhancements.map(item => `
                <li class="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>${item}</span>
                </li>
              `).join('')}
            </ul>
          `;

          div.innerHTML = listHTML;
          experienceContainer.appendChild(div);
        } else {
          // Handle array of objects (company-specific recommendations)
          enhancements.forEach((exp) => {
            const recommendationsHTML =
              Array.isArray(exp.recommendations) && exp.recommendations.length > 0
                ? `<ul class="text-xs text-gray-600 dark:text-gray-400 list-none pl-4 space-y-1 text-left">
                           ${exp.recommendations.map((rec) => `<li>${rec || "N/A"}</li>`).join("")}
                         </ul>`
                : '<p class="text-xs text-gray-500 dark:text-gray-400 italic">No specific recommendations.</p>';

            const div = document.createElement("div");
            div.className =
              "border-b border-gray-200 dark:border-gray-700 pb-3 last:border-0 last:pb-0 mb-3";
            div.innerHTML = `
                        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-1.5">${exp.company || "N/A"}</h4>
                        ${recommendationsHTML}
                    `;
            experienceContainer.appendChild(div);
          });
        }
      } else {
        experienceContainer.innerHTML = `<p class="text-sm text-gray-500 dark:text-gray-400 italic">No experience enhancement suggestions.</p>`;
      }
    }

    // Skills Prioritization (Endorsements)
    if (skillsContainer) {
      skillsContainer.innerHTML = ""; // Clear previous
      const endorsements = results.skillEndorsements;
      if (
        endorsements &&
        Array.isArray(endorsements) &&
        endorsements.length > 0
      ) {
        endorsements.forEach((category) => {
          const skillsHTML =
            Array.isArray(category.skills) && category.skills.length > 0
              ? category.skills
                  .map(
                    (skill) =>
                      `<span class="inline-block px-2.5 py-1 rounded-full bg-pink-100 dark:bg-pink-900/50 text-xs text-pink-700 dark:text-pink-300 font-medium mr-1 mb-1">${skill}</span>`
                  )
                  .join("")
              : '<span class="text-xs text-gray-500 dark:text-gray-400 italic">No skills listed.</span>';

          const div = document.createElement("div");
          div.className = "mb-3";
          div.innerHTML = `
                      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-2">${category.category || "N/A"}</h4>
                      <div class="flex flex-wrap gap-1">
                          ${skillsHTML}
                      </div>
                  `;
          skillsContainer.appendChild(div);
        });
      } else {
        skillsContainer.innerHTML = `<p class="text-sm text-gray-500 dark:text-gray-400 italic">No skill suggestions available.</p>`;
      }
    }

    // Content Strategy
    if (strategyContainer) {
      strategyContainer.innerHTML = ""; // Clear previous
      const strategies = results.contentStrategy;
      if (strategies && Array.isArray(strategies) && strategies.length > 0) {
        strategies.forEach((strategy) => {
          const exampleHTML = strategy.example
            ? `<div class="mt-1 text-xs italic text-teal-500 dark:text-teal-400 bg-teal-100 dark:bg-teal-800/50 p-2 rounded"> Example: ${strategy.example}</div>`
            : "";
          const div = document.createElement("div");
          div.className =
            "p-3 rounded-lg bg-teal-50 dark:bg-teal-900/30 border border-teal-200 dark:border-teal-800 mb-2";
          div.innerHTML = `
                      <h4 class="text-sm font-semibold text-teal-800 dark:text-teal-200 mb-1">${strategy.contentType || "N/A"}</h4>
                      <p class="text-xs text-teal-600 dark:text-teal-300 mb-1">${strategy.description || "N/A"}</p>
                      ${exampleHTML}
                  `;
          strategyContainer.appendChild(div);
        });
      } else {
        strategyContainer.innerHTML = `<p class="text-sm text-gray-500 dark:text-gray-400 italic">No content strategy suggestions.</p>`;
      }
    }

    // Networking Recommendations
    updateList(
      networkingContainer,
      results.networkingRecommendations,
      "No specific networking recommendations."
    );

    // Competitive Analysis
    if (industryStandards) {
      industryStandards.textContent =
        results.competitiveAnalysis.industryStandards ||
        "Analysis unavailable.";
      industryStandards.parentElement?.classList.toggle(
        "opacity-50",
        !results.competitiveAnalysis.industryStandards
      );
    }
    updateList(
      differentiatorsList,
      results.competitiveAnalysis.differentiators,
      "Analysis unavailable."
    );

    // Analysis metadata is no longer displayed

    // --- Show Results & Scroll ---
    if (resultsContainer) {
      resultsContainer.classList.remove("hidden");
      resultsContainer.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  // --- Initialization and Event Listener Setup ---

  document.addEventListener("DOMContentLoaded", async () => {
    // --- Cache DOM Elements ---
    // General / Form Controls
    const clearButton = document.getElementById("clearForm");
    const resumeOptionsSelector = document.getElementById(
      "resumeOptionsSelector"
    );
    const manualEntryContainer = document.getElementById(
      "manualEntryContainer"
    );
    const enterManuallyButton = document.getElementById("enterManuallyButton");
    const cancelManualEntry = document.getElementById("cancelManualEntry");
    const resumeContentTextarea = document.getElementById(
      "resumeContent"
    ) as HTMLTextAreaElement | null;

    // Resume Upload/Import
    const resumeFileUploadButton = document.getElementById(
      "resumeFileUploadButton"
    );
    const resumeFileInput = document.getElementById(
      "resumeFileInput"
    ) as HTMLInputElement | null;
    const resumeFileAddedUI = document.getElementById("resumeFileAddedUI");
    const resumeFileNameElement = document.getElementById("resumeFileName");
    const changeResumeSourceButton =
      document.getElementById("changeResumeSource");
    const importResumeButton = document.getElementById("importResumeButton");

    // LinkedIn Upload
    const linkedinFileUploadButton = document.getElementById(
      "linkedinFileUploadButton"
    );
    const linkedinFileInput = document.getElementById(
      "linkedin-upload"
    ) as HTMLInputElement | null;
    const linkedinOptionsSelector = document.getElementById(
      "linkedinOptionsSelector"
    );
    const linkedinFileAddedUI = document.getElementById("linkedinFileAddedUI");
    const linkedinFileNameElement = document.getElementById("linkedinFileName");
    const changeLinkedinSourceButton = document.getElementById(
      "changeLinkedinSource"
    );

    // Optimization Form & Results
    const linkedinOptimiserForm = document.getElementById(
      "linkedinOptimiserForm"
    ) as HTMLFormElement | null; // Use the form itself

    // --- Initial Authentication and Access Check ---
    async function setupAuthAndInitialChecks() {
      try {
        const currentUser = await authService.getCurrentUser();
        if (currentUser?.uid) {
          currentUserId = currentUser.uid;
          currentIdToken = await currentUser.getIdToken();
          await checkLinkedInAccess();
        } else {
          console.log("User not authenticated on page load.");
        }
      } catch (error) {
        console.error("Error during initial authentication check:", error);
        showErrorModal(
          "Authentication Error",
          "Could not verify your login status. Please try refreshing the page."
        );
      }
    }

    // --- Setup Form Control Event Listeners ---
    function setupFormControls() {
      // Clear form button
      clearButton?.addEventListener("click", (e) => {
        e.preventDefault();
        window.location.reload(); // Simple reload to reset everything
      });

      // Change LinkedIn source button (from file added UI)
      changeLinkedinSourceButton?.addEventListener("click", () => {
        linkedinOptionsSelector?.classList.remove("hidden");
        linkedinFileAddedUI?.classList.add("hidden");
        if (linkedinFileInput) linkedinFileInput.value = ""; // Clear file input
        linkedinContent = ""; // Clear stored LinkedIn content
      });

      // Clear resume content textarea on page load
      if (resumeContentTextarea) {
        resumeContentTextarea.value = "";
      }
    }

    // --- Setup LinkedIn File Upload ---
    function setupLinkedInUpload() {
      const supportedTypes = ["application/pdf"];
      const linkedinUploadLoading = document.getElementById('linkedinUploadLoading'); // Get loading indicator element

      linkedinFileUploadButton?.addEventListener("click", (e) => {
        e.preventDefault();
        linkedinFileInput?.click();
      });

      linkedinFileInput?.addEventListener("change", async (event) => {
        const file = (event.target as HTMLInputElement).files?.[0];
        if (!file) return;

        // Show loading indicator
        linkedinUploadLoading?.classList.remove('hidden');

        if (!supportedTypes.includes(file.type)) {
          showErrorModal(
            "Unsupported File Type",
            "Please upload your LinkedIn profile as a PDF file."
          );
          linkedinFileInput.value = "";
          return;
        }

        if (!currentUserId || !currentIdToken) {
          showErrorModal(
            "Authentication Error",
            "Please log in to upload your LinkedIn profile."
          );
          linkedinFileInput.value = "";
          return;
        }

        // Check access *before* uploading and processing
        const hasAccess = await checkLinkedInAccess();
        if (!hasAccess) {
          linkedinFileInput.value = ""; // Clear input if access denied
          return; // Stop processing
        }

        try {
          // Show loading indicator
          linkedinUploadLoading?.classList.remove('hidden');

          const result = await uploadFileToServer(file);

          if (result.success && result.data?.text) {
            linkedinContent = result.data.text; // Store the content globally

            // Debug logging to verify content extraction
            console.log(`LinkedIn PDF extracted: ${linkedinContent.length} characters`);
            console.log(`LinkedIn content preview: ${linkedinContent.substring(0, 100)}...`);

            // Update UI
            if (linkedinFileNameElement)
              linkedinFileNameElement.textContent = file.name;
            linkedinFileAddedUI?.classList.remove("hidden");
            linkedinOptionsSelector?.classList.add("hidden");
          } else {
            throw new Error(result.error || "Failed to process LinkedIn PDF.");
          }
        } catch (error) {
          showErrorModal(
            "LinkedIn Upload Error",
            error instanceof Error
              ? error.message
              : "An unknown error occurred during LinkedIn PDF upload."
          );
          linkedinContent = ""; // Clear content on error
          linkedinFileInput.value = ""; // Reset input
        } finally {
          // Hide loading indicator
          linkedinUploadLoading?.classList.add('hidden');
        }
      });
    }

    // --- Setup Optimization Form Submission ---
    function setupOptimizationSubmit() {
      linkedinOptimiserForm?.addEventListener("submit", async (event) => {
        event.preventDefault(); // Prevent default HTML form submission

        // Initialize utility classes
        const componentId = "linkedinOptimiser";
        const errorHandler = new ErrorHandler(componentId);
        const loadingManager = new LoadingManager(componentId);

        if (!currentUserId || !currentIdToken) {
          errorHandler.showError(
            "Authentication Error",
            "Please log in to optimize your profile."
          );
          return;
        }

        if (!linkedinContent) {
          errorHandler.showError(
            "Missing Input",
            "Please upload your LinkedIn profile PDF first."
          );
          return;
        }

        // Get resume content using our utility module
        const resumeContent = window.resumeInputHandler ? window.resumeInputHandler.getResumeContent() : resumeContentTextarea?.value || "";

        if (!resumeContent) {
          errorHandler.showError(
            "Missing Input",
            "Please provide your resume content (upload, import, or enter manually)."
          );
          return;
        }

        // Final access check before hitting the expensive API call
        const initializer = new ComponentInitializer(componentId);
        const access = await initializer.checkFeatureAccess("linkedinOptimiser");
        if (!access.canAccess) {
          return; // Access check will show upgrade modal if needed
        }

        // Set button loading state
        loadingManager.setButtonLoading("optimizeButtonLoader", true, "Optimizing...");

        // Get progressive loading preference
        const progressiveLoading = document.getElementById('progressiveLoading') as HTMLInputElement;
        const useProgressiveLoading = progressiveLoading?.checked !== false;

        // Show progressive loading UI if enabled
        const progressiveLoadingContainer = document.getElementById('progressiveLoadingContainer');
        const resultsContainer = document.getElementById("linkedinResults");

        if (useProgressiveLoading) {
          progressiveLoadingContainer?.classList.remove('hidden');
          progressiveLoadingContainer?.scrollIntoView({ behavior: "smooth" });

          // Reset progress indicators
          updateProgressStep(1);
        }

        try {
          // Always make the initial quick analysis request for progressive loading
          const quickResult = await APIClient.optimizeLinkedIn({
            linkedinContent,
            resumeContent,
            progressive: true
          });

          // Update progress indicators to step 2 (Basic Analysis)
          updateProgressStep(2);

          // Show partial results
          updatePartialResults(quickResult);

          // Update progress indicators to step 3 (Detailed Analysis)
          updateProgressStep(3);

          // Debug logging to verify content being sent to API
          console.log(`Sending to API - LinkedIn content length: ${linkedinContent.length} characters`);
          console.log(`Sending to API - Resume content length: ${resumeContent.length} characters`);

          // Use APIClient for the full analysis request (automatically proceed)
          const result = await APIClient.optimizeLinkedIn({
            linkedinContent,
            resumeContent
          });

          // Update progress indicators to step 4 (Complete)
          updateProgressStep(4);

          // Hide progressive loading UI after a short delay
          setTimeout(() => {
            progressiveLoadingContainer?.classList.add('hidden');
          }, 1000);

          // Update results with the full analysis data
          updateResults(result);

          // Track usage *after* successful optimization call
          if (access.userId) {
            await trackLinkedInUsage(access.userId);
          }

          // Show results container
          resultsContainer?.classList.remove("hidden");

          // Scroll to results
          resultsContainer?.scrollIntoView({ behavior: "smooth", block: "start" });

          // Setup copy buttons after results are displayed
          setupCopyButtons();

          // Setup retry button
          setupRetryButton();

        } catch (error) {
          console.error("Optimization error:", error);

          // Hide progressive loading UI if it was shown
          const progressiveLoadingContainer = document.getElementById('progressiveLoadingContainer');
          if (progressiveLoadingContainer) {
            progressiveLoadingContainer.classList.add('hidden');
          }

          // Use ErrorHandler for consistent error handling
          errorHandler.showError(
            "Optimization Failed",
            error instanceof Error
              ? error.message
              : "An unknown error occurred during optimization."
          );
        } finally {
          // Hide loader and re-enable button regardless of success/failure
          loadingManager.setButtonLoading("optimizeButtonLoader", false);
        }
      });
    }

    // --- Setup Resume File Upload ---
    function setupResumeUpload() {
      // Initialize utility classes
      const componentId = "linkedinOptimiser";
      const errorHandler = new ErrorHandler(componentId);
      const resumeUploadLoading = document.getElementById('resumeUploadLoading'); // Get loading indicator element


      // Initialize the resume input handler with custom error handling
      window.resumeInputHandler = setupResumeInput({
        componentId,
        // Custom error handler that uses our ErrorHandler utility
        onError: (title: string, message: string) => {
          errorHandler.showError(title, message);
        },
        // Callback when resume content changes
        onResumeContentChanged: (content: string) => {
          if (resumeContentTextarea && content !== resumeContentTextarea.value) {
            resumeContentTextarea.value = content;
            resumeContentTextarea.dispatchEvent(new Event("input"));

            // Update UI
            resumeFileAddedUI?.classList.remove("hidden");
            resumeOptionsSelector?.classList.add("hidden");
            manualEntryContainer?.classList.add("hidden");

            if (resumeFileNameElement) {
              resumeFileNameElement.textContent = "Uploaded Resume";
            }
          }
        },
        // Pass the current user ID and token
        userId: currentUserId,
        idToken: currentIdToken
      });

      // Set up the click handler for the upload button
      resumeFileUploadButton?.addEventListener("click", (e) => {
        e.preventDefault();
        if (window.resumeInputHandler) {
          window.resumeInputHandler.triggerFileUpload();
        } else {
          resumeFileInput?.click();
        }
      });
    }

    // --- Setup Resume Import Modal ---
    function setupResumeImport() {
      // No need to add event listeners here
      // The importResumeButton event listener is already set up in the resumeInputHandler.js file
    }

    // --- Add Resume-Specific Form Control Listeners ---
    function setupResumeFormControls() {
      // Enter resume manually button
      enterManuallyButton?.addEventListener("click", () => {
        resumeOptionsSelector?.classList.add("hidden");
        manualEntryContainer?.classList.remove("hidden");
        resumeFileAddedUI?.classList.add("hidden");
        resumeContentTextarea?.focus();
      });

      // Cancel manual entry button
      cancelManualEntry?.addEventListener("click", () => {
        resumeOptionsSelector?.classList.remove("hidden");
        manualEntryContainer?.classList.add("hidden");
        // Optionally clear the manual textarea: resumeContentTextarea.value = "";
      });

      // Change resume source button (from file added UI)
      changeResumeSourceButton?.addEventListener("click", () => {
        if (window.resumeInputHandler) {
          window.resumeInputHandler.clearResumeContent();
        }

        resumeOptionsSelector?.classList.remove("hidden");
        resumeFileAddedUI?.classList.add("hidden");
        manualEntryContainer?.classList.add("hidden");
        if (resumeContentTextarea) resumeContentTextarea.value = ""; // Clear text area
      });
    }

    // --- Run Setup Functions ---
    await setupAuthAndInitialChecks(); // Wait for auth check before setting up handlers that might need user ID/token
    setupFormControls(); // General controls (Clear, LinkedIn Source)
    setupLinkedInUpload();
    setupResumeUpload(); // Add call
    setupResumeImport(); // Add call
    setupResumeFormControls(); // Add call for resume-specific buttons
    setupOptimizationSubmit(); // Ensure this is called last or after dependencies are set up
  }); // End DOMContentLoaded

  // Initially hide the form and show the loader
  document.addEventListener("DOMContentLoaded", () => {
    const formContainer = document.getElementById('linkedinFormContainer');
    const loaderContainer = document.getElementById('progressiveLoadingContainer');
    if (formContainer && loaderContainer) {
      formContainer.classList.add('hidden');
      loaderContainer.classList.remove('hidden');
    }
  });
</script>
