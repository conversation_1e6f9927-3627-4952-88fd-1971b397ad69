---
// No server-side logic needed based on the original code.
// Imports for server-side rendering would go here if necessary.
import ErrorModal from "../ErrorModal.astro";
import LoadingSpinner from "../LoadingSpinner.astro";
import ButtonLoader from "../ButtonLoader.astro";
---

<style>
  /* Styles specific to dynamically generated job cards */
  #jobCardsContainer > div {
    padding: 1.5rem;
    font-size: 1.05rem;
    border-radius: 1.25rem;
    transition: all 0.3s ease;
    /* Note: Specific styling like background, border, etc., are handled by createJobCard function */
  }

  /* Modern scrollbar for containers */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(247, 250, 252, 0.1);
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(203, 213, 225, 0.5);
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.7);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(15, 23, 42, 0.1);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(51, 65, 85, 0.5);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(71, 85, 105, 0.7);
  }

  /* Filter pill animations */
  .filter-pill {
    transition: all 0.2s ease;
  }

  .filter-pill:hover {
    transform: translateY(-2px);
  }

  .filter-pill.active {
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.3);
  }
</style>

<div class="relative mt-16 w-full max-w-4xl mx-auto rounded-2xl">
  <!-- ==========================
       Filter & Add Section (Enhanced)
       ========================== -->
  <div
    class="mb-8 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 bg-white/90 dark:bg-gray-900/80 backdrop-blur-xl border border-gray-100/80 dark:border-gray-800/60 rounded-2xl px-4 py-6 shadow-lg shadow-indigo-500/5 dark:shadow-indigo-500/10"
  >
    <div class="flex items-center">
      <button
        id="addJobBtn"
        class="w-full sm:w-auto inline-flex h-12 sm:h-13 items-center justify-center px-6 sm:px-8 py-2 sm:py-3 text-sm sm:text-base font-semibold text-white bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 dark:from-indigo-400 dark:to-purple-500 dark:hover:from-indigo-500 dark:hover:to-purple-600 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-[1.02] active:scale-95 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 shadow-lg hover:shadow-xl shadow-indigo-500/20 dark:shadow-indigo-500/30"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="w-5 h-5 mr-2"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Add New Application
      </button>
    </div>

    <div class="flex items-center gap-3">
      <div class="relative group">
        <div class="absolute -inset-0.5 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full opacity-30 group-hover:opacity-100 blur transition duration-200"></div>
        <div class="relative">
          <select
            id="statusFilterSelect"
            class="w-full px-4 py-2.5 border border-gray-200 dark:border-gray-700/70 bg-white dark:bg-gray-800/80 rounded-full dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm font-medium transition-colors duration-200 pl-10 pr-10 appearance-none"
          >
            <option value="all">All Applications</option>
            <option value="Applied">Applied</option>
            <option value="Interview">Interview</option>
            <option value="Offer">Offer</option>
            <option value="Rejected">Rejected</option>
          </select>
          <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
            <svg class="h-4 w-4 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- ==========================
       Job Cards Display Area
       ========================== -->
  <div class="mx-auto">
    <div class="space-y-6 custom-scrollbar" id="jobCardsContainer">
      <!-- Job cards will be dynamically inserted here -->
      <!-- Loading state will be shown here initially -->
      <div id="loadingState" class="py-12 flex flex-col items-center justify-center">
        <LoadingSpinner
          size="xl"
          color="primary"
          className="mb-4"
        />
        <p class="mt-4 text-gray-500 dark:text-gray-400 text-sm font-medium">Loading your applications...</p>
      </div>
    </div>
  </div>

  <!-- ==========================
       Modals
       ========================== -->

  <!-- Add Job Form Modal (Hidden by default) -->
  <div
    id="addJobFormModal"
    class="fixed inset-0 z-50 flex items-center justify-center opacity-0 invisible transition-all duration-300 ease-in-out bg-gray-900/60 dark:bg-gray-950/70 backdrop-blur-sm"
  >
    <div
      id="addJobFormContent"
      class="w-full max-w-2xl mx-4 transform scale-95 opacity-0 transition-all duration-300 ease-in-out rounded-3xl shadow-2xl"
    >
      <div
        class="max-h-[90vh] overflow-y-auto custom-scrollbar bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border border-gray-100/80 dark:border-gray-800/60 rounded-3xl p-6 sm:p-8 shadow-2xl relative"
      >
        <div class="absolute top-0 inset-x-0 h-1.5 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-t-3xl"></div>

        <button
          type="button"
          data-close-add-modal
          class="absolute top-4 right-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors bg-gray-100/80 dark:bg-gray-800/80 rounded-full p-1.5 hover:bg-gray-200/80 dark:hover:bg-gray-700/80"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>

        <div class="text-center mb-6 mt-2">
          <div class="inline-flex items-center justify-center h-14 w-14 rounded-full bg-indigo-100 dark:bg-indigo-900/30 mb-4">
            <svg class="h-7 w-7 text-indigo-600 dark:text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
          <h2
            class="text-xl sm:text-2xl font-bold text-center text-gray-900 dark:text-white"
          >
            Add New Job Application
          </h2>
          <p class="text-gray-500 dark:text-gray-400 text-sm mt-1.5">Track your job applications and access smart tools</p>
        </div>

        <form
          id="jobForm"
          class="w-full space-y-5 bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-5 sm:p-6 shadow-lg border-gray-100/80 border dark:border-gray-700/40 transition-all duration-300 ease-in-out"
        >
          <!-- Form Fields -->
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-5">
            <div class="space-y-2">
              <label
                for="company"
                class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                >Company <span class="text-red-500">*</span></label
              >
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <input
                  type="text"
                  id="company"
                  name="company"
                  required
                  class="w-full pl-10 pr-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 rounded-xl dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm transition-colors duration-200 shadow-sm"
                  placeholder="e.g., Newton School, Google"
                />
              </div>
            </div>
            <div class="space-y-2">
              <label
                for="position"
                class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                >Job Position <span class="text-red-500">*</span></label
              >
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <input
                  type="text"
                  id="position"
                  name="position"
                  required
                  class="w-full pl-10 pr-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 rounded-xl dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm transition-colors duration-200 shadow-sm"
                  placeholder="e.g., Software Engineer"
                />
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 sm:grid-cols-2 gap-5">
            <div class="space-y-2">
              <label
                for="status"
                class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                >Application Status <span class="text-red-500">*</span></label
              >
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <select
                  id="status"
                  name="status"
                  required
                  class="w-full pl-10 pr-10 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 rounded-xl dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm transition-colors duration-200 shadow-sm appearance-none"
                >
                  <option value="Applied">Applied</option>
                  <option value="Interview">Interview</option>
                  <option value="Offer">Offer</option>
                  <option value="Rejected">Rejected</option>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3">
                  <svg class="h-4 w-4 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>
            <div class="space-y-2">
              <label
                for="dateApplied"
                class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                >Date Applied <span class="text-red-500">*</span></label
              >
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <input
                  type="date"
                  id="dateApplied"
                  name="dateApplied"
                  required
                  class="w-full pl-10 pr-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 rounded-xl dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm transition-colors duration-200 shadow-sm appearance-none"
                  value={new Date().toISOString().split("T")[0]}
                />
              </div>
            </div>
          </div>

          <div class="space-y-2">
            <label
              for="url"
              class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
              >Job Posting URL <span class="text-red-500">*</span></label
            >
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <input
                type="url"
                id="url"
                name="url"
                required
                class="w-full pl-10 pr-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 rounded-xl dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm transition-colors duration-200 shadow-sm"
                placeholder="https://..."
              />
            </div>
          </div>

          <div class="space-y-2">
            <label
              for="jobDescription"
              class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
              >Job Description <span class="text-red-500">*</span></label
            >
            <div class="relative">
              <textarea
                id="jobDescription"
                name="jobDescription"
                required
                rows="4"
                class="w-full px-4 py-3 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 rounded-xl dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm transition-colors duration-200 shadow-sm resize-y"
                placeholder="Paste the full job description here"></textarea>
            </div>
            <p
              class="text-xs text-gray-500 dark:text-gray-400 text-left pt-1 flex items-center"
            >
              <svg class="h-3.5 w-3.5 mr-1 text-indigo-500 dark:text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Tip: Include role, company, qualifications for better analysis.
            </p>
          </div>

          <div class="space-y-2">
            <label
              for="notes"
              class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
              >Additional Notes</label
            >
            <div class="relative">
              <textarea
                id="notes"
                name="notes"
                rows="2"
                class="w-full px-4 py-3 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 rounded-xl dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm transition-colors duration-200 shadow-sm resize-y"
                placeholder="Contacts, follow-up dates, etc."></textarea>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="pt-4">
            <button
              type="submit"
              class="w-full inline-flex h-12 items-center justify-center px-6 py-3 text-base font-semibold text-white bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 dark:from-indigo-400 dark:to-purple-500 dark:hover:from-indigo-500 dark:hover:to-purple-600 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-[1.02] active:scale-95 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 shadow-lg hover:shadow-xl shadow-indigo-500/20 dark:shadow-indigo-500/30"
            >
              <ButtonLoader
                id="addButtonLoader"
                isLoading={false}
                loadingText="Adding..."
                spinnerPosition="left"
                spinnerSize="md"
                spinnerColor="white"
              >
                <svg
                  class="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                  ><path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg
                >
                Add Application
              </ButtonLoader>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Edit Job Form Modal (Hidden by default) -->
  <div
    id="editModal"
    class="fixed inset-0 z-50 flex items-center justify-center opacity-0 invisible transition-all duration-300 ease-in-out bg-gray-900/60 dark:bg-gray-950/70 backdrop-blur-sm"
  >
    <div
      id="editModalContent"
      class="w-full max-w-2xl mx-4 transform scale-95 opacity-0 transition-all duration-300 ease-in-out rounded-3xl shadow-2xl"
    >
      <div
        class="max-h-[90vh] overflow-y-auto custom-scrollbar bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border border-gray-100/80 dark:border-gray-800/60 rounded-3xl p-6 sm:p-8 shadow-2xl relative"
      >
        <div class="absolute top-0 inset-x-0 h-1.5 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-t-3xl"></div>

        <button
          id="closeEditModalBtn"
          type="button"
          class="absolute top-4 right-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors bg-gray-100/80 dark:bg-gray-800/80 rounded-full p-1.5 hover:bg-gray-200/80 dark:hover:bg-gray-700/80"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>

        <div class="text-center mb-6 mt-2">
          <div class="inline-flex items-center justify-center h-14 w-14 rounded-full bg-indigo-100 dark:bg-indigo-900/30 mb-4">
            <svg class="h-7 w-7 text-indigo-600 dark:text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </div>
          <h2
            class="text-xl sm:text-2xl font-bold text-center text-gray-900 dark:text-white"
          >
            Edit Job Application
          </h2>
          <p class="text-gray-500 dark:text-gray-400 text-sm mt-1.5">Update your application details</p>
        </div>

        <form
          id="editForm"
          class="w-full space-y-5 bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-5 sm:p-6 shadow-lg border-gray-100/80 border dark:border-gray-700/40 transition-all duration-300 ease-in-out"
        >
          <input type="hidden" id="editJobId" name="editJobId" />
          <!-- Form Fields -->
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-5">
            <div class="space-y-2">
              <label
                for="editCompany"
                class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                >Company <span class="text-red-500">*</span></label
              >
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <input
                  type="text"
                  id="editCompany"
                  name="editCompany"
                  required
                  class="w-full pl-10 pr-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 rounded-xl dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm transition-colors duration-200 shadow-sm"
                  placeholder="e.g., Newton School, Google"
                />
              </div>
            </div>
            <div class="space-y-2">
              <label
                for="editPosition"
                class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                >Job Position <span class="text-red-500">*</span></label
              >
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <input
                  type="text"
                  id="editPosition"
                  name="editPosition"
                  required
                  class="w-full pl-10 pr-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 rounded-xl dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm transition-colors duration-200 shadow-sm"
                  placeholder="e.g., Software Engineer"
                />
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 sm:grid-cols-2 gap-5">
            <div class="space-y-2">
              <label
                for="editStatus"
                class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                >Application Status <span class="text-red-500">*</span></label
              >
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <select
                  id="editStatus"
                  name="editStatus"
                  required
                  class="w-full pl-10 pr-10 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 rounded-xl dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm transition-colors duration-200 shadow-sm appearance-none"
                >
                  <option value="Applied">Applied</option>
                  <option value="Interview">Interview</option>
                  <option value="Offer">Offer</option>
                  <option value="Rejected">Rejected</option>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3">
                  <svg class="h-4 w-4 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>
            <div class="space-y-2">
              <label
                for="editDateApplied"
                class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                >Date Applied <span class="text-red-500">*</span></label
              >
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <input
                  type="date"
                  id="editDateApplied"
                  name="editDateApplied"
                  required
                  class="w-full pl-10 pr-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 rounded-xl dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm transition-colors duration-200 shadow-sm appearance-none"
                />
              </div>
            </div>
          </div>

          <div class="space-y-2">
            <label
              for="editUrl"
              class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
              >Job Posting URL <span class="text-red-500">*</span></label
            >
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <input
                type="url"
                id="editUrl"
                name="editUrl"
                required
                class="w-full pl-10 pr-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 rounded-xl dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm transition-colors duration-200 shadow-sm"
                placeholder="https://..."
              />
            </div>
          </div>

          <div class="space-y-2">
            <label
              for="editJobDescription"
              class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
              >Job Description <span class="text-red-500">*</span></label
            >
            <div class="relative">
              <textarea
                id="editJobDescription"
                name="editJobDescription"
                required
                rows="4"
                class="w-full px-4 py-3 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 rounded-xl dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm transition-colors duration-200 shadow-sm resize-y"
                placeholder="Paste the full job description here"></textarea>
            </div>
            <p
              class="text-xs text-gray-500 dark:text-gray-400 text-left pt-1 flex items-center"
            >
              <svg class="h-3.5 w-3.5 mr-1 text-indigo-500 dark:text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Tip: Include role, company, qualifications for better analysis.
            </p>
          </div>

          <div class="space-y-2">
            <label
              for="editNotes"
              class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
              >Additional Notes</label
            >
            <div class="relative">
              <textarea
                id="editNotes"
                name="editNotes"
                rows="2"
                class="w-full px-4 py-3 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 rounded-xl dark:text-gray-100 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-2 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 outline-none text-sm transition-colors duration-200 shadow-sm resize-y"
                placeholder="Contacts, follow-up dates, etc."></textarea>
            </div>
          </div>

          <!-- Save Changes Button -->
          <div class="pt-4">
            <button
              type="submit"
              class="w-full inline-flex h-12 items-center justify-center px-6 py-3 text-base font-semibold text-white bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 dark:from-indigo-400 dark:to-purple-500 dark:hover:from-indigo-500 dark:hover:to-purple-600 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-[1.02] active:scale-95 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 shadow-lg hover:shadow-xl shadow-indigo-500/20 dark:shadow-indigo-500/30"
            >
              <svg
                id="saveIcon"
                class="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
                ><path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                ></path></svg
              >
              <svg
                id="saveSpinner"
                class="animate-spin -ml-1 mr-2 h-5 w-5 text-white hidden"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                ><circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"></circle><path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path></svg
              >
              <span id="saveButtonText">Save Changes</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal (Hidden by default) -->
  <div
    id="deleteModal"
    class="opacity-0 pointer-events-none fixed inset-0 bg-gray-900/60 dark:bg-gray-950/70 backdrop-blur-sm overflow-y-auto h-full w-full z-50 flex items-center justify-center transition-opacity duration-300"
  >
    <div
      class="relative p-8 w-full max-w-sm shadow-2xl rounded-3xl bg-white/95 dark:bg-gray-900/95 border border-gray-100/80 dark:border-gray-800/60 mx-4 transform scale-95 opacity-0 transition-all duration-300 ease-out"
      id="deleteModalContent"
    >
      <div class="text-center">
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/30 mb-5">
          <svg class="h-8 w-8 text-red-600 dark:text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </div>
        <h3
          class="text-xl font-bold text-gray-900 dark:text-white mb-3"
        >
          Delete Application
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Are you sure you want to delete this job application? This action cannot be undone.
        </p>
        <input type="hidden" id="deleteJobId" />
        <div class="flex justify-center gap-3">
          <button
            id="confirmDeleteBtn"
            class="px-5 py-2.5 text-sm font-medium bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 text-white rounded-xl shadow-lg shadow-red-500/20 dark:shadow-red-700/30 hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] active:scale-95 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
            >Delete</button
          >
          <button
            id="cancelDeleteBtn"
            class="px-5 py-2.5 text-sm font-medium bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 shadow-sm hover:shadow transition-all duration-200 transform hover:scale-[1.02] active:scale-95 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
            >Cancel</button
          >
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // ==========================
  // Imports
  // ==========================
  import { getAuth } from "firebase/auth";
  import { TierManagementService } from "../../lib/tierManagement"; // Assuming path is correct
  import { db } from "../../lib/firebase"; // Assuming path is correct
  import {
    collection,
    addDoc,
    getDoc,
    query,
    orderBy,
    onSnapshot,
    deleteDoc,
    doc,
    updateDoc,
    Timestamp,
    type QuerySnapshot, // Explicit type import
  } from "firebase/firestore";

  // ==========================
  // Interfaces & Types
  // ==========================
  interface Job {
    id: string;
    company: string;
    position: string;
    status: string;
    dateApplied: Timestamp; // Use Timestamp directly from Firestore
    url?: string;
    jobDescription?: string;
    notes?: string;
    createdAt: Timestamp;
    updatedAt?: Timestamp;
    userId?: string; // Keep track of user
  }

  // ==========================
  // Constants & DOM Elements
  // ==========================
  const statusFilterSelect = document.getElementById(
    "statusFilterSelect"
  ) as HTMLSelectElement;
  const jobCardsContainer = document.getElementById("jobCardsContainer");
  const addJobBtn = document.getElementById("addJobBtn");

  // Add Modal Elements
  const addJobFormModal = document.getElementById("addJobFormModal");
  const addJobFormContent = document.getElementById("addJobFormContent");
  const addJobForm = document.getElementById(
    "jobForm"
  ) as HTMLFormElement | null;
  const closeAddModalBtn = document.querySelector("[data-close-add-modal]");

  // Edit Modal Elements
  const editModal = document.getElementById("editModal");
  const editModalContent = document.getElementById("editModalContent");
  const editForm = document.getElementById(
    "editForm"
  ) as HTMLFormElement | null;
  const closeEditModalBtn = document.getElementById("closeEditModalBtn");
  const editJobIdInput = document.getElementById(
    "editJobId"
  ) as HTMLInputElement;
  const editCompanyInput = document.getElementById(
    "editCompany"
  ) as HTMLInputElement;
  const editPositionInput = document.getElementById(
    "editPosition"
  ) as HTMLInputElement;
  const editStatusSelect = document.getElementById(
    "editStatus"
  ) as HTMLSelectElement;
  const editDateAppliedInput = document.getElementById(
    "editDateApplied"
  ) as HTMLInputElement;
  const editUrlInput = document.getElementById("editUrl") as HTMLInputElement;
  const editJobDescriptionTextarea = document.getElementById(
    "editJobDescription"
  ) as HTMLTextAreaElement;
  const editNotesTextarea = document.getElementById(
    "editNotes"
  ) as HTMLTextAreaElement;

  // Delete Modal Elements
  const deleteModal = document.getElementById("deleteModal");
  const confirmDeleteBtn = document.getElementById("confirmDeleteBtn");
  const cancelDeleteBtn = document.getElementById("cancelDeleteBtn");
  const deleteJobIdInput = document.getElementById(
    "deleteJobId"
  ) as HTMLInputElement;

  // Firebase Collection Reference
  const jobsCollectionRef = collection(db, "jobTracker");

  // ==========================
  // State
  // ==========================
  let latestSnapshot: QuerySnapshot | null = null;
  const DEBOUNCE_DELAY = 150; // ms

  // Import optimistic UI utilities
  import { performOptimisticRequest } from "../../lib/optimisticUI";
  import "../../scripts/jobTrackerOptimistic.js";

  // ==========================
  // Helper Functions
  // ==========================

  /** Debounces function execution */
  function debounce<F extends (...args: any[]) => any>(
    func: F,
    waitFor: number
  ) {
    let timeout: ReturnType<typeof setTimeout> | null = null;
    return (...args: Parameters<F>): Promise<ReturnType<F>> =>
      new Promise((resolve) => {
        if (timeout) {
          clearTimeout(timeout);
        }
        timeout = setTimeout(() => resolve(func(...args)), waitFor);
      });
  }

  /** Normalizes URL input */
  function normalizeUrl(url: string): string {
    url = url.trim();
    if (!url) return "";
    if (!/^https?:\/\//i.test(url)) {
      url = `https://${url}`;
    }
    try {
      new URL(url); // Validate URL structure
      return url;
    } catch (error) {
      console.warn("Invalid URL provided:", url, error); // Log the error for debugging
      // Throw a more specific error that can be caught by the form handlers
      throw new Error("Invalid URL format. Please enter a valid web address.");
    }
  }

  /** Formats Firestore Timestamp to 'YYYY-MM-DD' for date input */
  function formatDateForInput(timestamp: Timestamp): string {
    return timestamp.toDate().toISOString().split("T")[0];
  }

  /** Calculates time difference for display */
  function getTimeAgo(date: Date): string {
    const now = Date.now();
    const past = date.getTime();
    const diffTime = Math.abs(now - past);
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return "Applied today";
    if (diffDays === 1) return "Applied yesterday";
    if (diffDays < 7) return `Applied ${diffDays}d ago`;
    if (diffDays < 30) return `Applied ${Math.floor(diffDays / 7)}w ago`;
    return `Applied on ${date.toLocaleDateString("en-US", { month: "short", day: "numeric" })}`;
  }

  /** Returns Tailwind classes based on job status */
  function getStatusStyle(status: string): string {
    const styles: { [key: string]: string } = {
      Applied:
        "bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 dark:from-blue-900/70 dark:to-indigo-900/70 dark:text-blue-200",
      Interview:
        "bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 dark:from-yellow-900/70 dark:to-amber-900/70 dark:text-yellow-200",
      Offer:
        "bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 dark:from-green-900/70 dark:to-emerald-900/70 dark:text-green-200",
      Rejected:
        "bg-gradient-to-r from-red-100 to-rose-100 text-red-800 dark:from-red-900/70 dark:to-rose-900/70 dark:text-red-200",
    };
    return styles[status] || styles["Applied"]; // Default to 'Applied' style
  }

  /** Toggles button loading state */
  function setButtonLoading(
    button: HTMLButtonElement | null,
    isLoading: boolean,
    defaultText: string,
    iconId: string,
    spinnerId: string
  ) {
    if (!button) return;
    const textSpan = button.querySelector("span"); // Assuming text is in a span
    const icon = document.getElementById(iconId);
    const spinner = document.getElementById(spinnerId);

    button.disabled = isLoading;
    if (textSpan) {
      textSpan.textContent = isLoading
        ? defaultText.includes("Add")
          ? "Adding..."
          : "Saving..."
        : defaultText;
    }
    if (icon) icon.classList.toggle("hidden", isLoading);
    if (spinner) spinner.classList.toggle("hidden", !isLoading);
  }

  // ==========================
  // Core UI Rendering
  // ==========================

  /** Creates the HTML element for a single job card */
  function createJobCard(job: Job): HTMLDivElement {
    const timeAgo = getTimeAgo(job.dateApplied.toDate());
    const companyInitial = job.company.charAt(0).toUpperCase();

    const card = document.createElement("div");
    // Modern card design with glass morphism, subtle gradients, and micro-interactions
    card.className =
      "bg-white/90 dark:bg-gray-900/80 backdrop-blur-xl border border-gray-100/80 dark:border-gray-800/60 rounded-3xl transition-all duration-300 hover:shadow-2xl shadow-lg job-card hover:translate-y-[-4px] group relative overflow-hidden";

    // Add subtle gradient accent based on status - using className instead of classList.add to avoid space issues
    let gradientClasses = "before:absolute before:inset-0 before:opacity-30 before:rounded-3xl before:z-0 before:bg-gradient-to-r ";

    if (job.status === "Applied") {
      gradientClasses += "before:from-blue-500/20 before:to-indigo-500/20 dark:before:from-blue-600/20 dark:before:to-indigo-600/20";
    } else if (job.status === "Interview") {
      gradientClasses += "before:from-yellow-500/20 before:to-amber-500/20 dark:before:from-yellow-600/20 dark:before:to-amber-600/20";
    } else if (job.status === "Offer") {
      gradientClasses += "before:from-green-500/20 before:to-emerald-500/20 dark:before:from-green-600/20 dark:before:to-emerald-600/20";
    } else {
      gradientClasses += "before:from-red-500/20 before:to-rose-500/20 dark:before:from-red-600/20 dark:before:to-rose-600/20";
    }

    // Add the gradient classes to the existing class list
    card.className += " " + gradientClasses;

    card.dataset.jobId = job.id;

    card.innerHTML = `
      <div class="relative z-10">
        <div class="flex items-start justify-between gap-4 px-6 pt-6">
          <div class="flex items-center min-w-0">
            <div class="flex-shrink-0 mr-4">
              <div class="w-12 h-12 rounded-xl flex items-center justify-center text-xl font-bold ${
                job.status === "Applied"
                  ? "bg-blue-100 text-blue-700 dark:bg-blue-900/70 dark:text-blue-300"
                  : job.status === "Interview"
                    ? "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/70 dark:text-yellow-300"
                    : job.status === "Offer"
                      ? "bg-green-100 text-green-700 dark:bg-green-900/70 dark:text-green-300"
                      : "bg-red-100 text-red-700 dark:bg-red-900/70 dark:text-red-300"
              }">
                ${companyInitial}
              </div>
            </div>
            <div class="min-w-0">
              <h3 class="text-xl font-extrabold text-gray-900 dark:text-white truncate">${job.company}</h3>
              <div class="flex items-center gap-2 text-sm mt-1">
                <p class="text-gray-600 dark:text-gray-300 truncate font-medium">${job.position}</p>
                ${
                  job.url
                    ? `
                  <a href="${job.url}" target="_blank" rel="noopener noreferrer" class="inline-flex items-center text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300 transition-colors flex-shrink-0 group-hover:underline" title="View job posting">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                `
                    : ""
                }
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-2 flex-shrink-0">
            <span class="px-4 py-1.5 text-xs font-bold rounded-full ${getStatusStyle(job.status)} shadow-md flex items-center gap-1 border border-white/40 dark:border-gray-800/60 ring-2 ring-white/30 dark:ring-gray-900/40 backdrop-blur-sm transition-all duration-200">
              ${
                job.status === "Applied"
                  ? `<svg class="w-4 h-4 hidden sm:block" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" /></svg>`
                  : job.status === "Interview"
                    ? `<svg class="w-4 h-4 hidden sm:block" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M8 12h8M12 8v8" /></svg>`
                    : job.status === "Offer"
                      ? `<svg class="w-4 h-4 hidden sm:block" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M5 13l4 4L19 7" /></svg>`
                      : job.status === "Rejected"
                        ? `<svg class="w-4 h-4 hidden sm:block" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M6 18L18 6M6 6l12 12" /></svg>`
                        : ""
              }
              <span class="ml-1">${job.status}</span>
            </span>
          </div>
        </div>

        <div class="mt-4 space-y-4 px-6 pb-6">
          <div class="flex items-center justify-between text-xs">
            <div class="text-gray-500 dark:text-gray-400 font-medium flex items-center gap-1.5">
              <svg class="w-4 h-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3m8 4V3m-9 8h10m-12 8a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2H6a2 2 0 00-2 2v12z"/></svg>
              ${timeAgo}
            </div>
            <div class="flex items-center gap-1 bg-white/80 dark:bg-gray-800/60 rounded-full px-1.5 py-1 shadow-sm border border-gray-200/80 dark:border-gray-700/60">
              <button class="px-3 py-1 text-xs text-indigo-600 hover:bg-indigo-50 dark:text-indigo-300 dark:hover:bg-indigo-900/30 font-semibold rounded-full transition-colors edit-job-btn">Edit</button>
              <button class="px-3 py-1 text-xs text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/30 font-semibold rounded-full transition-colors delete-job-btn">Delete</button>
            </div>
          </div>

          <div class="pt-4 border-t border-gray-100 dark:border-gray-800/60">
            <div class="flex flex-wrap gap-2">
              <button class="text-xs px-4 py-2 bg-indigo-50 text-indigo-700 dark:bg-indigo-900/40 dark:text-indigo-300 rounded-full hover:bg-indigo-100 dark:hover:bg-indigo-800/50 font-bold shadow-sm transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 analyze-job-btn">
                <span class="flex items-center gap-1.5">
                  <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                  </svg>
                  Job Research
                </span>
              </button>
              <button class="text-xs px-4 py-2 bg-emerald-50 text-emerald-700 dark:bg-emerald-900/40 dark:text-emerald-300 rounded-full hover:bg-emerald-100 dark:hover:bg-emerald-800/50 font-bold shadow-sm transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 generate-resume-btn">
                <span class="flex items-center gap-1.5">
                  <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  Resume
                </span>
              </button>
              <button class="text-xs px-4 py-2 bg-purple-50 text-purple-700 dark:bg-purple-900/40 dark:text-purple-300 rounded-full hover:bg-purple-100 dark:hover:bg-purple-800/50 font-bold shadow-sm transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 generate-cover-letter-btn">
                <span class="flex items-center gap-1.5">
                  <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                  Cover Letter
                </span>
              </button>
              <button class="text-xs px-4 py-2 bg-amber-50 text-amber-700 dark:bg-amber-900/40 dark:text-amber-300 rounded-full hover:bg-amber-100 dark:hover:bg-amber-800/50 font-bold shadow-sm transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 interview-prep-btn">
                <span class="flex items-center gap-1.5">
                  <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                  </svg>
                  Interview Prep
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
    return card;
  }

  /** Filters and renders job cards based on current filter and snapshot */
  function renderFilteredJobs(snapshot?: QuerySnapshot) { // Accept optional snapshot argument
    // Use the provided snapshot if available, otherwise use the latest cached one
    const currentSnapshot = snapshot || latestSnapshot;

    if (!jobCardsContainer || !currentSnapshot) return;

    // Make the function globally accessible and add type declaration
    if (typeof window !== 'undefined') {
      (window as any).renderFilteredJobs = renderFilteredJobs; // Use 'any' to avoid TS error here
    }


    const selectedStatus = statusFilterSelect.value;
    jobCardsContainer.innerHTML = ""; // Clear existing cards

    const jobs: Job[] = currentSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...(doc.data() as Omit<Job, "id">),
    }));

    const filteredJobs = jobs.filter(
      (job) => selectedStatus === "all" || job.status === selectedStatus
    );

    // Hide loading state if it exists
    const loadingState = document.getElementById('loadingState');
    if (loadingState) {
      loadingState.style.display = 'none';
    }

    if (filteredJobs.length === 0) {
      jobCardsContainer.innerHTML = `
        <div class="flex flex-col items-center justify-center py-16 px-4">
          <div class="w-24 h-24 mb-6 rounded-full bg-gray-100 dark:bg-gray-800/50 flex items-center justify-center">
            <svg class="w-12 h-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">No applications found</h3>
          <p class="text-base text-gray-500 dark:text-gray-400 mb-1 text-center max-w-md">
            ${selectedStatus !== "all"
              ? `No job applications with status "${selectedStatus}" found.`
              : "You haven't added any job applications yet."}
          </p>
          <p class="text-sm text-gray-400 dark:text-gray-500 text-center max-w-md">
            ${selectedStatus !== "all"
              ? `Try selecting a different status filter or add new applications.`
              : `Click "Add New Application" to start tracking your job search progress.`}
          </p>
          <button
            id="emptyStateAddBtn"
            class="mt-6 px-5 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-[1.02] active:scale-95 focus:outline-none shadow-lg hover:shadow-xl"
          >
            <span class="flex items-center gap-2">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Add Your First Application
            </span>
          </button>
        </div>
      `;

      // Add event listener to the empty state add button
      document.getElementById('emptyStateAddBtn')?.addEventListener('click', openAddModal);

    } else {
      // Clear container first
      jobCardsContainer.innerHTML = '';

      // Add each job card with a staggered animation
      filteredJobs.forEach((job, index) => {
        const card = createJobCard(job);
        // Add animation delay based on index
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        card.style.transitionDelay = `${index * 0.05}s`;

        jobCardsContainer.appendChild(card);

        // Force a reflow to ensure the animation works
        void card.offsetWidth;

        // Animate in
        setTimeout(() => {
          card.style.opacity = '1';
          card.style.transform = 'translateY(0)';
        }, 10);
      });
    }
  }

  const debouncedRenderFilteredJobs = debounce(
    renderFilteredJobs,
    DEBOUNCE_DELAY
  );

  // ==========================
  // Modal Management Functions
  // ==========================

  function openModal(
    modalElement: HTMLElement | null,
    contentElement: HTMLElement | null
  ) {
    if (modalElement && contentElement) {
      modalElement.classList.remove("invisible", "opacity-0");
      requestAnimationFrame(() => {
        contentElement.classList.remove("scale-95", "opacity-0");
        contentElement.classList.add("scale-100", "opacity-100");
      });
    }
  }

  function closeModal(
    modalElement: HTMLElement | null,
    contentElement: HTMLElement | null,
    formElement?: HTMLFormElement | null
  ) {
    if (modalElement && contentElement) {
      contentElement.classList.add("scale-95", "opacity-0");
      contentElement.classList.remove("scale-100", "opacity-100");
      setTimeout(() => {
        modalElement.classList.add("invisible", "opacity-0");
        if (formElement) {
          formElement.reset();
          // Clear potential validation styles
          const urlInput = formElement.querySelector(
            'input[type="url"]'
          ) as HTMLInputElement | null;
          urlInput?.classList.remove(
            "border-red-500",
            "focus:border-red-500",
            "dark:border-red-500"
          );
        }
      }, 300); // Match CSS transition duration
    }
  }

  // --- Add Modal ---
  const openAddModal = () => openModal(addJobFormModal, addJobFormContent);
  const closeAddModal = () =>
    closeModal(addJobFormModal, addJobFormContent, addJobForm);

  // --- Edit Modal ---
  async function openEditModal(id: string) {
    try {
      const docRef = doc(db, "jobTracker", id);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists() && editForm) {
        const job = docSnap.data() as Omit<Job, "id">;

        // Populate edit form
        editJobIdInput.value = id;
        editCompanyInput.value = job.company;
        editPositionInput.value = job.position;
        editStatusSelect.value = job.status;
        editDateAppliedInput.value = formatDateForInput(job.dateApplied);
        editUrlInput.value = job.url || "";
        editJobDescriptionTextarea.value = job.jobDescription || "";
        editNotesTextarea.value = job.notes || "";

        openModal(editModal, editModalContent);
      } else {
        console.error("Job document not found for editing:", id);

        // Show error modal using our centralized error handling
        import("../../lib/errorHandling").then(({ showErrorModal }) => {
          showErrorModal(
            "jobTrackerErrorModal",
            "Job Not Found",
            "Could not find the job application details to edit."
          );
        }).catch(err => {
          console.error("Failed to import error handling utility:", err);
          // Fallback to alert if import fails
          alert("Could not find the job application details to edit.");
        });
      }
    } catch (error) {
      console.error("Error fetching job details for edit:", error);

      // Show error modal using our centralized error handling
      import("../../lib/errorHandling").then(({ showErrorModal }) => {
        showErrorModal(
          "jobTrackerErrorModal",
          "Error Loading Job Details",
          "There was an error retrieving job application details. Please try again."
        );
      }).catch(err => {
        console.error("Failed to import error handling utility:", err);
        // Fallback to alert if import fails
        alert("Error retrieving job application details. Please try again.");
      });
    }
  }
  const closeEditModal = () =>
    closeModal(editModal, editModalContent, editForm);

  // --- Delete Modal ---
  function openDeleteModal(id: string) {
    if (deleteModal && deleteJobIdInput) {
      deleteJobIdInput.value = id;

      // Show the modal with animation
      deleteModal.classList.remove("pointer-events-none");
      deleteModal.classList.add("opacity-100");

      // Animate the content
      const modalContent = document.getElementById("deleteModalContent");
      if (modalContent) {
        setTimeout(() => {
          modalContent.classList.remove("scale-95", "opacity-0");
          modalContent.classList.add("scale-100", "opacity-100");
        }, 50);
      }
    }
  }

  function closeDeleteModal() {
    if (deleteModal) {
      // Animate out
      const modalContent = document.getElementById("deleteModalContent");
      if (modalContent) {
        modalContent.classList.remove("scale-100", "opacity-100");
        modalContent.classList.add("scale-95", "opacity-0");
      }

      // Hide the modal after animation completes
      setTimeout(() => {
        deleteModal.classList.remove("opacity-100");
        deleteModal.classList.add("pointer-events-none");
        deleteJobIdInput.value = ""; // Clear the ID

        // Remove any previous error message
        const errorMsg = deleteModal.querySelector(".text-red-500");
        errorMsg?.remove();
      }, 300);
    }
  }

  // ==========================
  // Firebase & Logic Functions
  // ==========================

  /** Handles the actual deletion after confirmation */
  async function handleDeleteConfirmation() {
    const id = deleteJobIdInput.value;
    if (!id) return;

    confirmDeleteBtn?.setAttribute("disabled", "true"); // Disable button during deletion

    try {
      const jobDocRef = doc(db, "jobTracker", id);
      const jobDoc = await getDoc(jobDocRef);
      let userId = null;

      if (jobDoc.exists()) {
        const jobData = jobDoc.data();
        userId = jobData.userId; // Get user ID before deleting
        await deleteDoc(jobDocRef);
      } else {
        console.warn("Job not found for deletion, maybe already deleted?");
      }

      // Decrement usage count if user ID was found
      if (userId) {
        try {
          await TierManagementService.decrementJobTrackerUsage(userId);
        } catch (tierError) {
          console.error(
            "Error decrementing job tracker usage (deletion still successful):",
            tierError
          );
        }
      }

      closeDeleteModal();
    } catch (error) {
      console.error("Error deleting job:", error);

      // Show error modal using our centralized error handling
      import("../../lib/errorHandling").then(({ showErrorModal }) => {
        showErrorModal(
          "jobTrackerErrorModal",
          "Error Deleting Application",
          "There was an error deleting your job application. Please try again."
        );
      }).catch(err => {
        console.error("Failed to import error handling utility:", err);

        // Fallback to direct DOM manipulation if import fails
        const errorMessage = document.createElement("p");
        errorMessage.className = "text-red-500 dark:text-red-400 text-xs mt-2"; // Smaller text
        errorMessage.textContent = "Error deleting. Please try again.";
        deleteModal?.querySelector(".text-center")?.appendChild(errorMessage);
      });

      // Keep modal open to show error
    } finally {
      confirmDeleteBtn?.removeAttribute("disabled"); // Re-enable button
    }
  }

  /** Handles Add Job Form Submission */
  async function handleAddFormSubmit(event: SubmitEvent) {
    event.preventDefault();
    if (!addJobForm) return;

    const formData = new FormData(addJobForm);
    const submitButton = addJobForm.querySelector(
      'button[type="submit"]'
    ) as HTMLButtonElement | null;

    const rawUrl = formData.get("url") as string;
    const normalizedUrl = normalizeUrl(rawUrl);
    const urlInput = addJobForm.querySelector(
      "#url"
    ) as HTMLInputElement | null;

    // URL Validation Feedback
    if (rawUrl && !normalizedUrl && urlInput) {
      // Show error modal using our centralized error handling
      import("../../lib/errorHandling").then(({ showErrorModal }) => {
        showErrorModal(
          "jobTrackerErrorModal",
          "Invalid URL",
          "Please enter a valid URL (e.g., https://google.com or google.com)."
        );
      }).catch(err => {
        console.error("Failed to import error handling utility:", err);
        // Fallback to alert if import fails
        alert("Please enter a valid URL (e.g., https://google.com or google.com).");
      });

      urlInput.focus();
      urlInput.classList.add(
        "border-red-500",
        "focus:border-red-500",
        "dark:border-red-500"
      );
      return;
    } else if (urlInput) {
      urlInput.classList.remove(
        "border-red-500",
        "focus:border-red-500",
        "dark:border-red-500"
      );
    }

    setButtonLoading(
      submitButton,
      true,
      "Add Application",
      "addIcon",
      "addSpinner"
    );

    try {
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) {
        // Show error modal using our centralized error handling
        import("../../lib/errorHandling").then(({ showErrorModal }) => {
          showErrorModal(
            "jobTrackerErrorModal",
            "Authentication Required",
            "Please log in to add a job application."
          );
        }).catch(err => {
          console.error("Failed to import error handling utility:", err);
          // Fallback to alert if import fails
          alert("Please log in to add a job application.");
        });
        throw new Error("User not logged in"); // Throw error to trigger finally block correctly
      }

      // Check tier limit *before* adding
      await TierManagementService.trackJobTracker(user.uid); // Throws error if limit reached

      const jobData = {
        userId: user.uid,
        company: formData.get("company") as string,
        position: formData.get("position") as string,
        status: formData.get("status") as string,
        dateApplied: Timestamp.fromDate(
          new Date(formData.get("dateApplied") as string)
        ),
        url: normalizedUrl,
        jobDescription: formData.get("jobDescription") as string,
        notes: (formData.get("notes") as string) || "",
        createdAt: Timestamp.now(),
      };

      await addDoc(jobsCollectionRef, jobData);
      closeAddModal(); // Close modal on success
    } catch (error) {
      console.error("Error adding job application:", error);

      // Determine error message
      const displayError =
        error instanceof Error && error.message.includes("limit")
          ? error.message // Show specific limit error
          : "Error adding application. Please try again.";

      // Show error modal using our centralized error handling
      import("../../lib/errorHandling").then(({ showErrorModal }) => {
        showErrorModal(
          "jobTrackerErrorModal",
          "Error Adding Application",
          displayError
        );
      }).catch(err => {
        console.error("Failed to import error handling utility:", err);
        // Fallback to alert if import fails
        alert(displayError);
      });

      // Keep modal open on error
    } finally {
      // Reset button loading state using the loadingState utility
      import("../../lib/loadingState").then(({ setButtonLoading }) => {
        setButtonLoading("addButtonLoader", false);
      }).catch(err => {
        console.error("Failed to import loading state utility:", err);

        // Fallback to direct DOM manipulation if import fails
        setButtonLoading(
          submitButton,
          false,
          "Add Application",
          "addIcon",
          "addSpinner"
        );
      });
    }
  }

  /** Handles Edit Job Form Submission */
  async function handleEditFormSubmit(event: SubmitEvent) {
    event.preventDefault();
    if (!editForm) return;

    const formData = new FormData(editForm);
    const jobId = formData.get("editJobId") as string;
    if (!jobId) {
      console.error("Missing Job ID in edit form.");

      // Show error modal using our centralized error handling
      import("../../lib/errorHandling").then(({ showErrorModal }) => {
        showErrorModal(
          "jobTrackerErrorModal",
          "Missing Job ID",
          "Cannot save changes: Job ID is missing. Please try again."
        );
      }).catch(err => {
        console.error("Failed to import error handling utility:", err);
        // Fallback to alert if import fails
        alert("Cannot save changes: Job ID is missing.");
      });
      return;
    }

    const submitButton = editForm.querySelector(
      'button[type="submit"]'
    ) as HTMLButtonElement | null;

    const rawUrl = formData.get("editUrl") as string;
    const normalizedUrl = normalizeUrl(rawUrl);
    const urlInput = editForm.querySelector(
      "#editUrl"
    ) as HTMLInputElement | null;

    // URL Validation Feedback
    if (rawUrl && !normalizedUrl && urlInput) {
      // Show error modal using our centralized error handling
      import("../../lib/errorHandling").then(({ showErrorModal }) => {
        showErrorModal(
          "jobTrackerErrorModal",
          "Invalid URL",
          "Please enter a valid URL (e.g., https://google.com or google.com)."
        );
      }).catch(err => {
        console.error("Failed to import error handling utility:", err);
        // Fallback to alert if import fails
        alert("Please enter a valid URL (e.g., https://google.com or google.com).");
      });

      urlInput.focus();
      urlInput.classList.add(
        "border-red-500",
        "focus:border-red-500",
        "dark:border-red-500"
      );
      return;
    } else if (urlInput) {
      urlInput.classList.remove(
        "border-red-500",
        "focus:border-red-500",
        "dark:border-red-500"
      );
    }

    setButtonLoading(
      submitButton,
      true,
      "Save Changes",
      "saveIcon",
      "saveSpinner"
    );

    try {
      const docRef = doc(db, "jobTracker", jobId);
      await updateDoc(docRef, {
        company: formData.get("editCompany") as string,
        position: formData.get("editPosition") as string,
        status: formData.get("editStatus") as string,
        dateApplied: Timestamp.fromDate(
          new Date(formData.get("editDateApplied") as string)
        ),
        url: normalizedUrl,
        jobDescription: formData.get("editJobDescription") as string,
        notes: (formData.get("editNotes") as string) || "",
        updatedAt: Timestamp.now(),
      });
      closeEditModal(); // Close modal on success
    } catch (error) {
      console.error("Error updating job:", error);

      // Show error modal using our centralized error handling
      import("../../lib/errorHandling").then(({ showErrorModal }) => {
        showErrorModal(
          "jobTrackerErrorModal",
          "Error Updating Application",
          "There was an error updating your job application. Please try again."
        );
      }).catch(err => {
        console.error("Failed to import error handling utility:", err);
        // Fallback to alert if import fails
        alert("Error updating job application. Please try again.");
      });

      // Keep modal open on error
    } finally {
      // Reset button loading state using the loadingState utility
      import("../../lib/loadingState").then(({ setButtonLoading }) => {
        setButtonLoading("editButtonLoader", false);
      }).catch(err => {
        console.error("Failed to import loading state utility:", err);

        // Fallback to direct DOM manipulation if import fails
        setButtonLoading(
          submitButton,
          false,
          "Save Changes",
          "saveIcon",
          "saveSpinner"
        );
      });
    }
  }

  /** Handles clicks for redirecting to other tool pages */
  async function handleToolRedirect(event: MouseEvent) {
    // The target element is already verified to be a tool button or its child
    // by the event delegation code, so we can directly use it
    const target = event.target as Element;
    const actionButton = target.closest(
      ".analyze-job-btn, .generate-resume-btn, .generate-cover-letter-btn, .interview-prep-btn"
    ) as HTMLElement;

    // This should never happen since we check in the event delegation, but just to be safe
    if (!actionButton) return;

    const jobCard = actionButton.closest(".job-card") as HTMLElement | null;
    const jobId = jobCard?.dataset.jobId;
    if (!jobId) return;

    // Determine target page based on button class
    let targetPage = "";
    if (actionButton.classList.contains("analyze-job-btn"))
      targetPage = "/job-research";
    else if (actionButton.classList.contains("generate-resume-btn"))
      targetPage = "/resume";
    else if (actionButton.classList.contains("generate-cover-letter-btn"))
      targetPage = "/cover-letter";
    else if (actionButton.classList.contains("interview-prep-btn"))
      targetPage = "/interview-prep";
    else return; // Should not happen if selector is correct

    try {
      const jobDoc = await getDoc(doc(db, "jobTracker", jobId));
      if (jobDoc.exists()) {
        const jobData = jobDoc.data();
        // Store essential job data in localStorage for the target page
        localStorage.setItem(
          "currentJob",
          JSON.stringify({
            company: jobData.company,
            position: jobData.position,
            jobDescription: jobData.jobDescription || "",
          })
        );
        // Redirect
        window.location.href = targetPage;
      } else {
        console.warn("Job data not found for redirect.");
      }
    } catch (error) {
      console.error("Error fetching job data for redirect:", error);

      // Show error modal using our centralized error handling
      import("../../lib/errorHandling").then(({ showErrorModal }) => {
        showErrorModal(
          "jobTrackerErrorModal",
          "Error Loading Job Data",
          "Could not load job data for the tool. Please try again."
        );
      }).catch(err => {
        console.error("Failed to import error handling utility:", err);
        // Fallback to alert if import fails
        alert("Could not load job data for the tool. Please try again.");
      });
    }
  }

  // ==========================
  // Event Listeners Setup
  // ==========================

  function initializeEventListeners() {
    // Filter Listener
    statusFilterSelect?.addEventListener("change", () => debouncedRenderFilteredJobs()); // Wrap in arrow function to ignore event

    // Modal Triggers
    addJobBtn?.addEventListener("click", openAddModal);
    closeAddModalBtn?.addEventListener("click", closeAddModal);
    closeEditModalBtn?.addEventListener("click", closeEditModal);
    cancelDeleteBtn?.addEventListener("click", closeDeleteModal);

    // Modal Background Click & Escape Key
    addJobFormModal?.addEventListener("click", (e) => {
      if (e.target === addJobFormModal) closeAddModal();
    });
    editModal?.addEventListener("click", (e) => {
      if (e.target === editModal) closeEditModal();
    });
    deleteModal?.addEventListener("click", (e) => {
      if (e.target === deleteModal) closeDeleteModal();
    });

    document.addEventListener("keydown", (e) => {
      if (e.key === "Escape") {
        if (addJobFormModal && !addJobFormModal.classList.contains("invisible"))
          closeAddModal();
        else if (editModal && !editModal.classList.contains("invisible"))
          closeEditModal();
        else if (deleteModal && !deleteModal.classList.contains("hidden"))
          closeDeleteModal();
      }
    });

    // Form Submissions
    addJobForm?.addEventListener("submit", handleAddFormSubmit);
    editForm?.addEventListener("submit", handleEditFormSubmit);
    confirmDeleteBtn?.addEventListener("click", handleDeleteConfirmation);

    // Event Delegation for Job Card Buttons (Edit, Delete, Tools)
    jobCardsContainer?.addEventListener("click", (event) => {
      const target = event.target as HTMLElement;
      const jobCard = target.closest(".job-card") as HTMLElement | null;
      const jobId = jobCard?.dataset.jobId;

      if (!jobId) return; // Click was not on a button within a card

      // Find the closest button that was clicked (handles clicks on nested elements like spans and SVGs)
      const editButton = target.closest(".edit-job-btn");
      const deleteButton = target.closest(".delete-job-btn");
      const toolButton = target.closest(
        ".analyze-job-btn, .generate-resume-btn, .generate-cover-letter-btn, .interview-prep-btn"
      );

      if (editButton) {
        openEditModal(jobId);
      } else if (deleteButton) {
        openDeleteModal(jobId);
      } else if (toolButton) {
        // Delegate tool redirects to a separate handler
        handleToolRedirect(event as MouseEvent); // Pass the original event
      }
    });
  }

  // ==========================
  // Initialization & Realtime Listener
  // ==========================

  // Listen for real-time updates to the job list
  const jobsQuery = query(jobsCollectionRef, orderBy("dateApplied", "desc")); // Order by date applied

  // We're using onSnapshot for real-time updates, but we don't need to unsubscribe
  // since this component lives for the entire page lifecycle
  onSnapshot(
    jobsQuery,
    (snapshot) => {
      latestSnapshot = snapshot; // Cache the latest snapshot
      renderFilteredJobs(); // Render jobs based on the new data and current filter
    },
    (error) => {
      console.error("Error listening to job updates:", error);

      // Show error in the job cards container
      if (jobCardsContainer) {
        // Show a more user-friendly error message
        jobCardsContainer.innerHTML = `
          <div class="flex flex-col items-center justify-center py-12 px-4">
            <div class="w-16 h-16 mb-4 text-red-500 dark:text-red-400">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <p class="text-center text-red-600 dark:text-red-400 font-medium mb-2">Error loading job applications</p>
            <p class="text-center text-gray-500 dark:text-gray-400 text-sm">Please check your connection or try again later.</p>
            <button
              id="retryButton"
              class="mt-4 px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
              onclick="window.location.reload()"
            >
              Retry
            </button>
          </div>
        `;
      }

      // Show error modal using our centralized error handling
      import("../../lib/errorHandling").then(({ showErrorModal }) => {
        showErrorModal(
          "jobTrackerErrorModal",
          "Error Loading Applications",
          "There was an error loading your job applications. Please check your connection or try again later."
        );
      }).catch(err => {
        console.error("Failed to import error handling utility:", err);
      });
    }
  );

  // Initialize event listeners when the DOM is ready
  if (typeof window !== "undefined") {
    document.addEventListener("DOMContentLoaded", initializeEventListeners);
  }
</script>

<!-- Error Modal Component -->
<ErrorModal
  id="jobTrackerErrorModal"
  title="Error"
  message="An error occurred. Please try again."
  isOpen={false}
  zIndex={60}
/>
