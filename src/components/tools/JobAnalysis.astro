---
import type { JobAnalysisData } from "../../types/JobAnalysis";
import UpgradePrompt from "../UpgradePrompt.astro";
import ErrorModal from "../ErrorModal.astro";
import LoadingSpinner from "../LoadingSpinner.astro";
import ButtonLoader from "../ButtonLoader.astro";
import UsageDisplay from "../UsageDisplay.astro";

// Initialize empty analysis data
// (Keep the initial empty analysis data structure as is)
const analysis: JobAnalysisData = {
  jobRoleSummary: {
    title: "",
    seniorityLevel: "",
    keyResponsibilities: [],
    mainExpectations: [],
    reportingStructure: "",
    teamSize: "",
    workMode: "",
  },
  skillsAndQualifications: {
    hardSkills: [],
    softSkills: [],
    requiredCertifications: [],
    educationLevel: "",
    experienceRequired: "",
    preferredSkills: [],
  },
  compensationDetails: {
    baseSalaryRange: "",
    totalCompensationRange: "",
    equityDetails: {
      type: "",
      vestingSchedule: "",
      approximateValue: "",
    },
    benefits: {
      healthcare: [],
      retirement: [],
      additionalPerks: [],
    },
    bonusStructure: {
      type: "",
      frequency: "",
      amount: "",
    },
    industryComparison: "",
  },
  companyInsights: {
    name: "",
    size: "",
    industry: "",
    fundingDetails: {
      stage: "",
      totalRaised: "",
      lastRound: "",
      keyInvestors: [],
    },
    growth: {
      metrics: [],
      revenueRange: "",
      userGrowth: "",
    },
    culture: {
      values: [],
      workLifeBalance: "",
      learningOpportunities: "",
      diversityInitiatives: [],
    },
    employeeStats: {
      ratings: {
        overall: 0,
        workLifeBalance: 0,
        careerGrowth: 0,
        compensation: 0,
        culture: 0,
      },
      averageTenure: "",
      turnoverRate: "",
      promotionRate: "",
    },
    competitors: [],
  },
  careerGrowth: {
    promotionPath: [],
    learningBudget: "",
    mentorshipPrograms: [],
    skillDevelopmentOpportunities: [],
  },
  jobMarketTrends: {
    currentDemand: "",
    growthProjection: "",
    industryTrends: [],
    salaryTrends: "",
    skillsInDemand: [],
    competitorSalaries: [],
  },
  workCulture: {
    workSchedule: "",
    remotePolicy: "",
    workEnvironment: [],
    teamDynamics: "",
    managementStyle: "",
    redFlags: [],
    positiveIndicators: [],
  },
  applicationTips: {
    resumeAdvice: [],
    coverLetterTips: [],
    interviewPrep: {
      commonQuestions: [],
      technicalAssessment: "",
      processSteps: [],
    },
    negotiationTips: [],
  },
};
---

<div class="relative max-w-6xl mx-auto py-16 w-full">
  <div
    aria-hidden="true"
    class="absolute inset-0 grid grid-cols-2 -space-x-52 opacity-40 dark:opacity-20"
  >
    <div
      class="blur-[106px] h-56 bg-gradient-to-br from-primary to-purple-400 dark:from-blue-700 dark:to-indigo-600"
    >
    </div>
    <div
      class="blur-[106px] h-32 bg-gradient-to-r from-cyan-400 to-sky-300 dark:to-indigo-600 dark:from-emerald-500"
    >
    </div>
  </div>
  <div class="relative w-full">
    <div class="container mx-auto md:px-6 lg:px-8">
      <div class="w-full mx-auto">
        <form
          id="jobAnalysisForm"
          class="w-full space-y-6 sm:space-y-8 bg-white dark:bg-gray-900/80 backdrop-blur-md rounded-3xl p-4 sm:p-8
                    shadow-xl border-gray-100 border dark:border-gray-700/50
                    transition-all duration-300 ease-in-out hover:shadow-2xl job-analysis-form"
        >
          <div id="jobAnalysisFormContent" class="hidden">
            <div class="form-content">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Company Name Input -->
                <div class="space-y-2">
                  <label
                    for="companyName"
                    class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Company Name<span class="text-red-600"> *</span>
                  </label>
                  <div class="relative">
                    <input
                      id="companyName"
                      type="text"
                      placeholder="e.g., Newton School, Google, Startup X"
                      required
                      class="w-full px-4 py-3 border border-gray-300 dark:border-gray-700/70 bg-white dark:bg-gray-800/30 rounded-xl
                                          dark:text-gray-100 focus:border-primary dark:focus:border-primary focus:ring-1 focus:ring-primary
                                          outline-none text-sm transition-colors duration-200"
                    />
                  </div>
                </div>

                <!-- Job Position Input -->
                <div class="space-y-2">
                  <label
                    for="jobPosition"
                    class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Job Position<span class="text-red-600"> *</span>
                  </label>
                  <div class="relative">
                    <input
                      id="jobPosition"
                      type="text"
                      placeholder="e.g., Product Manager, Software Engineer"
                      required
                      class="w-full px-4 py-3 border border-gray-300 dark:border-gray-700/70 bg-white dark:bg-gray-800/30 rounded-xl
                                          dark:text-gray-100 focus:border-primary dark:focus:border-primary focus:ring-1 focus:ring-primary
                                          outline-none text-sm transition-colors duration-200"
                    />
                  </div>
                </div>
              </div>

              <!-- Submit Button -->
              <div
                class="flex flex-col sm:flex-row justify-center items-center space-y-2.5 sm:space-y-0 sm:space-x-4 pt-4"
              >
                <button
                  type="submit"
                  id="analyzeJobBtn"
                  class="w-full sm:w-auto inline-flex h-11 sm:h-12 items-center justify-center px-6 sm:px-8 py-2 sm:py-3
                                  text-sm sm:text-base font-semibold
                                  text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700
                                  dark:text-white dark:bg-gradient-to-r dark:from-blue-500 dark:to-indigo-500 dark:hover:from-blue-600 dark:hover:to-indigo-600
                                  rounded-full
                                  transition-all duration-300 ease-in-out
                                  transform hover:scale-[1.03] active:scale-95
                                  focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900
                                  shadow-lg hover:shadow-xl"
                >
                  <ButtonLoader
                    id="analyzeButtonLoader"
                    isLoading={false}
                    loadingText="Researching..."
                    spinnerPosition="left"
                    spinnerSize="md"
                    spinnerColor="white"
                    className="flex-row"
                  >
                    <span class="inline-flex items-center">
                      <svg
                        class="w-5 h-5 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                        ><path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg
                      >
                      Research Now
                    </span>
                  </ButtonLoader>
                </button>
                <button
                  type="button"
                  id="clearForm"
                  class="w-full sm:w-auto inline-flex h-11 sm:h-12 items-center justify-center px-6 sm:px-8 py-2 sm:py-3
                                  text-sm sm:text-base font-semibold
                                  text-gray-700 bg-gray-100 hover:bg-gray-200
                                  dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600
                                  border border-transparent
                                  rounded-full
                                  transition-all duration-300 ease-in-out
                                  transform hover:scale-[1.03] active:scale-95
                                  focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-offset-gray-900
                                  shadow-md hover:shadow-lg"
                >
                  <svg
                    class="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                    ><path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    ></path></svg
                  >
                  Clear
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Skeletal Loader -->
<div id="skeletalLoader" class="mt-12 text-left px-4 sm:px-6 lg:px-8">
  <!-- Loading text -->
  <div class="text-center mb-6">
    <p class="text-xl font-semibold text-gray-900 dark:text-white">
      <span class="animate-pulse">Researching Job</span>
    </p>
    <div class="mt-3 flex justify-center items-center space-x-2">
      <LoadingSpinner
        size="sm"
        color="primary"
      />
      <p class="text-sm text-gray-500 dark:text-gray-400">
        Gathering insights, comparing data... This usually takes 5-15 seconds.
      </p>
    </div>
  </div>
  <!-- Simplified Skeleton for the new layout -->
  <div class="max-w-7xl mx-auto p-4 md:p-6 lg:p-8">
    <div
      class="bg-gray-100 dark:bg-gray-800/50 backdrop-blur-sm p-6 sm:p-8 rounded-3xl shadow-md animate-pulse border border-gray-200 dark:border-gray-700/50"
    >
      <!-- Skeleton Header -->
      <div
        class="h-7 bg-gray-300 dark:bg-gray-700 rounded-md w-3/4 mx-auto mb-4"
      >
      </div>
      <div
        class="h-4 bg-gray-300 dark:bg-gray-700 rounded-md w-1/2 mx-auto mb-8"
      >
      </div>

      <!-- Skeleton Key Metrics -->
      <div
        class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 border-b border-gray-300 dark:border-gray-700/50 pb-8"
      >
        <div class="bg-gray-200 dark:bg-gray-700/70 p-5 rounded-xl h-24"></div>
        <div class="bg-gray-200 dark:bg-gray-700/70 p-5 rounded-xl h-24"></div>
        <div class="bg-gray-200 dark:bg-gray-700/70 p-5 rounded-xl h-24"></div>
      </div>

      <!-- Skeleton Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-x-10 gap-y-8">
        <!-- Skeleton Column 1 -->
        <div class="space-y-8">
          <div
            class="h-6 bg-gray-300 dark:bg-gray-700 rounded w-1/3 mb-4 border-b border-gray-300 dark:border-gray-700/50 pb-2"
          >
          </div>
          <div class="space-y-3">
            <div class="h-4 bg-gray-300 dark:bg-gray-700 rounded w-1/4 mb-2">
            </div>
            <div class="h-10 bg-gray-200 dark:bg-gray-600 rounded w-full"></div>
            <div class="h-10 bg-gray-200 dark:bg-gray-600 rounded w-full"></div>
          </div>
          <div class="space-y-3">
            <div class="h-4 bg-gray-300 dark:bg-gray-700 rounded w-1/4 mb-2">
            </div>
            <div class="flex flex-wrap gap-2">
              <div class="h-6 w-16 bg-gray-200 dark:bg-gray-600 rounded-full">
              </div>
              <div class="h-6 w-20 bg-gray-200 dark:bg-gray-600 rounded-full">
              </div>
              <div class="h-6 w-12 bg-gray-200 dark:bg-gray-600 rounded-full">
              </div>
            </div>
          </div>
          <div
            class="h-6 bg-gray-300 dark:bg-gray-700 rounded w-1/3 mb-4 border-b border-gray-300 dark:border-gray-700/50 pb-2 mt-8"
          >
          </div>
          <div class="space-y-3">
            <div class="h-4 bg-gray-300 dark:bg-gray-700 rounded w-1/4 mb-2">
            </div>
            <div class="h-10 bg-gray-200 dark:bg-gray-600 rounded w-full"></div>
            <div class="h-10 bg-gray-200 dark:bg-gray-600 rounded w-full"></div>
          </div>
        </div>
        <!-- Skeleton Column 2 -->
        <div class="space-y-8">
          <div
            class="h-6 bg-gray-300 dark:bg-gray-700 rounded w-1/3 mb-4 border-b border-gray-300 dark:border-gray-700/50 pb-2"
          >
          </div>
          <div class="space-y-3">
            <div class="h-4 bg-gray-300 dark:bg-gray-700 rounded w-1/4 mb-2">
            </div>
            <div class="grid grid-cols-2 gap-3">
              <div class="h-10 bg-gray-200 dark:bg-gray-600 rounded"></div>
              <div class="h-10 bg-gray-200 dark:bg-gray-600 rounded"></div>
              <div class="h-10 bg-gray-200 dark:bg-gray-600 rounded"></div>
              <div class="h-10 bg-gray-200 dark:bg-gray-600 rounded"></div>
            </div>
          </div>
          <div class="space-y-3">
            <div class="h-4 bg-gray-300 dark:bg-gray-700 rounded w-1/4 mb-2">
            </div>
            <div class="grid grid-cols-4 gap-3">
              <div class="h-16 bg-gray-200 dark:bg-gray-600 rounded"></div>
              <div class="h-16 bg-gray-200 dark:bg-gray-600 rounded"></div>
              <div class="h-16 bg-gray-200 dark:bg-gray-600 rounded"></div>
              <div class="h-16 bg-gray-200 dark:bg-gray-600 rounded"></div>
            </div>
          </div>
          <div
            class="h-6 bg-gray-300 dark:bg-gray-700 rounded w-1/3 mb-4 border-b border-gray-300 dark:border-gray-700/50 pb-2 mt-8"
          >
          </div>
          <div class="space-y-3">
            <div class="h-4 bg-gray-300 dark:bg-gray-700 rounded w-1/4 mb-2">
            </div>
            <div class="h-10 bg-gray-200 dark:bg-gray-600 rounded w-full"></div>
            <div class="h-10 bg-gray-200 dark:bg-gray-600 rounded w-full"></div>
          </div>
        </div>
      </div>
      <!-- Skeleton Disclaimer -->
      <div class="mt-10 pt-6 border-t border-gray-300 dark:border-gray-700/50">
        <div class="h-3 bg-gray-300 dark:bg-gray-700 rounded w-full mx-auto">
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Results Container - Now just an empty container to be filled -->
<div
  id="analysisResultContainer"
  class="hidden mt-12 w-full px-2 sm:px-4 lg:px-6 text-left"
>
  <!-- Content will be dynamically injected here by generateAnalysisHTML -->
</div>

<!-- Upgrade Prompt Component -->
<UpgradePrompt
  id="jobAnalysisUpgradePrompt"
  featureName="job analyses"
  isOpen={false}
/>

<!-- Error Modal Component -->
<ErrorModal
  id="jobAnalysisErrorModal"
  title="Error"
  message="An error occurred. Please try again."
  isOpen={false}
  zIndex={60}
/>

<style>

  .animate-spin-slow {
    animation: spin 2s linear infinite;
    border-color: #3b82f6;
    opacity: 0.8;
  }

  .dark .animate-spin-slow {
    border-color: #60a5fa;
  }

  .animate-spin-slower {
    animation: spin 3s linear infinite;
    border-color: rgba(59, 130, 246, 0.5);
    opacity: 0.6;
  }

  .dark .animate-spin-slower {
    border-color: rgba(96, 165, 250, 0.5);
  }
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  /* Removed typing animation as it's less relevant now */
  .tooltip {
    visibility: hidden; /* Hide by default */
    opacity: 0;
    transition:
      opacity 0.2s,
      visibility 0.2s;
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-4px); /* Added slight offset */
    background-color: rgba(17, 24, 39, 0.9); /* Dark background */
    color: white; /* White text */
    border-radius: 4px;
    padding: 4px 8px;
    z-index: 10;
    white-space: nowrap; /* Prevent wrapping */
    font-size: 0.75rem; /* Smaller font */
    pointer-events: none; /* Allow clicking through */
  }
  .relative:hover .tooltip {
    visibility: visible;
    opacity: 1;
  }
  .job-analysis-form input:required:invalid,
  .job-analysis-form textarea:required:invalid {
    /* Subtle indication for invalid fields *before* submission attempt */
    /* border-color: #e5e7eb; /* Keep default border */
  }
  .job-analysis-form:user-invalid input:required,
  .job-analysis-form:user-invalid textarea:required {
    /* Styles for invalid fields *after* submission attempt */
    border-color: #f87171; /* Red border */
    box-shadow: 0 0 0 1px #f87171;
  }

  /* Ensure Font Awesome is loaded if you use icons */
  /* Example: <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-..." crossorigin="anonymous" referrerpolicy="no-referrer" /> */
</style>

<script>
  import type { JobAnalysisData } from "../../types/JobAnalysis";
  import { ComponentInitializer } from "../../lib/componentInitializer";
  import { authService } from "../../lib/auth"; // Import authService
  import { ErrorHandler } from "../../lib/errorHandler";
  import { LoadingManager } from "../../lib/loadingManager";
  import { FormValidator } from "../../lib/formValidator";
  import { APIClient } from "../../lib/apiClient";
  import { DataUtils } from "../../lib/dataUtils";
  import "../../scripts/jobAnalysisOptimistic.js";

  document.addEventListener("DOMContentLoaded", async () => {
    // Initialize utility classes
    const componentId = "jobAnalysis";
    const initializer = new ComponentInitializer(componentId);

    // Get DOM elements
    const form = document.querySelector(
      "#jobAnalysisForm"
    ) as HTMLFormElement | null;
    const formContent = document.getElementById('jobAnalysisFormContent');
    const skeletalLoader = document.querySelector(
      "#skeletalLoader"
    ) as HTMLDivElement | null;
    const analysisResultContainer = document.querySelector(
      "#analysisResultContainer"
    ) as HTMLDivElement | null;
    const submitButton = form?.querySelector(
      'button[type="submit"]'
    ) as HTMLButtonElement | null;
    const analyzeButtonText = submitButton?.querySelector(
      "span"
    ) as HTMLSpanElement | null;

    let currentAnalysis: JobAnalysisData | null = null; // Initialize as null

    // Hide loader and show form content immediately
    if (skeletalLoader) skeletalLoader.classList.add('hidden');
    if (formContent) formContent.classList.remove('hidden');

    // Check access when the component loads
    const initialAccess = await initializer.checkFeatureAccess();

    // Disable submit button if usage limit is reached
    if (initialAccess.usageLimitReached && submitButton) {
      submitButton.setAttribute("disabled", "true");
      submitButton.classList.add("opacity-50", "cursor-not-allowed");
      if (analyzeButtonText) analyzeButtonText.textContent = "Limit Reached";
      submitButton.title = "Usage limit reached. Please upgrade your plan.";
    }

    // Check for stored job data (used for pre-filling form, e.g., on page reload)
    // Note: localStorage has size limitations and may not be suitable for very large job descriptions.
    const storedJob = localStorage.getItem("currentJob");
    if (storedJob) {
      try {
        const jobData = JSON.parse(storedJob);
        const companyInput = document.getElementById(
          "companyName"
        ) as HTMLInputElement;
        const positionInput = document.getElementById(
          "jobPosition"
        ) as HTMLInputElement;
        if (companyInput) companyInput.value = jobData.company || "";
        if (positionInput) positionInput.value = jobData.position || "";

        // Clear the stored data after use
        localStorage.removeItem("currentJob");
      } catch (error) {
        console.error("Failed to parse stored job data from localStorage:", error);
        // Clear potentially corrupted data
        localStorage.removeItem("currentJob");
      }
    }

    // --- Form Submit Event Listener ---
    form?.addEventListener("submit", async (event) => {
      event.preventDefault();
      form.classList.add(":user-invalid"); // Trigger :user-invalid styles if needed

      // Initialize utility classes
      const componentId = "jobAnalysis";
      const errorHandler = new ErrorHandler(componentId);
      const loadingManager = new LoadingManager(componentId);

      // --- Initial Input Validation ---
      const companyNameInput = document.querySelector(
        "#companyName"
      ) as HTMLInputElement | null;
      const jobPositionInput = document.querySelector(
        "#jobPosition"
      ) as HTMLInputElement | null;

      // Validate inputs using FormValidator
      const inputs = [companyNameInput, jobPositionInput].filter(Boolean) as (HTMLInputElement | HTMLTextAreaElement)[];
      const isValid = FormValidator.validateForm(inputs, {
        companyName: { required: true, minLength: 2 },
        jobPosition: { required: true, minLength: 2 },
      });

      if (!isValid) {
        return; // Stop submission if basic validation fails
      }

      // --- Access Check ---
      const initializer = new ComponentInitializer(componentId);
      const access = await initializer.checkFeatureAccess();
      if (!access.canAccess) {
        return; // Stop if user cannot access the feature
      }

      // --- UI Update: Start Loading ---
      loadingManager.setButtonLoading("analyzeButtonLoader", true, "Researching...");
      if (skeletalLoader) skeletalLoader.classList.remove("hidden");
      if (analysisResultContainer) {
        analysisResultContainer.classList.add("hidden"); // Hide previous results
        analysisResultContainer.innerHTML = ""; // Clear previous results
      }

      // --- API Call ---
      try {
        const requestBody = {
          companyName: companyNameInput!.value,
          jobPosition: jobPositionInput!.value,
        };

        // Use APIClient for the API call
        // Use EventSource for Server-Sent Events (SSE)
        // Get the authentication token
        const user = await authService.getCurrentUser();
        if (!user) {
           await errorHandler.showError("Authentication Required", "Please log in to research jobs.");
           loadingManager.setButtonLoading("analyzeButtonLoader", false);
           if (skeletalLoader) skeletalLoader.classList.add("hidden");
           return; // Stop if user is not authenticated
        }
        const token = await user.getIdToken();

        // Use EventSource for Server-Sent Events (SSE)
        // Include the token as a query parameter for authentication with SSE
        const eventSource = new EventSource(
          `/.netlify/functions/jobAnalysis?companyName=${encodeURIComponent(companyNameInput!.value)}&jobPosition=${encodeURIComponent(jobPositionInput!.value)}&token=${encodeURIComponent(token)}`,
          {
            withCredentials: true // Send cookies (though token in query is primary auth for SSE here)
          }
        );

        let receivedData = ""; // Accumulate streamed data

        eventSource.onmessage = (event) => {
          console.log("Received SSE message:", event.data);
          receivedData += event.data; // Append incoming data

          // Attempt to parse the accumulated data as JSON
          try {
            const partialAnalysis: JobAnalysisData = JSON.parse(receivedData);
            currentAnalysis = partialAnalysis; // Update current analysis with partial/complete data

            // Update UI with the latest data
            if (analysisResultContainer) {
              analysisResultContainer.innerHTML = generateAnalysisHTML(currentAnalysis);
              analysisResultContainer.classList.remove("hidden");
              // Optional: scrollIntoView if you want to auto-scroll on updates
              // analysisResultContainer.scrollIntoView({ behavior: "smooth", block: "start" });
            }

            // If the data is complete (e.g., contains a specific final marker or structure)
            // you might close the connection here. However, since the backend sends the full JSON
            // in chunks, the 'end' event is more reliable for completion.

          } catch (parseError) {
            // This is expected for partial JSON chunks. Do nothing.
            // console.warn("Failed to parse partial JSON:", parseError);
          }
        };

        eventSource.onerror = async (error) => {
          console.error("SSE Error:", error);
          eventSource.close(); // Close the connection on error

          // Check if the error indicates a closed connection (which might be successful completion)
          if (eventSource.readyState === EventSource.CLOSED) {
            console.log("SSE connection closed (via onerror).");
            // Check if currentAnalysis has data to indicate successful completion
            if (currentAnalysis && Object.keys(currentAnalysis).length > 0) {
               if (access.userId) { // Track usage on completion
                 try {
                   await initializer.trackFeatureUsage(access.userId);
                 } catch (trackingError) {
                   console.warn("Failed to track job research usage, but content was generated successfully:", trackingError);
                 }
               }
               // If it was a clean close after receiving data, update UI state
               loadingManager.setButtonLoading("analyzeButtonLoader", false);
               if (skeletalLoader) skeletalLoader.classList.add("hidden");
               if (analysisResultContainer && !analysisResultContainer.classList.contains("hidden")) {
                  analysisResultContainer.scrollIntoView({ behavior: "smooth", block: "start" });
               }
               return; // Exit the error handler as it was a successful close
            }
          }

          // If we reach here, it's an actual error during streaming
          let errorMessage = "An error occurred during analysis streaming.";
          if (error instanceof Event && error.type === 'error') {
             errorMessage = "Analysis streaming failed. Please check your network connection or try again.";
          } else if (error instanceof Error) {
              errorMessage = error.message;
          }

          await errorHandler.showError("Analysis Streaming Error", errorMessage);
          if (analysisResultContainer) {
            analysisResultContainer.innerHTML = errorHandler.createErrorHTML(errorMessage);
            analysisResultContainer.classList.remove("hidden");
            analysisResultContainer.scrollIntoView({ behavior: "smooth", block: "start" });
          }
          loadingManager.setButtonLoading("analyzeButtonLoader", false);
          if (skeletalLoader) skeletalLoader.classList.add("hidden");
        };

        eventSource.onopen = () => {
          console.log("SSE connection opened.");
          // Update loading message for streaming
          if (skeletalLoader?.querySelector('p.text-xl')) {
            (skeletalLoader.querySelector('p.text-xl') as HTMLElement).textContent = "Analysis in progress...";
          }
          if (skeletalLoader?.querySelector('p.text-sm')) {
            (skeletalLoader.querySelector('p.text-sm') as HTMLElement).textContent = "Receiving insights...";
          }
        };

        // Listen for the 'end' event or rely on the backend closing the connection
        // The backend is expected to close the connection after sending the full data.
        // If the backend sends a specific 'end' event, you can listen for that:
        // eventSource.addEventListener('end', () => {
        //   console.log("SSE stream ended.");
        //   eventSource.close();
        //   loadingManager.setButtonLoading("analyzeButtonLoader", false);
        //   if (skeletalLoader) skeletalLoader.classList.add("hidden");
        //   if (analysisResultContainer) {
        //      analysisResultContainer.scrollIntoView({ behavior: "smooth", block: "start" });
        //   }
        // });

        // If the backend simply closes the connection after the last data chunk,
        // the 'error' event might fire with a readyState of 2 (CLOSED).
        // We handle completion by checking if the received data is a complete JSON object
        // in the onmessage handler, and the backend closing the connection will trigger onerror
        // with a closed state, which we handle by closing the connection explicitly.

        // Track usage when the stream successfully completes (or after a certain amount of data is received)
        // This might need refinement based on how the backend signals completion.
        // For now, we'll track usage when the connection closes *without* an explicit error,
        // assuming a successful stream completion. This is a simplification and might need adjustment.

        // Note: The initial APIClient.analyzeJob call is replaced by the EventSource connection.
        // The error handling for the initial call is now handled by the EventSource onerror.

      } catch (error) { // This catch is for the initial APIClient.analyzeJob call
        console.error("Job research Initial Request Error:", error);
        if (analysisResultContainer) {
          const message = error instanceof Error ? error.message : "Failed to initiate job research.";
          analysisResultContainer.innerHTML = errorHandler.createErrorHTML(message, error instanceof Error ? error.stack : undefined);
          analysisResultContainer.classList.remove("hidden");
          analysisResultContainer.scrollIntoView({ behavior: "smooth", block: "start" });
        }
        await errorHandler.showError(
          "Job research Error",
          error instanceof Error ? error.message : "Failed to start analysis. Please try again."
        );
        // Ensure loading state is reset even if initial call fails
        if (skeletalLoader) skeletalLoader.classList.add("hidden");
        loadingManager.setButtonLoading("analyzeButtonLoader", false);
      }
      // Note: The 'finally' block is removed here because loading state is managed within the polling logic or initial error.
    });

    // --- NEW generateAnalysisHTML function ---
    function generateAnalysisHTML(analysis: JobAnalysisData): string {
      // Use DataUtils for safe property access
      const safeGet = DataUtils.safeGet;

      // Helper function to generate list items with consistent styling
      const generateListItem = (
        item: string | undefined | null,
        bgColor: string = "bg-gray-50 dark:bg-gray-800/60",
        textColor: string = "text-gray-700 dark:text-gray-400"
      ): string => {
        if (
          !item ||
          typeof item !== "string" ||
          item.trim() === "" ||
          item.toLowerCase() === "n/a"
        )
          return ""; // Don't render empty/NA items
        return `<li class="p-3 ${bgColor} rounded-lg text-sm ${textColor}">${item}</li>`;
      };

      // Helper function to generate skill tags
      const generateSkillTag = (skill: string | undefined | null): string => {
        if (
          !skill ||
          typeof skill !== "string" ||
          skill.trim() === "" ||
          skill.toLowerCase() === "n/a"
        )
          return "";
        return `<span class="inline-block px-3 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 text-xs font-medium rounded-full whitespace-nowrap">${skill}</span>`;
      };

      // Helper function for key-value pairs
      const generateKeyValueItem = (
        key: string,
        value: string | number | undefined | null,
        valueClasses: string = "text-gray-800 dark:text-white font-medium"
      ): string => {
        const displayValue = safeGet({ value }, "value"); // Use safeGet to handle undefined/null/empty
        if (displayValue === "N/A") return ""; // Don't render N/A values
        return `
                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center p-3 bg-gray-50 dark:bg-gray-800/60 rounded-lg">
                    <span class="text-sm text-gray-500 dark:text-gray-400 mb-1 sm:mb-0">${key}</span>
                    <span class="text-sm text-right sm:text-left ${valueClasses}">${displayValue}</span>
                </div>`;
      };

      // Helper function for ratings
      const generateRatingItem = (
        label: string,
        rating: number | string | undefined | null
      ): string => {
        const displayRating = safeGet({ rating }, "rating", 0); // Default to 0 if N/A
        if (displayRating === 0) return ""; // Don't render 0/5 ratings
        const ratingValue =
          typeof displayRating === "string"
            ? parseFloat(displayRating)
            : displayRating;
        let colorClass = "text-gray-600 dark:text-gray-400"; // Default color
        if (ratingValue >= 4) colorClass = "text-green-600 dark:text-green-400";
        else if (ratingValue >= 3)
          colorClass = "text-yellow-600 dark:text-yellow-400";
        else if (ratingValue > 0) colorClass = "text-red-600 dark:text-red-400";

        return `
                <div class="p-3 bg-gray-50 dark:bg-gray-800/60 rounded-lg text-center">
                    <p class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">${label}</p>
                    <p class="text-xl font-bold ${colorClass} mt-1">${ratingValue}/5</p>
                </div>`;
      };

      // Generate Sections conditionally
      const sections = {
        roleOverview: `
                <section>
                    <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700/50 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg>
                        Role Overview
                    </h3>
                    <div class="space-y-5">
                        ${
                          safeGet(
                            analysis,
                            "jobRoleSummary.keyResponsibilities",
                            []
                          ).length > 0
                            ? `
                        <div>
                            <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-2">Key Responsibilities</h4>
                            <ul class="space-y-2">
                                ${safeGet(
                                  analysis,
                                  "jobRoleSummary.keyResponsibilities",
                                  []
                                )
                                  .map((resp: string) => generateListItem(resp))
                                  .join("")}
                            </ul>
                        </div>`
                            : ""
                        }
                         ${
                           safeGet(
                             analysis,
                             "skillsAndQualifications.hardSkills",
                             []
                           ).length > 0
                             ? `
                        <div>
                            <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-2">Required Skills</h4>
                            <div class="flex flex-wrap gap-2">
                                ${safeGet(
                                  analysis,
                                  "skillsAndQualifications.hardSkills",
                                  []
                                )
                                  .map((skill: string) =>
                                    generateSkillTag(skill)
                                  )
                                  .join("")}
                            </div>
                        </div>`
                             : ""
                         }
                         ${generateKeyValueItem("Seniority", safeGet(analysis, "jobRoleSummary.seniorityLevel"))}
                         ${generateKeyValueItem("Work Mode", safeGet(analysis, "jobRoleSummary.workMode"))}
                         ${generateKeyValueItem("Experience", safeGet(analysis, "skillsAndQualifications.experienceRequired"))}
                    </div>
                </section>`,

        compensationBenefits: `
                <section>
                    <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700/50 flex items-center">
                       <svg class="w-5 h-5 mr-2 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h1a2 2 0 002-2v-2a2 2 0 00-2-2M7 16H5a2 2 0 01-2-2v-2a2 2 0 012-2h2m0 4v-4m12 4H9m4-8h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2v2a2 2 0 002 2z"></path></svg>
                       Compensation & Benefits
                    </h3>
                    <div class="space-y-5">
                         ${
                           safeGet(
                             analysis,
                             "compensationDetails.baseSalaryRange"
                           ) !== "N/A" ||
                           safeGet(
                             analysis,
                             "compensationDetails.totalCompensationRange"
                           ) !== "N/A"
                             ? `
                         <div>
                            <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-2">Salary Structure</h4>
                            <div class="space-y-2">
                                ${generateKeyValueItem("Base Salary", safeGet(analysis, "compensationDetails.baseSalaryRange"))}
                                ${generateKeyValueItem("Total Comp", safeGet(analysis, "compensationDetails.totalCompensationRange"))}
                                ${generateKeyValueItem("Industry Comparison", safeGet(analysis, "compensationDetails.industryComparison"), "text-gray-600 dark:text-gray-400 italic")}
                            </div>
                        </div>`
                             : ""
                         }
                        ${
                          safeGet(
                            analysis,
                            "compensationDetails.benefits.healthcare",
                            []
                          ).length > 0 ||
                          safeGet(
                            analysis,
                            "compensationDetails.benefits.retirement",
                            []
                          ).length > 0 ||
                          safeGet(
                            analysis,
                            "compensationDetails.benefits.additionalPerks",
                            []
                          ).length > 0
                            ? `
                        <div>
                            <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-2">Key Benefits</h4>
                            <ul class="space-y-2">
                                ${safeGet(
                                  analysis,
                                  "compensationDetails.benefits.healthcare",
                                  []
                                )
                                  .map((benefit: string) =>
                                    generateListItem(
                                      benefit,
                                      "bg-green-50 dark:bg-green-900/40",
                                      "text-green-800 dark:text-green-300"
                                    )
                                  )
                                  .join("")}
                                ${safeGet(
                                  analysis,
                                  "compensationDetails.benefits.retirement",
                                  []
                                )
                                  .map((benefit: string) =>
                                    generateListItem(
                                      benefit,
                                      "bg-indigo-50 dark:bg-indigo-900/40",
                                      "text-indigo-800 dark:text-indigo-300"
                                    )
                                  )
                                  .join("")}
                                ${safeGet(
                                  analysis,
                                  "compensationDetails.benefits.additionalPerks",
                                  []
                                )
                                  .map((benefit: string) =>
                                    generateListItem(
                                      benefit,
                                      "bg-purple-50 dark:bg-purple-900/40",
                                      "text-purple-800 dark:text-purple-300"
                                    )
                                  )
                                  .join("")}
                            </ul>
                        </div>`
                             : ""
                         }
                         ${
                           safeGet(
                             analysis,
                             "compensationDetails.equityDetails.type"
                           ) !== "N/A"
                             ? `
                         <div>
                            <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-2">Equity</h4>
                            <div class="space-y-2">
                                ${generateKeyValueItem("Type", safeGet(analysis, "compensationDetails.equityDetails.type"))}
                                ${generateKeyValueItem("Vesting", safeGet(analysis, "compensationDetails.equityDetails.vestingSchedule"))}
                                ${generateKeyValueItem("Approx. Value", safeGet(analysis, "compensationDetails.equityDetails.approximateValue"))}
                            </div>
                         </div>
                         `
                             : ""
                         }
                    </div>
                </section>`,

        applicationTips: `
                <section>
                    <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700/50 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path></svg>
                        Application Tips
                    </h3>
                    <div class="space-y-5">
                         ${
                           safeGet(analysis, "applicationTips.resumeAdvice", []).length > 0
                             ? `
                        <div>
                            <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-2">Resume / CV</h4>
                            <ul class="space-y-2">
                                ${safeGet(
                                  analysis,
                                  "applicationTips.resumeAdvice",
                                  []
                                )
                                  .map((tip: string) => generateListItem(tip))
                                  .join("")}
                            </ul>
                        </div>`
                             : ""
                         }
                         ${
                           safeGet(
                             analysis,
                             "applicationTips.interviewPrep.commonQuestions",
                             []
                           ).length > 0
                             ? `
                        <div>
                            <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-2">Potential Interview Questions</h4>
                             <ul class="space-y-2">
                                ${safeGet(
                                  analysis,
                                  "applicationTips.interviewPrep.commonQuestions",
                                  []
                                )
                                  .map((q: string) =>
                                    generateListItem(
                                      q,
                                      "bg-blue-50 dark:bg-blue-900/40",
                                      "text-blue-800 dark:text-blue-300"
                                    )
                                  )
                                  .join("")}
                            </ul>
                        </div>`
                             : ""
                         }
                         ${
                           safeGet(
                             analysis,
                             "applicationTips.coverLetterTips",
                             []
                           ).length > 0
                             ? `
                        <div>
                            <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-2">Cover Letter</h4>
                            <ul class="space-y-2">
                                ${safeGet(
                                  analysis,
                                  "applicationTips.coverLetterTips",
                                  []
                                )
                                  .map((tip: string) => generateListItem(tip))
                                  .join("")}
                            </ul>
                        </div>`
                             : ""
                         }
                    </div>
                </section>`,

        cultureInsights: `
                 <section>
                    <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700/50 flex items-center">
                       <svg class="w-5 h-5 mr-2 text-teal-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg>
                       Culture & Employee Insights
                    </h3>
                    <div class="space-y-6">
                        ${
                          safeGet(analysis, "workCulture.workSchedule") !==
                            "N/A" ||
                          safeGet(analysis, "workCulture.remotePolicy") !==
                            "N/A" ||
                          safeGet(analysis, "workCulture.teamDynamics") !==
                            "N/A"
                            ? `
                        <div>
                             <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-3">Work Environment</h4>
                             <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                 ${generateKeyValueItem("Work Schedule", safeGet(analysis, "workCulture.workSchedule"))}
                                 ${generateKeyValueItem("Remote Policy", safeGet(analysis, "workCulture.remotePolicy"))}
                                 ${generateKeyValueItem("Team Dynamics", safeGet(analysis, "workCulture.teamDynamics"))}
                                 ${generateKeyValueItem("Management Style", safeGet(analysis, "workCulture.managementStyle"))}
                             </div>
                        </div>`
                            : ""
                        }
                        ${
                          safeGet(
                            analysis,
                            "companyInsights.employeeStats.ratings.overall",
                            0
                          ) > 0
                            ? `
                        <div>
                            <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-3">Company Ratings (Est.)</h4>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                ${generateRatingItem("Overall", safeGet(analysis, "companyInsights.employeeStats.ratings.overall"))}
                                ${generateRatingItem("Work/Life", safeGet(analysis, "companyInsights.employeeStats.ratings.workLifeBalance"))}
                                ${generateRatingItem("Growth", safeGet(analysis, "companyInsights.employeeStats.ratings.careerGrowth"))}
                                ${generateRatingItem("Culture", safeGet(analysis, "companyInsights.employeeStats.ratings.culture"))}
                            </div>
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center md:text-left">Ratings are estimated based on available data.</p>
                        </div>`
                            : ""
                        }
                        ${
                          safeGet(
                            analysis,
                            "companyInsights.employeeStats.averageTenure"
                          ) !== "N/A" ||
                          safeGet(
                            analysis,
                            "companyInsights.employeeStats.turnoverRate"
                          ) !== "N/A"
                            ? `
                         <div>
                            <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-3">Key Employee Metrics (Est.)</h4>
                            <div class="space-y-2">
                                ${generateKeyValueItem("Avg. Tenure", safeGet(analysis, "companyInsights.employeeStats.averageTenure"))}
                                ${generateKeyValueItem("Turnover Rate", safeGet(analysis, "companyInsights.employeeStats.turnoverRate"))}
                                ${generateKeyValueItem("Promotion Rate", safeGet(analysis, "companyInsights.employeeStats.promotionRate"))}
                            </div>
                        </div>`
                            : ""
                        }
                         ${
                           safeGet(analysis, "workCulture.redFlags", [])
                             .length > 0 ||
                           safeGet(
                             analysis,
                             "workCulture.positiveIndicators",
                             []
                           ).length > 0
                             ? `
                         <div class="grid grid-cols-1 sm:grid-cols-2 gap-5">
                            ${
                              safeGet(analysis, "workCulture.redFlags", [])
                                .length > 0
                                ? `
                            <div>
                                <h4 class="text-sm uppercase tracking-wider font-medium text-red-600 dark:text-red-400 mb-2 flex items-center">
                                    <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 01-1-1V6z" clip-rule="evenodd"></path></svg>
                                    Potential Red Flags
                                </h4>
                                <ul class="space-y-2">
                                     ${safeGet(
                                       analysis,
                                       "workCulture.redFlags",
                                       []
                                     )
                                       .map((flag: string) =>
                                         generateListItem(
                                           flag,
                                           "bg-red-50 dark:bg-red-900/50",
                                           "text-red-800 dark:text-red-300"
                                         )
                                       )
                                       .join("")}
                                </ul>
                            </div>`
                                : "<div></div>" /* Placeholder for grid alignment */
                            }
                             ${
                               safeGet(
                                 analysis,
                                 "workCulture.positiveIndicators",
                                 []
                               ).length > 0
                                 ? `
                            <div>
                                <h4 class="text-sm uppercase tracking-wider font-medium text-green-600 dark:text-green-400 mb-2 flex items-center">
                                    <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 01-1-1V6z" clip-rule="evenodd" transform="rotate(180 10 9.5)"></path></svg>
                                    Potential Green Flags
                                </h4>
                                 <ul class="space-y-2">
                                    ${safeGet(
                                      analysis,
                                      "workCulture.positiveIndicators",
                                      []
                                    )
                                      .map((indicator: string) =>
                                        generateListItem(
                                          indicator,
                                          "bg-green-50 dark:bg-green-900/50",
                                          "text-green-800 dark:text-green-300"
                                        )
                                      )
                                      .join("")}
                                </ul>
                            </div>`
                                 : ""
                             }
                        </div>`
                             : ""
                         }
                    </div>
                </section>`,

        marketCareer: `
                <section>
                     <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700/50 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path></svg>
                        Market & Career Path
                    </h3>
                     <div class="space-y-6">
                         ${
                           safeGet(
                             analysis,
                             "jobMarketTrends.currentDemand"
                           ) !== "N/A" ||
                           safeGet(
                             analysis,
                             "jobMarketTrends.growthProjection"
                           ) !== "N/A" ||
                           safeGet(
                             analysis,
                             "jobMarketTrends.industryTrends",
                             []
                           ).length > 0
                             ? `
                         <div>
                            <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-3">Market Trends</h4>
                            <div class="space-y-2">
                                ${generateKeyValueItem("Demand Level", safeGet(analysis, "jobMarketTrends.currentDemand"))}
                                ${generateKeyValueItem("Growth Projection", safeGet(analysis, "jobMarketTrends.growthProjection"))}
                                ${safeGet(analysis, "jobMarketTrends.industryTrends", []).length > 0 ? generateListItem(`Key Trends: ${safeGet(analysis, "jobMarketTrends.industryTrends", []).join(", ")}`) : ""}
                                ${generateListItem(`Salary Insights: ${safeGet(analysis, "jobMarketTrends.salaryTrends")}`)}
                                 ${
                                   safeGet(
                                     analysis,
                                     "jobMarketTrends.skillsInDemand",
                                     []
                                   ).length > 0
                                     ? `
                                <div class="p-3 bg-gray-50 dark:bg-gray-800/60 rounded-lg">
                                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">Related In-Demand Skills</p>
                                    <div class="flex flex-wrap gap-1.5">
                                        ${safeGet(
                                          analysis,
                                          "jobMarketTrends.skillsInDemand",
                                          []
                                        )
                                          .map((skill: string) =>
                                            generateSkillTag(skill)
                                          )
                                          .join("")}
                                    </div>
                                </div>`
                                     : ""
                                 }
                            </div>
                        </div>`
                             : ""
                         }
                        ${
                          safeGet(analysis, "careerGrowth.promotionPath", [])
                            .length > 0 ||
                          safeGet(analysis, "careerGrowth.learningBudget") !==
                            "N/A"
                            ? `
                        <div>
                            <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-3">Career Growth</h4>
                             <ul class="space-y-2">
                                ${safeGet(
                                  analysis,
                                  "careerGrowth.promotionPath",
                                  []
                                )
                                  .map((path: string) => generateListItem(path))
                                  .join("")}
                                ${generateKeyValueItem("Learning Budget", safeGet(analysis, "careerGrowth.learningBudget"))}
                                ${generateListItem(`Mentorship: ${Array.isArray(safeGet(analysis, "careerGrowth.mentorshipPrograms", "N/A")) ? safeGet(analysis, "careerGrowth.mentorshipPrograms", []).join(", ") : safeGet(analysis, "careerGrowth.mentorshipPrograms")}`)}
                                ${safeGet(analysis, "careerGrowth.skillDevelopmentOpportunities", []).length > 0 ? generateListItem(`Skill Dev: ${safeGet(analysis, "careerGrowth.skillDevelopmentOpportunities", []).join(", ")}`) : ""}
                            </ul>
                        </div>`
                            : ""
                        }
                         ${
                           safeGet(analysis, "companyInsights.competitors", [])
                             .length > 0
                             ? `
                         <div>
                            <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-3">Key Competitors</h4>
                            <ul class="space-y-2">
                                ${safeGet(
                                  analysis,
                                  "companyInsights.competitors",
                                  []
                                )
                                  .map((comp: string) => generateListItem(comp))
                                  .join("")}
                            </ul>
                        </div>`
                             : ""
                         }
                        ${
                          safeGet(
                            analysis,
                            "companyInsights.fundingDetails.stage"
                          ) !== "N/A" ||
                          safeGet(
                            analysis,
                            "companyInsights.fundingDetails.keyInvestors",
                            []
                          ).length > 0
                            ? `
                        <div>
                             <h4 class="text-sm uppercase tracking-wider font-medium text-gray-600 dark:text-gray-400 mb-3">Company Funding</h4>
                             <div class="space-y-2">
                                 ${generateKeyValueItem("Stage", safeGet(analysis, "companyInsights.fundingDetails.stage"))}
                                 ${generateKeyValueItem("Total Raised", safeGet(analysis, "companyInsights.fundingDetails.totalRaised"))}
                                 ${generateKeyValueItem("Last Round", safeGet(analysis, "companyInsights.fundingDetails.lastRound"))}
                                 ${safeGet(analysis, "companyInsights.fundingDetails.keyInvestors", []).length > 0 ? generateListItem(`Investors: ${safeGet(analysis, "companyInsights.fundingDetails.keyInvestors", []).join(", ")}`) : ""}
                            </div>
                        </div>`
                            : ""
                        }
                     </div>
                </section>`,
      };

      // Determine which sections have content
      const hasContent = (sectionHTML: string) =>
        sectionHTML.includes("<li") ||
        sectionHTML.includes("<span") ||
        sectionHTML.includes('<div class="flex'); // Basic check if any data was rendered

      const leftColumnContent = [
        sections.roleOverview,
        sections.compensationBenefits,
        sections.applicationTips,
      ]
        .filter(hasContent)
        .join(
          '<div class="my-6 border-t border-dashed border-gray-200 dark:border-gray-700/50"></div>'
        );
      const rightColumnContent = [
        sections.cultureInsights,
        sections.marketCareer,
      ]
        .filter(hasContent)
        .join(
          '<div class="my-6 border-t border-dashed border-gray-200 dark:border-gray-700/50"></div>'
        );

      // If no content in either column, show a message
      if (!leftColumnContent && !rightColumnContent) {
        return createErrorHTML(
          "Could not extract detailed information. The job description might be too short or lack specific details."
        );
      }

      return `
        <div class="max-w-7xl mx-auto p-0 md:p-4 lg:p-6 typing-animation">
            <div class="bg-white/95 dark:bg-gray-800/80 backdrop-blur-lg p-6 sm:p-8 rounded-3xl shadow-xl border border-gray-200 dark:border-gray-700/50">

                <!-- Section Title -->
                <h2 class="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-3 text-center">
                    ${safeGet(analysis, "jobRoleSummary.title", "the Role")}
                </h2>
                <p class="text-center text-gray-600 dark:text-gray-400 mb-8 text-md">
                    At ${safeGet(analysis, "companyInsights.name", "the Company")}
                </p>

                <!-- Key Metrics Summary (Top Section) -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 border-b border-gray-200 dark:border-gray-700/50 pb-8">
                    <div class="bg-gradient-to-br from-emerald-50 dark:from-emerald-900/40 to-white dark:to-gray-800/40 p-5 rounded-xl border border-emerald-200 dark:border-emerald-800/50 text-left shadow-sm">
                        <dt class="text-xs font-semibold text-emerald-800 dark:text-emerald-300 uppercase tracking-wider">Total Compensation (Est.)</dt>
                        <dd class="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
                            ${safeGet(analysis, "compensationDetails.totalCompensationRange")}
                        </dd>
                        <p class="mt-1 text-xs text-gray-600 dark:text-gray-400">${safeGet(analysis, "compensationDetails.industryComparison")}</p>
                    </div>
                    <div class="bg-gradient-to-br from-sky-50 dark:from-sky-900/40 to-white dark:to-gray-800/40 p-5 rounded-xl border border-sky-200 dark:border-sky-800/50 text-left shadow-sm">
                        <dt class="text-xs font-semibold text-sky-800 dark:text-sky-300 uppercase tracking-wider">Company Insight</dt>
                        <dd class="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
                            ${safeGet(analysis, "companyInsights.fundingDetails.stage")}
                        </dd>
                        <p class="mt-1 text-xs text-gray-600 dark:text-gray-400">
                            Last round: ${safeGet(analysis, "companyInsights.fundingDetails.lastRound")}
                        </p>
                    </div>
                    <div class="bg-gradient-to-br from-indigo-50 dark:from-indigo-900/40 to-white dark:to-gray-800/40 p-5 rounded-xl border border-indigo-200 dark:border-indigo-800/50 text-left shadow-sm">
                        <dt class="text-xs font-semibold text-indigo-800 dark:text-indigo-300 uppercase tracking-wider">Est. Growth Metrics</dt>
                        <dd class="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
                             ${safeGet(analysis, "companyInsights.growth.userGrowth")}
                        </dd>
                         <p class="mt-1 text-xs text-gray-600 dark:text-gray-400">Revenue: ${safeGet(analysis, "companyInsights.growth.revenueRange")}</p>
                    </div>
                </div>

                <!-- Main Content Grid (Two Columns on Large Screens) -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-x-10 gap-y-8">

                    <!-- Column 1 -->
                    <div class="space-y-8">
                        ${leftColumnContent || '<p class="text-gray-500 dark:text-gray-400">No details available for this section.</p>'}
                    </div>

                    <!-- Column 2 -->
                    <div class="space-y-8">
                         ${rightColumnContent || '<p class="text-gray-500 dark:text-gray-400">No details available for this section.</p>'}
                    </div>
                </div>

                <!-- Disclaimer -->
                <div class="mt-10 pt-6 border-t border-gray-200 dark:border-gray-700/50">
                    <p class="text-xs text-center text-gray-500 dark:text-gray-500 leading-relaxed">
                        <strong class="font-medium">Disclaimer:</strong> This analysis is AI-generated based on the provided job description and publicly available data. Information like salary ranges, company culture, and ratings are estimates and may not be fully accurate or exhaustive. Always conduct your own thorough research and due diligence. Verify details directly with the hiring company.
                    </p>
                </div>

            </div>
        </div>
        <!-- Font Awesome script (if using icons). Add this to your main layout or here. -->
      `;
    }

    // --- Function to create standardized error HTML ---
    function createErrorHTML(message: string, details?: string): string {
      return `
            <div class="max-w-3xl mx-auto bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800/50 p-6 rounded-2xl shadow-md text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/50 mb-4">
                  <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg>
                </div>
                <h2 class="text-xl font-semibold mb-3 text-red-800 dark:text-red-300">
                    Analysis Error
                </h2>
                <p class="text-red-700 dark:text-red-400 mb-4 text-sm">
                    ${message} Please try again.
                </p>
                ${
                  details
                    ? `
                <details class="text-left text-xs text-red-600 dark:text-red-400/80 max-w-full mx-auto mt-4">
                    <summary class="cursor-pointer font-medium hover:text-red-800 dark:hover:text-red-200">Technical Details</summary>
                    <pre class="mt-2 p-3 bg-red-100 dark:bg-red-900/40 rounded-lg overflow-x-auto whitespace-pre-wrap break-words"><code>${details}</code></pre>
                </details>
                `
                    : ""
                }
            </div>
        `;
    }

    // --- Clear Button Logic ---
    const clearButton = document.getElementById("clearForm");

    if (clearButton && form && analysisResultContainer) {
      clearButton.addEventListener("click", () => {
        // Get form inputs
        const companyNameInput = document.getElementById("companyName") as HTMLInputElement | null;
        const jobPositionInput = document.getElementById("jobPosition") as HTMLInputElement | null;

        // Clear form inputs
        if (companyNameInput) companyNameInput.value = "";
        if (jobPositionInput) jobPositionInput.value = "";

        // Clear any validation errors
        const inputs = [companyNameInput, jobPositionInput].filter(Boolean) as (HTMLInputElement | HTMLTextAreaElement)[];
        inputs.forEach(input => FormValidator.clearError(input));

        // Hide and clear results
        analysisResultContainer.classList.add("hidden");
        analysisResultContainer.innerHTML = ""; // Clear content

        // Reset state
        currentAnalysis = null;
        form.classList.remove(":user-invalid");

        // Scroll back to the top
        form.scrollIntoView({ behavior: "smooth", block: "start" });
      });
    }
  }); // End DOMContentLoaded
</script>
