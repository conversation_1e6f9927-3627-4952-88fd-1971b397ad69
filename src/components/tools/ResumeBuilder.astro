---
import ResumeForm from "../resume-builder/ResumeForm.astro";
import ResumePreview from "../resume-builder/ResumePreview.astro";
import ResumeCustomizer from "../resume-builder/ResumeCustomizer.astro";
---

<div class="relative w-full max-w-screen px-6 md:px-16 mx-auto">
  <!-- Progress Indicator -->
  <div class="mb-8">
    <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
      <span>Build Your Resume</span>
      <span id="progress-text">Step 1 of 5</span>
    </div>
    <div class="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
      <div id="progress-bar" class="bg-indigo-600 h-2 rounded-full transition-all duration-300" style="width: 20%"></div>
    </div>
  </div>

  <div id="resume-builder-container" class="flex flex-col xl:flex-row gap-6 items-start">
    <!-- Form Section -->
    <div id="resume-form-container" class="w-full xl:w-2/5 transition-all duration-500 ease-in-out">
      <div class="space-y-6">
        <!-- Quick Upload Section -->
        <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg p-6">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Quick Start</h3>
          <div class="space-y-4">
            <div>
              <label for="resume-upload" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Upload Existing Resume (Optional)
              </label>
              <div class="flex justify-center px-6 py-4 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg hover:border-indigo-400 dark:hover:border-indigo-500 transition-colors">
                <div class="text-center">
                  <svg class="mx-auto h-8 w-8 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                  <div class="mt-2">
                    <label for="resume-upload-input" class="cursor-pointer text-sm font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500">
                      <span>Upload file</span>
                      <input id="resume-upload-input" name="resume-upload" type="file" class="sr-only" accept=".doc,.docx,.pdf">
                    </label>
                    <span class="text-sm text-gray-500 dark:text-gray-400"> or drag and drop</span>
                  </div>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    PDF, DOC, DOCX up to 10MB
                  </p>
                </div>
              </div>
            </div>
            <div class="text-center">
              <span class="text-sm text-gray-500 dark:text-gray-400">or</span>
            </div>
            <button id="start-fresh-btn" class="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors font-medium">
              Start Fresh
            </button>
          </div>
        </div>

        <!-- Form Sections -->
        <ResumeForm />
      </div>
    </div>

    <!-- Preview and Customization Section -->
    <div id="resume-preview-wrapper" class="w-full xl:w-3/5 transition-all duration-500 ease-in-out">
      <div id="resume-preview-flex-container" class="sticky top-24 flex gap-4">
        <!-- Preview Content -->
        <div id="resume-preview-content-wrapper" class="flex-1 transition-all duration-500 ease-in-out">
          <ResumePreview />
        </div>

        <!-- Customization Panel -->
        <div id="resume-customizer-container" class="customizer-panel transition-all duration-500 ease-in-out">
          <ResumeCustomizer />
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Backdrop -->
  <div id="mobile-backdrop" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden transition-opacity duration-300"></div>
  </div>
</div>

<script>
  import { setResumeData } from '../../lib/resumeBuilderService';

  document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('resume-builder-container');
    const formContainer = document.getElementById('resume-form-container');
    const previewWrapper = document.getElementById('resume-preview-wrapper');
    const customizeBtn = document.getElementById('customize-btn');
    const customizerContainer = document.getElementById('resume-customizer-container');
    const backToEditBtn = document.getElementById('back-to-edit-btn');
    const startFreshBtn = document.getElementById('start-fresh-btn');
    const fileInput = document.getElementById('resume-upload-input') as HTMLInputElement;
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const mobileBackdrop = document.getElementById('mobile-backdrop');

    if (!container || !formContainer || !previewWrapper || !customizeBtn || !customizerContainer || !backToEditBtn) return;

    // Progress tracking
    const sections = ['contactInfo', 'summary', 'workExperience', 'education', 'skills'];
    let completedSections = 0;

    const updateProgress = () => {
      const percentage = Math.round((completedSections / sections.length) * 100);
      if (progressBar) progressBar.style.width = `${percentage}%`;
      if (progressText) progressText.textContent = `Step ${completedSections + 1} of ${sections.length}`;
    };

    // Check if we're on mobile/tablet
    const isMobile = () => window.innerWidth < 1280;

    // Customization panel toggle
    customizeBtn.addEventListener('click', () => {
      // Hide form container smoothly
      formContainer.classList.add('form-hidden');
      // Open customization panel
      customizerContainer.classList.add('customizer-open');
      // Expand preview wrapper to full width
      previewWrapper.classList.add('preview-expanded');

      // Force width update for better expansion
      setTimeout(() => {
        if (!isMobile()) {
          previewWrapper.style.width = '100%';
          const flexContainer = document.getElementById('resume-preview-flex-container');
          if (flexContainer) {
            flexContainer.style.width = '100%';
          }
        }
      }, 100);

      // Show mobile backdrop if on mobile
      if (isMobile() && mobileBackdrop) {
        mobileBackdrop.classList.remove('hidden');
        mobileBackdrop.classList.add('opacity-100');
      }
      // Toggle buttons
      customizeBtn.classList.add('hidden');
      backToEditBtn.classList.remove('hidden');
    });

    const closeCustomizer = () => {
      // Show form container
      formContainer.classList.remove('form-hidden');
      // Close customization panel
      customizerContainer.classList.remove('customizer-open');
      // Reset preview wrapper width
      previewWrapper.classList.remove('preview-expanded');

      // Reset inline styles
      previewWrapper.style.width = '';
      const flexContainer = document.getElementById('resume-preview-flex-container');
      if (flexContainer) {
        flexContainer.style.width = '';
      }

      // Hide mobile backdrop
      if (mobileBackdrop) {
        mobileBackdrop.classList.remove('opacity-100');
        setTimeout(() => mobileBackdrop.classList.add('hidden'), 300);
      }
      // Toggle buttons
      customizeBtn.classList.remove('hidden');
      backToEditBtn.classList.add('hidden');
    };

    backToEditBtn.addEventListener('click', closeCustomizer);

    // Close customizer when clicking backdrop on mobile
    if (mobileBackdrop) {
      mobileBackdrop.addEventListener('click', closeCustomizer);
    }

    // Close customizer when clicking the close button in the panel
    document.addEventListener('click', (e) => {
      if ((e.target as HTMLElement)?.id === 'close-customizer-btn' ||
          (e.target as HTMLElement)?.closest('#close-customizer-btn')) {
        closeCustomizer();
      }
    });

    // Handle window resize
    window.addEventListener('resize', () => {
      if (!isMobile() && mobileBackdrop) {
        mobileBackdrop.classList.remove('opacity-100');
        mobileBackdrop.classList.add('hidden');
      }
    });

    // Start fresh functionality
    if (startFreshBtn) {
      startFreshBtn.addEventListener('click', () => {
        // Clear any uploaded data and focus on first form section
        const firstAccordion = document.querySelector('.accordion-button') as HTMLButtonElement;
        if (firstAccordion && firstAccordion.classList.contains('collapsed')) {
          firstAccordion.click();
        }

        // Scroll to form
        formContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
      });
    }

    // File upload handling
    if (fileInput) {
      fileInput.addEventListener('change', async (event) => {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];

        if (!file) return;

        const formData = new FormData();
        formData.append('resume', file);

        try {
          const response = await fetch('/.netlify/functions/parse-uploaded-resume', {
            method: 'POST',
            body: formData,
          });

          if (!response.ok) {
            throw new Error('Failed to parse resume');
          }

          const data = await response.json();
          setResumeData(data);

          // Update progress based on filled data
          completedSections = sections.filter(section => {
            const sectionData = data[section];
            return sectionData && (
              typeof sectionData === 'string' ? sectionData.trim() :
              Array.isArray(sectionData) ? sectionData.length > 0 :
              Object.keys(sectionData).some(key => sectionData[key])
            );
          }).length;
          updateProgress();

        } catch (error) {
          console.error('Error uploading resume:', error);
        }
      });
    }

    // Initialize progress
    updateProgress();
  });
</script>

<style>
  .max-h-screen {
    max-height: 100vh;
  }

  /* Form hiding animation */
  #resume-form-container {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(0);
    opacity: 1;
    flex-shrink: 0;
  }

  #resume-form-container.form-hidden {
    transform: translateX(-100%);
    opacity: 0;
    pointer-events: none;
    width: 0 !important;
    min-width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Preview expansion */
  #resume-preview-wrapper {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* When preview is expanded, take full container width */
  #resume-preview-wrapper.preview-expanded {
    width: 100% !important;
    flex: 1 !important;
  }

  /* Ensure the flex container also expands */
  #resume-preview-wrapper.preview-expanded #resume-preview-flex-container {
    width: 100% !important;
  }

  /* Customization Panel Styles */
  .customizer-panel {
    width: 0;
    opacity: 0;
    pointer-events: none;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .customizer-panel.customizer-open {
    opacity: 1;
    pointer-events: auto;
  }

  /* Mobile backdrop */
  #mobile-backdrop {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }

  #mobile-backdrop.opacity-100 {
    opacity: 1;
  }

  /* Responsive adjustments */
  @media (max-width: 1279px) {
    #resume-form-container.form-hidden {
      transform: translateY(-100%);
    }

    .customizer-panel.customizer-open {
      position: fixed;
      top: 0;
      right: 0;
      height: 100vh;
      width: 90vw;
      max-width: 420px;
      z-index: 50;
      background: white;
      box-shadow: -4px 0 6px -1px rgb(0 0 0 / 0.1);
      border-left: 1px solid rgb(229 231 235);
    }

    .dark .customizer-panel.customizer-open {
      background: rgb(31 41 55);
      border-left-color: rgb(75 85 99);
    }

    /* Better mobile preview sizing */
    #resume-preview-wrapper.preview-expanded {
      width: 100% !important;
    }

    #resume-preview-wrapper.preview-expanded #resume-preview-content-wrapper {
      max-width: 100%;
      padding-right: 0;
    }
  }

  @media (min-width: 1280px) {
    .customizer-panel.customizer-open {
      width: 380px;
    }

    #resume-preview-content-wrapper {
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* When form is hidden and customizer is open */
    #resume-builder-container:has(.form-hidden) #resume-preview-wrapper.preview-expanded {
      width: 100% !important;
    }

    #resume-builder-container:has(.form-hidden) #resume-preview-flex-container {
      width: 100%;
    }

    #resume-builder-container:has(.form-hidden) #resume-preview-content-wrapper {
      flex: 1;
      max-width: calc(100% - 400px);
      margin-right: 1rem;
    }

    /* Fallback for browsers that don't support :has() */
    .form-hidden ~ #resume-preview-wrapper.preview-expanded {
      width: 100% !important;
    }

    .form-hidden ~ #resume-preview-wrapper.preview-expanded #resume-preview-flex-container {
      width: 100%;
    }

    .form-hidden ~ #resume-preview-wrapper.preview-expanded #resume-preview-content-wrapper {
      flex: 1;
      max-width: calc(100% - 400px);
      margin-right: 1rem;
    }
  }

  /* Form section improvements */
  #resume-form-container {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }

  #resume-form-container::-webkit-scrollbar {
    width: 6px;
  }

  #resume-form-container::-webkit-scrollbar-track {
    background: transparent;
  }

  #resume-form-container::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }

  /* Progress bar animation */
  #progress-bar {
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Smooth transitions for layout changes */
  #resume-builder-container {
    transition: all 0.3s ease-in-out;
  }

  /* Mobile optimizations */
  @media (max-width: 1279px) {
    #resume-builder-container {
      flex-direction: column;
    }

    #resume-form-container,
    #resume-preview-wrapper {
      width: 100%;
    }

    #resume-preview-flex-container {
      position: relative;
      top: auto;
    }
  }
</style>