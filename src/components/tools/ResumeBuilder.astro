---
import ResumeForm from "../resume-builder/ResumeForm.astro";
import ResumePreview from "../resume-builder/ResumePreview.astro";
import ResumeCustomizer from "../resume-builder/ResumeCustomizer.astro";
---

<div class="relative w-full max-w-screen px-6 md:px-16 mx-auto">
  <div id="resume-builder-container" class="flex flex-col lg:flex-row gap-8 items-start">
    <div id="resume-form-container" class="w-full lg:w-1/2 transition-all duration-500 ease-in-out">
          <div class="space-y-8">
            <div>
              <label for="resume-upload" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Upload Existing Resume
              </label>
              <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md">
                <div class="space-y-1 text-center">
                  <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                  <div class="flex text-sm text-gray-600 dark:text-gray-400">
                    <label for="resume-upload-input" class="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
                      <span>Upload a file</span>
                      <input id="resume-upload-input" name="resume-upload" type="file" class="sr-only" accept=".doc,.docx,.pdf">
                    </label>
                    <p class="pl-1">or drag and drop</p>
                  </div>
                  
                  <script>
                    import { setResumeData } from '../../lib/resumeBuilderService';
                  
                    const fileInput = document.getElementById('resume-upload-input') as HTMLInputElement;
                  
                    fileInput.addEventListener('change', async (event) => {
                      const target = event.target as HTMLInputElement;
                      const file = target.files?.[0];
                  
                      if (!file) {
                        return;
                      }
                  
                      const formData = new FormData();
                      formData.append('resume', file);
                  
                      try {
                        const response = await fetch('/.netlify/functions/parse-uploaded-resume', {
                          method: 'POST',
                          body: formData,
                        });
                  
                        if (!response.ok) {
                          throw new Error('Failed to parse resume');
                        }
                  
                        const data = await response.json();
                        setResumeData(data);
                      } catch (error) {
                        console.error('Error uploading resume:', error);
                        // Optionally, show an error message to the user
                      }
                    });
                  </script>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    PDF, DOC, DOCX up to 10MB
                  </p>
                </div>
              </div>
            </div>
            <ResumeForm />
          </div>
        </div>
    <div id="resume-preview-wrapper" class="w-full lg:w-1/2 transition-all duration-500 ease-in-out">
      <div id="resume-preview-flex-container" class="sticky top-24 flex">
        <div id="resume-preview-content-wrapper" class="w-full transition-all duration-500 ease-in-out">
          <ResumePreview />
        </div>
        <div id="resume-customizer-container" class="customizer-initial-hidden transition-all duration-500 ease-in-out">
          <ResumeCustomizer />
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('resume-builder-container');
    const formContainer = document.getElementById('resume-form-container');
    const previewWrapper = document.getElementById('resume-preview-wrapper');
    const customizeBtn = document.getElementById('customize-btn');
    const customizerContainer = document.getElementById('resume-customizer-container');
    const backToEditBtn = document.getElementById('back-to-edit-btn');

    if (!container || !formContainer || !previewWrapper || !customizeBtn || !customizerContainer || !backToEditBtn) return;

    customizeBtn.addEventListener('click', () => {
      container.classList.add('preview-focused');
      customizeBtn.classList.add('hidden');
      backToEditBtn.classList.remove('hidden');
    });

    backToEditBtn.addEventListener('click', () => {
      container.classList.remove('preview-focused');
      customizeBtn.classList.remove('hidden');
      backToEditBtn.classList.add('hidden');
    });
  });
</script>

<style>
  .max-h-screen {
    max-height: 100vh;
  }
  .customizer-initial-hidden {
    width: 0;
    opacity: 0;
    pointer-events: none;
  }
  @media (min-width: 1024px) {
    #resume-builder-container.preview-focused #resume-form-container {
      width: 0;
      opacity: 0;
      transform: translateX(100%); /* Slide out to the right */
      pointer-events: none;
    }
    #resume-builder-container.preview-focused #resume-preview-wrapper {
      width: 100%; /* Take full width */
    }
    #resume-builder-container.preview-focused #resume-preview-flex-container {
      gap: 1rem; /* Add padding between preview and customizer */
    }
    #resume-builder-container.preview-focused #resume-preview-content-wrapper #resume-preview-content {
      max-width: 100%; /* Allow it to shrink within its 50% parent */
    }
    #resume-builder-container.preview-focused #resume-preview-content-wrapper {
      width: 50%; /* Preview takes 50% */
      opacity: 1;
      pointer-events: auto;
    }
    #resume-builder-container.preview-focused #resume-customizer-container {
      width: 50%; /* Customizer takes 50% */
      opacity: 1;
      pointer-events: auto;
    }
  }
</style>