import { GoogleGenerativeAI } from "@google/generative-ai";

export const fetchInterviewQuestions = async ({
  request,
}: {
  request: Request;
}) => {
  try {
    let requestData;
    try {
      requestData = await request.json();
    } catch (parseError) {
      throw new Error("Invalid request format: Unable to parse request body");
    }

    const {
      companyName = "Unknown Company",
      jobRole = "Unknown Role",
      resume = "",
      category = "", // Optional parameter to request a specific category
    } = requestData;

    if (!companyName || typeof companyName !== "string") {
      throw new Error("Company name is required and must be a string");
    }

    if (!jobRole || typeof jobRole !== "string") {
      throw new Error("Job role is required and must be a string");
    }

    // Use Gemini 2.5 Flash for faster responses
    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY || "");

    // Check if this is a "load more" request to optimize token budget
    const isLoadMore = category && requestData.loadMore === true;

    const model = genAI.getGenerativeModel({
      model: "gemini-2.0-flash",
      generationConfig: {
        temperature: 0.5,
        maxOutputTokens: isLoadMore ? 1000 : 2000, // Reduce token budget for load more requests
        responseMimeType: "application/json",
      },
      // Set thinking_budget to 0 for faster responses
      systemInstruction: {
        thinking_budget: 0,
      } as any, // Using any to bypass type checking for thinking_budget
      // Enable Google Search as a tool
      tools: [{
        googleSearch: {}
      }] as any,
    });

    // If a specific category is requested, generate only that category
    if (category && ["companySpecific", "roleSpecific", "behavioral", "technical"].includes(category)) {
      // Get existing questions if this is a load more request
      const existingQuestions = requestData.existingQuestions || [];

      // Determine if this is a follow-up request with no resume
      const isFollowUpWithoutResume = isLoadMore && !resume.trim();

      const categoryPrompt = `You are an expert interview coach with deep knowledge of industry-specific interview questions.
        CRITICAL INSTRUCTIONS:
        1. Output ONLY a valid JSON object
        2. NO explanatory text or reasoning
        3. NO markdown
        4. Keep analysis concise
        5. ALWAYS USE GOOGLE SEARCH to find accurate, up-to-date information about the company, industry standards, and typical interview questions
        6. Focus on providing specific, actionable interview questions
        7. Provide 5 ${isLoadMore ? 'ADDITIONAL' : ''} questions for the ${category} category
        8. ${isLoadMore ? 'IMPORTANT: Generate completely DIFFERENT questions than these existing ones: ' + JSON.stringify(existingQuestions) : ''}
        9. Match this structure exactly: ${JSON.stringify({
          [category]: [
            {
              question: "",
              context: "",
            },
          ],
        })}

        I have an upcoming interview at ${companyName} for the role of ${jobRole}.
        Please research and provide me with likely interview questions in the ${category} category.

        IMPORTANT: First use Google Search to research about ${companyName}, their culture, recent news, and typical interview questions for ${jobRole} positions at this company.

        ${isFollowUpWithoutResume ?
          `This is a follow-up request for more questions. Focus on industry standards and best practices for ${jobRole} positions.` :
          `Here is my resume for context:
          ${resume}`
        }`;

      const result = await model.generateContent(categoryPrompt);
      const response = result.response;
      const content = response.text();

      if (!content) {
        throw new Error("API response content is empty");
      }

      // Parse the JSON response
      let parsedContent;
      try {
        parsedContent = JSON.parse(content);
      } catch (parseError) {
        console.error('Error parsing API response:', parseError);
        console.debug('API response content:', content);
        throw new Error("Failed to parse API response");
      }

      return {
        statusCode: 200,
        body: JSON.stringify(parsedContent),
      };
    } else {
      // Generate company-specific questions first for immediate display
      const companySpecificPrompt = `You are an expert interview coach with deep knowledge of industry-specific interview questions.
        CRITICAL INSTRUCTIONS:
        1. Output ONLY a valid JSON object
        2. NO explanatory text or reasoning
        3. NO markdown
        4. Keep analysis concise
        5. ALWAYS USE GOOGLE SEARCH to find accurate, up-to-date information about the company, industry standards, and typical interview questions
        6. Focus on providing specific, actionable interview questions
        7. Provide 5 questions for the companySpecific category
        8. Match this structure exactly: ${JSON.stringify({
          companySpecific: [
            {
              question: "",
              context: "",
            },
          ],
        })}

        I have an upcoming interview at ${companyName} for the role of ${jobRole}.
        Please research and provide me with likely interview questions specific to this company.

        IMPORTANT: First use Google Search to research about ${companyName}, their culture, recent news, products, services, and typical interview questions at this company.

        Here is my resume for context:
        ${resume}`;

      const result = await model.generateContent(companySpecificPrompt);
      const response = result.response;
      const content = response.text();

      if (!content) {
        throw new Error("API response content is empty");
      }

      // Parse the JSON response
      let parsedContent;
      try {
        parsedContent = JSON.parse(content);
      } catch (parseError) {
        console.error('Error parsing API response:', parseError);
        console.debug('API response content:', content);
        throw new Error("Failed to parse API response");
      }

      return {
        statusCode: 200,
        body: JSON.stringify({
          ...parsedContent,
          _partial: true, // Indicate this is a partial response
          _nextCategory: "roleSpecific" // Indicate which category to fetch next
        }),
      };
    }
  } catch (error: any) {
    console.error("Error in fetchInterviewQuestions:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: error.message || "An unknown error occurred",
      }),
    };
  }
};

export const reviewAnswer = async ({
  request,
}: {
  request: Request;
}) => {
  try {
    let requestData;
    try {
      requestData = await request.json();
    } catch (parseError) {
      throw new Error("Invalid request format: Unable to parse request body");
    }

    const { question = "", answer = "", resume = "" } = requestData;

    if (!question || typeof question !== "string") {
      throw new Error("Question is required and must be a string");
    }

    if (!answer || typeof answer !== "string") {
      throw new Error("Answer is required and must be a string");
    }

    // Use Gemini 2.5 Flash for faster responses
    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY || "");
    const model = genAI.getGenerativeModel({
      model: "gemini-2.0-flash",
      generationConfig: {
        temperature: 0.5,
        maxOutputTokens: 4000,
        responseMimeType: "application/json",
      },
      // Set thinking_budget to 0 for faster responses
      systemInstruction: {
        thinking_budget: 0,
      } as any, // Using any to bypass type checking for thinking_budget
      // Enable Google Search as a tool
      tools: [{
        googleSearch: {}
      }] as any,
    });

    const feedbackStructure = {
      strengths: [],
      weaknesses: [],
      improvements: [],
      alternativeApproach: "",
      overallRating: 0, // 1-10 scale
      suggestedAnswer: "",
      keyTakeaway: "",
    };

    const prompt = `You are an expert interview coach with deep knowledge of effective interview techniques.
      Your task is to review the candidate's answer to an interview question and provide constructive feedback and the approach to answer the question.
      CRITICAL INSTRUCTIONS:
      1. Output ONLY a valid JSON object
      2. NO explanatory text or reasoning
      3. NO markdown
      4. Use sentence case. Start each sentence with a capital letter for each sentence.
      5. ALWAYS USE GOOGLE SEARCH to find accurate, up-to-date information about best practices for answering this type of interview question
      6. Focus on providing specific, actionable feedback
      7. Match this structure exactly: ${JSON.stringify(feedbackStructure)}

      I was asked this interview question: "${question}"

      My answer was: "${answer}"

      ${resume ? `Here is my resume for context: ${resume}` : ''}

      IMPORTANT: First use Google Search to research best practices for answering this type of interview question, common mistakes, and ideal response structures.

      Please provide feedback on my answer.`;

    const result = await model.generateContent(prompt);
    const response = result.response;
    const content = response.text();

    if (!content) {
      throw new Error("API response content is empty");
    }

    // Parse the JSON response
    let parsedContent;
    try {
      parsedContent = JSON.parse(content);
    } catch (parseError) {
      console.error('Error parsing API response:', parseError);
      console.debug('API response content:', content);
      throw new Error("Failed to parse API response");
    }

    return {
      statusCode: 200,
      body: JSON.stringify(parsedContent),
    };
  } catch (error: any) {
    console.error("Error in reviewAnswer:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: error.message || "An unknown error occurred",
      }),
    };
  }
};
