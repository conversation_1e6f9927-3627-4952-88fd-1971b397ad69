import OpenAI from "openai";
import { DataUtils } from "@/lib/dataUtils"; // Import DataUtils for deepMerge, using alias

export const PerformJobAnalysis = async ({ companyName, jobPosition, userId }: { companyName: string; jobPosition: string; userId?: string | null }): Promise<ReadableStream<Uint8Array>> => {
  try {
    // Data is now passed directly as parameters, no need to parse request body
    // const {
    //   companyName = "Unknown Company",
    //   jobPosition = "Unknown Position",
    //   progressive = false, // New parameter to support progressive loading
    // } = requestData; // This is no longer needed

    // We can add a 'progressive' parameter here if needed in the future,
    // but for now, the frontend doesn't seem to be sending it via SSE query params.
    const progressive = false; // Assume false for now based on current frontend SSE implementation

    // If progressive loading is requested, return a quick analysis stream immediately
    if (progressive) {
      const quickAnalysis = {
        jobRoleSummary: {
          title: jobPosition,
          seniorityLevel: "Analysis in progress...", // Removed jobDescription dependency
          keyResponsibilities: [],
          mainExpectations: ["Analysis in progress..."],
          reportingStructure: "Analysis in progress...",
          teamSize: "Analysis in progress...",
          workMode: "Analysis in progress...", // Removed jobDescription dependency
        },
        skillsAndQualifications: {
          hardSkills: [],
          softSkills: ["Communication", "Teamwork"],
          requiredCertifications: [],
          educationLevel: "Analysis in progress...", // Removed jobDescription dependency
          experienceRequired: "Analysis in progress...",
          preferredSkills: [],
        },
        companyInsights: {
          name: companyName,
          size: "Analysis in progress...",
          industry: "Analysis in progress...",
        },
        analysisMetadata: {
          processingTimeMs: 500,
          modelUsed: "quick-analysis",
          analysisDate: new Date().toISOString(),
          analysisStatus: "partial"
        }
      };

      // Create a ReadableStream from the quick analysis object
      const encoder = new TextEncoder();
      return new ReadableStream({
        start(controller) {
          controller.enqueue(encoder.encode(JSON.stringify(quickAnalysis)));
          controller.close();
        }
      });
    }

    // Get OpenAI API Key from environment variable
    const apiKey = process.env.OPENAI_API_KEY || "";

    if (!apiKey) {
      console.error("OPENAI_API_KEY is not set in the environment variables.");
      throw new Error(
        "OPENAI_API_KEY is not set in the environment variables."
      );
    }

    try {
      const openai = new OpenAI({
        apiKey: apiKey
      });

      const jsonStructure = {
        jobRoleSummary: {
          title: "",
          seniorityLevel: "",
          keyResponsibilities: [],
          mainExpectations: [],
          reportingStructure: "",
          teamSize: "",
          workMode: "",
        },
        skillsAndQualifications: {
          hardSkills: [],
          softSkills: [],
          requiredCertifications: [],
          educationLevel: "",
          experienceRequired: "",
          preferredSkills: [],
        },
        compensationDetails: {
          baseSalaryRange: "",
          totalCompensationRange: "",
          equityDetails: {
            type: "",
            vestingSchedule: "",
            approximateValue: "",
          },
          benefits: {
            healthcare: [],
            retirement: [],
            additionalPerks: [],
          },
          bonusStructure: {
            type: "",
            frequency: "",
            amount: "",
          },
          industryComparison: "",
        },
        companyInsights: {
          name: "",
          size: "",
          industry: "",
          fundingDetails: {
            stage: "",
            totalRaised: "",
            lastRound: "",
            keyInvestors: [],
          },
          growth: {
            metrics: [],
            revenueRange: "",
            userGrowth: "",
          },
          culture: {
            values: [],
            workLifeBalance: "",
            learningOpportunities: "",
            diversityInitiatives: [],
          },
          employeeStats: {
            ratings: {
              overall: 0,
              workLifeBalance: 0,
              careerGrowth: 0,
              compensation: 0,
              culture: 0,
            },
            averageTenure: "",
            turnoverRate: "",
            promotionRate: "",
          },
          competitors: [],
        },
        careerGrowth: {
          promotionPath: [],
          learningBudget: "",
          mentorshipPrograms: [],
          skillDevelopmentOpportunities: [], // Explicitly define as an array
        },
        jobMarketTrends: {
          currentDemand: "",
          growthProjection: "",
          industryTrends: [],
          salaryTrends: "",
          skillsInDemand: [],
          competitorSalaries: [],
        },
        workCulture: {
          workSchedule: "",
          remotePolicy: "",
          workEnvironment: [],
          teamDynamics: "",
          managementStyle: "",
          redFlags: [],
          positiveIndicators: [],
        },
        applicationTips: {
          resumeAdvice: [],
          coverLetterTips: [],
          interviewPrep: {
            commonQuestions: [],
            technicalAssessment: "",
            processSteps: [],
          },
          negotiationTips: [],
        },
      };

      // Define partial JSON structures for each AI call
      const jsonStructurePart1 = {
        jobRoleSummary: jsonStructure.jobRoleSummary,
        skillsAndQualifications: jsonStructure.skillsAndQualifications,
      };

      const jsonStructurePart2 = {
        compensationDetails: jsonStructure.compensationDetails,
        companyInsights: jsonStructure.companyInsights,
      };

      const jsonStructurePart3 = {
        careerGrowth: jsonStructure.careerGrowth,
        jobMarketTrends: jsonStructure.jobMarketTrends,
        workCulture: jsonStructure.workCulture,
        applicationTips: jsonStructure.applicationTips,
      };

      // Helper function to create a prompt for a specific part
      const createPartPrompt = (partStructure: any, partName: string) => `You are an expert career advisor and job market analyst.
CRITICAL INSTRUCTIONS:
1. Output ONLY a valid JSON object
2. NO explanatory text or reasoning
3. NO markdown
4. Keep analysis concise
5. Use web search to find accurate, up-to-date information about the company, industry standards, and market data.
6. Focus on providing specific, actionable insights.
7. Use the indian currency symbol (₹) for currency values.
8. Match this structure exactly: ${JSON.stringify(partStructure, null, 2)}
9. IMPORTANT: Perform analysis based SOLELY on the provided Company and Position. Do NOT ask for a job description.
10. If specific details cannot be inferred from Company and Position, use "N/A" for string fields and empty arrays for array fields.

Perform a comprehensive analysis for the ${partName} section of the job analysis for the following job:
Company: ${companyName}
Position: ${jobPosition}

Additional Context:
- Thoroughly research the company's background using web search.
- Analyze industry standards and market trends using web search.
- Provide insights specific to this role and company based on available public information found via web search.
- Use multiple reputable sources for information found via web search.
- Use web search tool extensively to get a broader understanding of the industry and company.
- For array fields, provide multiple specific items as an array if possible based on web search results, otherwise an empty array.
- Ensure each array field contains multiple relevant, distinct entries if data is available via web search.
- IMPORTANT: Even with web search, some specific details (like exact funding stage or role-specific growth metrics) might not be publicly available without a job description. In such cases, it is CORRECT to use "N/A" or empty arrays as instructed. Do not invent information.
${partName.includes("Company Insights") ? `- SPECIFIC INSTRUCTION: Use web search to find the company's funding stage and key growth metrics. Provide this information if found, otherwise use "N/A".` : ''}`;

      // Create promises for parallel AI calls
      const call1Promise = openai.chat.completions.create({
        model: "gpt-4.1-nano",
        messages: [
          {
            role: "system",
            content: "You are an expert career advisor and job market analyst. Provide detailed job analysis in JSON format."
          },
          {
            role: "user",
            content: createPartPrompt(jsonStructurePart1, "Job Role Summary and Skills & Qualifications")
          }
        ],
        temperature: 0.1,
        max_tokens: 1000, // Reduced max_tokens for each part
        response_format: { type: "json_object" },
        stream: true,
      });

      const call2Promise = openai.chat.completions.create({
        model: "gpt-4.1-nano",
        messages: [
          {
            role: "system",
            content: "You are an expert career advisor and job market analyst. Provide detailed job analysis in JSON format."
          },
          {
            role: "user",
            content: createPartPrompt(jsonStructurePart2, "Compensation Details and Company Insights")
          }
        ],
        temperature: 0.1,
        max_tokens: 1000,
        response_format: { type: "json_object" },
        stream: true,
      });

      const call3Promise = openai.chat.completions.create({
        model: "gpt-4.1-nano",
        messages: [
          {
            role: "system",
            content: "You are an expert career advisor and job market analyst. Provide detailed job analysis in JSON format."
          },
          {
            role: "user",
            content: createPartPrompt(jsonStructurePart3, "Career Growth, Job Market Trends, Work Culture, and Application Tips")
          }
        ],
        temperature: 0.1,
        max_tokens: 1000, // Adjusted max_tokens for each part
        response_format: { type: "json_object" },
        stream: true,
      });

      const encoder = new TextEncoder();

      const customReadable = new ReadableStream({
        async start(controller) {
          // Process each stream and return its final parsed content
          const processSingleStream = async (stream: AsyncIterable<OpenAI.Chat.Completions.ChatCompletionChunk>): Promise<Record<string, any>> => {
            const decoder = new TextDecoder("utf-8");
            let accumulatedContent = "";
            let lastValidParsed: Record<string, any> = {};

            for await (const chunk of stream) {
              const content = chunk.choices[0]?.delta?.content || "";
              accumulatedContent += content;

              // Attempt to parse the accumulated content as JSON
              try {
                const parsed = JSON.parse(accumulatedContent);
                lastValidParsed = parsed; // Store the last successfully parsed JSON
                accumulatedContent = ""; // Reset accumulated content after a successful parse
              } catch (e) {
                // Incomplete JSON, continue accumulating
              }
            }
            // After stream ends, process any remaining content one last time
            if (accumulatedContent.trim().length > 0) {
              try {
                const parsed = JSON.parse(accumulatedContent);
                lastValidParsed = parsed;
              } catch (e) {
                console.error("Failed to parse final accumulated content from a stream:", e);
                // If final content is still incomplete, use the last valid parsed data
              }
            }
            return lastValidParsed;
          };

          // Run all stream processing in parallel to get their final JSONs
          const [result1, result2, result3] = await Promise.all([
            processSingleStream(await call1Promise),
            processSingleStream(await call2Promise),
            processSingleStream(await call3Promise),
          ]);

          // Merge all final results into one comprehensive object
          let finalMergedAnalysis: Record<string, any> = {};
          finalMergedAnalysis = DataUtils.deepMerge(finalMergedAnalysis, result1);
          finalMergedAnalysis = DataUtils.deepMerge(finalMergedAnalysis, result2);
          finalMergedAnalysis = DataUtils.deepMerge(finalMergedAnalysis, result3);

          // Enqueue the single, complete merged JSON
          controller.enqueue(encoder.encode(JSON.stringify(finalMergedAnalysis) + "\n"));
          controller.close();
        },
        cancel() {
          console.log("Stream cancelled by consumer.");
        }
      });

      return customReadable;

    } catch (error) {
      if (error instanceof Error) {
        console.error("OpenAI Job research Error:", error.message);
      }
      throw error;
    }
  } catch (error) {
    throw error;
  }
};
