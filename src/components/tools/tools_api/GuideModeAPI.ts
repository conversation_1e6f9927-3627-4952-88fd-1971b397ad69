/**
 * GuideModeAPI.ts
 *
 * This file contains the API functions for the Guide Mode component.
 * It handles sending messages to the guide-mode Netlify function and processing responses.
 * Updated to work with the new Google Gen AI SDK.
 */

// Define types for the conversation history (kept for backward compatibility)
export interface ConversationMessage {
  role: string;
  parts: { text: string }[];
}

// Define the response type
export interface GuideModeResponse {
  success: boolean;
  data?: string;
  error?: string;
}

/**
 * Sends a message to the guide-mode API
 *
 * @param message - The user's message
 * @param history - The conversation history (no longer sent to API, kept for backward compatibility)
 * @param authToken - Optional authentication token
 * @param mode - Optional mode parameter (defaults to "careerDiscussion")
 * @param stream - Optional parameter to request streaming response
 * @param isNewChat - Optional parameter to indicate if this is a new chat session
 * @returns Promise with the API response
 */
export async function sendGuideModeMessage(
  message: string,
  _history: ConversationMessage[] = [], // Kept for backward compatibility but unused
  authToken: string | null = null,
  mode: string = "careerDiscussion",
  stream: boolean = false,
  isNewChat: boolean = false
): Promise<GuideModeResponse> {
  try {
    // Debug log to help diagnose issues
    console.log(`Sending message to guide-mode API: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);
    console.log(`Using Google Gen AI SDK with built-in chat history management, isNewChat: ${isNewChat}`);

    // Make the API request
    const response = await fetch("/.netlify/functions/guide-mode", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...(authToken ? { Authorization: `Bearer ${authToken}` } : {}),
      },
      body: JSON.stringify({
        message,
        mode,
        stream,
        isNewChat: isNewChat ? true : undefined, // Only include if true
      }),
    });

    // Handle non-OK responses
    if (!response.ok) {
      let errorMsg = `HTTP error! status: ${response.status}`;
      try {
        const errData = await response.json();
        errorMsg = errData.error || errorMsg;
      } catch (e) {
        // Ignore if response is not JSON
      }
      console.error("API Error Response:", errorMsg);
      return {
        success: false,
        error: errorMsg
      };
    }

    // If streaming was requested, return the response object for streaming
    if (stream) {
      return {
        success: true,
        data: "Streaming response initiated",
        response: response
      } as any;
    }

    // Parse the response for non-streaming requests
    const data = await response.json();

    // Return the response data
    if (data.success && data.data) {
      return {
        success: true,
        data: data.data
      };
    } else {
      return {
        success: false,
        error: data.error || "Sorry, something went wrong receiving the data."
      };
    }
  } catch (error) {
    console.error("Error calling guide mode API:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Could not connect to the AI service."
    };
  }
}

/**
 * Sends a message to the guide-mode API with the heroConcise mode
 * Used for the hero section quick responses
 * Updated to work with the new Google Gen AI SDK.
 *
 * @param message - The user's message
 * @returns Promise with the API response
 */
export async function sendHeroConciseMessage(message: string): Promise<GuideModeResponse> {
  try {
    console.log(`Sending heroConcise message using Google Gen AI SDK: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);

    const response = await fetch("/.netlify/functions/guide-mode", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        message,
        mode: "heroConcise",
        isNewChat: true // Always treat hero concise messages as new chats
      }),
    });

    if (!response.ok) {
      let errorMsg = `Error: ${response.status} ${response.statusText}`;
      try {
        const errorData = await response.json();
        errorMsg = errorData.error || errorMsg;
      } catch (e) {
        // Ignore if response body isn't JSON or empty
      }
      return {
        success: false,
        error: errorMsg
      };
    }

    const data = await response.json();

    return {
      success: true,
      data: data.data
    };
  } catch (error) {
    console.error("Error in sendHeroConciseMessage:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred"
    };
  }
}

/**
 * Optimizes conversation history for API requests
 *
 * @param history - The full conversation history
 * @param maxMessages - Maximum number of messages to include
 * @param maxLength - Maximum length of each message
 * @returns Optimized conversation history
 */
export function optimizeConversationHistory(
  history: ConversationMessage[],
  maxMessages: number = 10,
  maxLength: number = 1000
): ConversationMessage[] {
  if (!history || !Array.isArray(history)) return [];

  // Truncate long messages
  const truncatedHistory = history.map(msg => ({
    role: msg.role,
    parts: [{
      text: msg.parts[0]?.text?.length > maxLength
        ? msg.parts[0].text.substring(0, maxLength) + "..."
        : msg.parts[0]?.text || ""
    }]
  }));

  // Return only the most recent messages
  return truncatedHistory.slice(-maxMessages);
}
