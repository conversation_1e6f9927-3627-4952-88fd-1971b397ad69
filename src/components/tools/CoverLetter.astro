---
import ErrorModal from "../ErrorModal.astro";
import LoadingSpinner from "../LoadingSpinner.astro";
import ButtonLoader from "../ButtonLoader.astro";
import UpgradePrompt from "../UpgradePrompt.astro";

interface CoverLetterTemplate {
  id: string;
  name: string;
  description: string;
  recommendedFor: string;
  color: string;
  promptModifier: string;
}

const coverLetterTemplates: CoverLetterTemplate[] = [
  { id: "professional", name: "Professional", description: "Formal and structured template for corporate roles", recommendedFor: "Corporate Positions", color: "indigo", promptModifier: "Write in a formal, professional tone with a focus on corporate achievements and structured language. Use precise, business-oriented vocabulary." },
  { id: "creative", name: "Creative", description: "Innovative template for creative industries", recommendedFor: "Design, Marketing, Media Roles", color: "emerald", promptModifier: "Write with a creative, engaging tone that highlights innovative thinking and unique personal brand. Use storytelling and vibrant language." },
  { id: "startup", name: "Startup", description: "Entrepreneurial template for tech and startup roles", recommendedFor: "Tech Startups, Entrepreneurial Positions", color: "orange", promptModifier: "Write with an entrepreneurial spirit, emphasizing adaptability, innovation, and passion for solving complex problems. Use dynamic, forward-thinking language." },
];

// --- Reusable Tailwind CSS Class Strings for Cleaner HTML ---
const formContainerClasses = `w-full space-y-6 sm:space-y-8 bg-white dark:bg-gray-900/80 backdrop-blur-md rounded-3xl p-4 sm:p-8
                              shadow-xl border-gray-100 border dark:border-gray-700/50
                              transition-all duration-300 ease-in-out hover:shadow-2xl`;

const textareaClasses = `w-full px-4 py-3 border border-gray-300 dark:border-gray-700/70 bg-white dark:bg-gray-800/30 rounded-xl
                         dark:text-gray-100 focus:border-primary dark:focus:border-primary focus:ring-1 focus:ring-primary
                         outline-none text-md transition-colors duration-200 resize-y`;

const resumeOptionButtonClasses = `flex-1 py-4 px-4 border-2 border-gray-300 dark:border-gray-700 rounded-xl
                                  bg-white/10 dark:bg-gray-800/20 hover:bg-white/20 dark:hover:bg-gray-800/30
                                  transition-all duration-300 text-center group`;

const templateLabelClasses = `block p-3 border-2 border-gray-300 dark:border-gray-700 rounded-xl
                              bg-white/10 dark:bg-gray-800/20 hover:bg-white/20 dark:hover:bg-gray-800/30
                              peer-checked:border-primary peer-checked:ring-2 peer-checked:ring-primary/30
                              transition-all duration-300 text-center space-y-2`;

const primaryButtonClasses = `w-full sm:w-auto inline-flex h-11 sm:h-12 items-center justify-center px-6 sm:px-8 py-2 sm:py-3
                              text-sm sm:text-base font-semibold text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700
                              dark:text-white dark:bg-gradient-to-r dark:from-blue-500 dark:to-indigo-500 dark:hover:from-blue-600 dark:hover:to-indigo-600
                              rounded-full transition-all duration-300 ease-in-out transform hover:scale-[1.03] active:scale-95
                              focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900
                              shadow-lg hover:shadow-xl`;

const secondaryButtonClasses = `w-full sm:w-auto inline-flex h-11 sm:h-12 items-center justify-center px-6 sm:px-8 py-2 sm:py-3
                                text-sm sm:text-base font-semibold text-gray-700 bg-gray-100 hover:bg-gray-200
                                dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600
                                border border-transparent rounded-full transition-all duration-300 ease-in-out
                                transform hover:scale-[1.03] active:scale-95
                                focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-offset-gray-900
                                shadow-md hover:shadow-lg`;
---

<div class="relative py-16 w-full">
  <div class="relative w-full">
    <div class="container mx-auto px-3 md:px-6 lg:px-8 max-w-6xl">
      <div class="w-full mx-auto">
        <form id="coverLetterForm" class={formContainerClasses}>
          <!-- Section: Job Description -->
          <div class="grid grid-cols-1 gap-3 sm:gap-6">
            <div class="space-y-1.5 sm:space-y-2">
              <label for="jobDescription" class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                Job Description <span class="text-red-600">*</span>
              </label>
              <textarea id="jobDescription" placeholder="Paste the complete job description here." rows="6" class={textareaClasses} required></textarea>
              <div class="validation-error text-red-500 text-sm mt-1"></div>
            </div>
          </div>

          <!-- Section: Resume Input -->
          <div class="grid grid-cols-1 gap-3 sm:gap-6">
            <div class="space-y-1.5 sm:space-y-2">
              <label class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                Resume <span class="text-red-600">*</span>
              </label>

              <!-- Resume Options Selector -->
              <div id="resumeOptionsSelector" class="flex flex-col sm:flex-row gap-3 mt-2">
                <button type="button" id="resumeFileUploadButton" class={resumeOptionButtonClasses}>
                  <div class="flex flex-col items-center justify-center space-y-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-primary group-hover:text-primary-dark transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span class="block font-medium text-gray-900 dark:text-white">Upload File</span>
                    <span class="text-xs text-gray-500 dark:text-gray-400">PDF, DOCX, TXT</span>
                  </div>
                </button>
                <button type="button" id="importResumeButton" class={resumeOptionButtonClasses}>
                  <div class="flex flex-col items-center justify-center space-y-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-primary group-hover:text-primary-dark transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                    </svg>
                    <span class="block font-medium text-gray-900 dark:text-white">Resume Manager</span>
                    <span class="text-xs text-gray-500 dark:text-gray-400">Select from saved resumes</span>
                  </div>
                </button>
                <button type="button" id="enterManuallyButton" class={resumeOptionButtonClasses}>
                  <div class="flex flex-col items-center justify-center space-y-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-primary group-hover:text-primary-dark transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 0L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    <span class="block font-medium text-gray-900 dark:text-white">Enter Manually</span>
                    <span class="text-xs text-gray-500 dark:text-gray-400">Type or paste your content</span>
                  </div>
                </button>
              </div>

              <!-- Manual Entry Container -->
              <div id="manualEntryContainer" class="hidden space-y-2">
                <div class="flex justify-between items-center">
                  <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Manual Entry</h4>
                  <button type="button" id="cancelManualEntry" class="text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">Cancel</button>
                </div>
                <textarea id="resumeContent" placeholder="Paste your current resume content here." rows="6" class={textareaClasses}></textarea>
                <div class="validation-error text-red-500 text-sm mt-1"></div>
              </div>

              <!-- File Added UI -->
              <div id="resumeFileAddedUI" class="hidden">
                <div class="py-3 px-4 border-2 border-gray-300 dark:border-gray-700 rounded-xl bg-white/10 dark:bg-gray-800/20 transition-all duration-300">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white">Resume Added</div>
                        <div id="resumeFileName" class="text-xs text-gray-500">Document.pdf</div>
                      </div>
                    </div>
                    <button type="button" id="changeResumeSource" class="text-xs bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-full px-3 py-1 transition-colors duration-200">Change</button>
                  </div>
                </div>
              </div>

              <!-- Hidden file input -->
              <input type="file" id="resumeFileInput" class="hidden" accept=".pdf,.doc,.docx,.txt,.rtf" />
            </div>
          </div>

          <!-- Section: Custom Instructions -->
          <div class="grid grid-cols-1 gap-3 sm:gap-6">
            <div class="space-y-1.5 sm:space-y-2">
              <label for="customInstructions" class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                Custom Instructions <span class="text-xs sm:text-sm text-gray-500 dark:text-gray-400 ml-2">(Optional)</span>
              </label>
              <textarea id="customInstructions" placeholder="e.g., Emphasize my project management experience." rows="3" class={textareaClasses}></textarea>
            </div>
          </div>

          <!-- Section: Template Selection -->
          <div class="space-y-4">
            <h3 class="text-lg sm:text-xl font-medium text-gray-900 dark:text-white">Choose Template</h3>
            <div class="grid grid-cols-2 sm:grid-cols-3 gap-3">
              {coverLetterTemplates.map((template, index) => (
                <div class="relative group cursor-pointer">
                  <input type="radio" id={`template-${template.id}`} name="template" value={template.id} class="hidden peer" required checked={index === 0} />
                  <label for={`template-${template.id}`} class={templateLabelClasses}>
                    <span class="block text-sm font-semibold text-gray-900 dark:text-white">{template.name}</span>
                    <span class="block text-xs text-gray-600 dark:text-gray-400">{template.description}</span>
                  </label>
                </div>
              ))}
            </div>
          </div>

          <!-- Section: Action Buttons -->
          <div class="flex flex-col sm:flex-row justify-center items-center space-y-2.5 sm:space-y-0 sm:space-x-4 pt-4">
            <button type="submit" id="generateCoverLetterBtn" class={primaryButtonClasses}>
              <ButtonLoader id="generateButtonLoader" isLoading={false} loadingText="Generating..." spinnerPosition="left" spinnerSize="md" spinnerColor="white">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Generate Cover Letter
              </ButtonLoader>
            </button>
            <button type="button" id="clearFormBtn" class={secondaryButtonClasses}>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
              Clear
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Modal for Cover Letter Preview -->
<div id="coverLetterModal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black/50 backdrop-blur-sm transition-all duration-300 ease-in-out opacity-0">
  <div class="relative w-[95%] max-w-4xl max-h-[90vh] flex flex-col mx-auto p-4 md:p-8 bg-gray-100 dark:bg-gray-900 rounded-sm shadow-lg overflow-y-auto translate-y-4 transition-all duration-300 ease-in-out">
    <button id="closeModalBtn" class="absolute top-4 right-4 z-20 group p-1.5 bg-gray-700 hover:bg-gray-600 rounded-full transition-colors duration-200">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>
    <div class="flex-1 overflow-y-auto p-4 md:p-8 bg-gray-100 dark:bg-gray-900 rounded-sm shadow-lg flex flex-col">
      <div id="streamingContent" class="min-h-[300px] w-full text-base font-serif text-gray-900 dark:text-white leading-relaxed tracking-normal whitespace-pre-wrap mx-auto max-w-3xl transition-opacity duration-300 ease-in-out" aria-live="polite">
        <!-- Content will be streamed here -->
      </div>
    </div>
    <div id="coverLetterActionButtons" class="flex flex-row justify-center items-center p-4 space-x-3 opacity-0 transition-all duration-500 ease-in-out transform translate-y-4 bg-transparent">
      <button id="downloadButton" class="inline-flex items-center justify-center px-4 py-2 text-sm font-semibold bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full transition-colors duration-300">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
        </svg>
        <span class="hidden sm:inline">Download</span>
      </button>
      <button id="copyButton" class="inline-flex items-center justify-center px-4 py-2 text-sm font-semibold bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full transition-colors duration-300">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
        </svg>
        <span class="hidden sm:inline">Copy</span>
      </button>
    </div>
  </div>
</div>

<!-- Other Astro Components -->
<ErrorModal id="coverLetterErrorModal" title="Error" message="An error occurred. Please try again." isOpen={false} zIndex={60} />
<UpgradePrompt id="coverLetterUpgradePrompt" featureName="cover letter generations" isOpen={false} />

<script>
  import { authService } from "../../lib/auth";
  import { setupResumeInput } from "../../lib/resumeInputHandler";
  import { ErrorHandler } from "../../lib/errorHandler";
  import { LoadingManager } from "../../lib/loadingManager";
  import { ComponentInitializer } from "../../lib/componentInitializer";
  import { APIClient } from "../../lib/apiClient";

  /**
   * Manages the entire state and functionality of the Cover Letter Generator component.
   */
  class CoverLetterGenerator {
    // DOM Elements
    form: HTMLFormElement | null = null;
    modal: HTMLDivElement | null = null;
    streamingContent: HTMLDivElement | null = null;
    actionButtons: HTMLDivElement | null = null;
    
    // Services and Handlers
    resumeInputHandler;
    errorHandler;
    loadingManager;
    componentInitializer;

    constructor() {
      this._bindDOMElements();
      this._initializeServices();
      this._bindEventListeners();
      this._checkInitialState();
    }

    /** Binds all necessary DOM elements to class properties. */
    _bindDOMElements() {
      this.form = document.getElementById("coverLetterForm") as HTMLFormElement;
      this.modal = document.getElementById("coverLetterModal") as HTMLDivElement;
      this.streamingContent = document.getElementById("streamingContent") as HTMLDivElement;
      this.actionButtons = document.getElementById("coverLetterActionButtons") as HTMLDivElement;
    }

    /** Initializes external services and handlers. */
    _initializeServices() {
      this.errorHandler = new ErrorHandler("coverLetter");
      this.loadingManager = new LoadingManager("coverLetter");
      this.componentInitializer = new ComponentInitializer("coverLetter");
      
      this.resumeInputHandler = setupResumeInput({
        componentId: "coverLetter",
        fileInputId: "resumeFileInput",
        fileUploadButtonId: "resumeFileUploadButton",
        fileAddedUIId: "resumeFileAddedUI",
        fileNameElementId: "resumeFileName",
        changeSourceButtonId: "changeResumeSource",
        optionsSelectorId: "resumeOptionsSelector",
        manualEntryContainerId: "manualEntryContainer",
        enterManuallyButtonId: "enterManuallyButton",
        cancelManualEntryButtonId: "cancelManualEntry",
        resumeContentTextareaId: "resumeContent",
        importResumeButtonId: "importResumeButton",
        onError: (title, message) => this.errorHandler.showError(title, message),
      });
    }

    /** Binds all event listeners for the component. */
    _bindEventListeners() {
      this.form?.addEventListener("submit", this._handleSubmit.bind(this));
      document.getElementById("clearFormBtn")?.addEventListener("click", this._clearForm.bind(this));
      document.getElementById("closeModalBtn")?.addEventListener("click", () => this._toggleModal(false));
      document.getElementById("downloadButton")?.addEventListener("click", this._handleDownload.bind(this));
      document.getElementById("copyButton")?.addEventListener("click", this._handleCopy.bind(this));
      
      // Close modal on outside click
      this.modal?.addEventListener("click", (event) => {
        if (event.target === this.modal) {
          this._toggleModal(false);
        }
      });
    }

    /** Checks for any initial state, like pre-filled data or user access. */
    async _checkInitialState() {
      // Check for stored job data from another page
      const storedJob = localStorage.getItem("currentJob");
      if (storedJob) {
        try {
          const jobData = JSON.parse(storedJob);
          const jobDescInput = document.getElementById("jobDescription") as HTMLTextAreaElement | null;
          if (jobDescInput) jobDescInput.value = jobData.jobDescription || "";
          localStorage.removeItem("currentJob");
        } catch (e) {
          console.warn("Could not parse stored job data.");
        }
      }
      // Clear any pre-existing resume content on this page
      this.resumeInputHandler.clearResumeContent();
      // Check if user can access this feature
      await this._checkFeatureAccess();
    }

    /** Handles the main form submission event. */
    async _handleSubmit(event) {
      event.preventDefault();
      
      const { canAccess } = await this._checkFeatureAccess();
      if (!canAccess) return;

      if (!this._validateForm()) return;

      this._showLoadingState();
      
      try {
        const formData = this._collectFormData();
        const response = await APIClient.generateCoverLetterStream(formData);
        await this._handleApiStream(response);
        await this.componentInitializer.trackFeatureUsage();
      } catch (error) {
        this._showErrorState(error);
      } finally {
        this.loadingManager?.setButtonLoading("generateButtonLoader", false);
      }
    }

    /** Validates all required form inputs. */
    _validateForm() {
      let isValid = true;
      const jobDescriptionInput = document.getElementById("jobDescription") as HTMLTextAreaElement | null;
      const resumeContent = this.resumeInputHandler.getResumeContent();

      // Validate Job Description
      if (!jobDescriptionInput || jobDescriptionInput.value.trim().length < 20) {
        this._setValidationError(jobDescriptionInput!, "Job description must be at least 20 characters long.");
        isValid = false;
      } else {
        this._clearValidationError(jobDescriptionInput);
      }

      // Validate Resume Content
      const resumeOptionsSelector = document.getElementById("resumeOptionsSelector");
      const resumeContainer = resumeOptionsSelector ? resumeOptionsSelector.parentElement : null;

      if (resumeContent.trim().length < 50) {
        if (resumeContainer) {
          this._setValidationError(resumeContainer, "Please provide your resume content by uploading, importing, or pasting it manually.");
        }
        isValid = false;
      } else {
        if (resumeContainer) {
          this._clearValidationError(resumeContainer);
        }
      }
      
      return isValid;
    }

    /** Collects all data from the form into a structured object. */
    _collectFormData() {
      const jobDescription = (document.getElementById("jobDescription") as HTMLTextAreaElement).value;
      const resumeContent = this.resumeInputHandler.getResumeContent();
      const customInstructions = (document.getElementById("customInstructions") as HTMLTextAreaElement).value;
      const template = (document.querySelector('input[name="template"]:checked') as HTMLInputElement).value;
      
      return { jobDescription, resumeContent, customInstructions, template };
    }

    /** Processes the streaming API response. */
    async _handleApiStream(response) {
      if (this.streamingContent) {
        this.streamingContent.classList.remove("opacity-100"); // Fade out current content
        this.streamingContent.classList.add("opacity-0");
        this.streamingContent.classList.remove("flex", "items-center", "justify-center"); // Ensure content starts from top
        // Wait for fade-out to complete before clearing and streaming new content
        await new Promise(resolve => setTimeout(resolve, 300)); // Match transition duration
        this.streamingContent.innerHTML = ""; // Clear loading pulse
      }
      let fullText = "";
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n\n");

        for (const line of lines) {
          if (line.startsWith("data: ") && !line.includes("[DONE]")) {
            try {
              const jsonData = JSON.parse(line.slice(5));
              if (jsonData.content) {
                fullText += jsonData.content;
                if (this.streamingContent) {
                  this.streamingContent.innerHTML = fullText; // Use innerHTML to render formatting like newlines
                }
              }
            } catch (e) { /* Ignore parsing errors */ }
          }
        }
      }
      if (this.streamingContent) {
        this.streamingContent.classList.remove("opacity-0");
        this.streamingContent.classList.add("opacity-100"); // Fade in new content
      }
      this._showActionButtons();
    }

    /** Clears all form fields and resets the UI. */
    _clearForm() {
      this.form?.reset();
      this.resumeInputHandler.clearResumeContent();
      this._clearValidationError(document.getElementById("jobDescription") as HTMLTextAreaElement | null);
      
      const resumeOptionsSelector = document.getElementById("resumeOptionsSelector");
      const resumeContainer = resumeOptionsSelector ? resumeOptionsSelector.parentElement : null;
      if (resumeContainer) {
        this._clearValidationError(resumeContainer);
      }
      
      const professionalTemplate = document.getElementById("template-professional") as HTMLInputElement | null;
      if (professionalTemplate) {
        professionalTemplate.checked = true;
      }
    }

    // --- UI State Management Methods ---

    _showLoadingState() {
      this.loadingManager?.setButtonLoading("generateButtonLoader", true, "Generating...");
      this._toggleModal(true);
      this.actionButtons?.classList.add("opacity-0", "translate-y-4");
      if (this.streamingContent) {
        this.streamingContent.classList.add("flex", "items-center", "justify-center", "opacity-100"); // Center loading spinner and ensure it's visible
        this.streamingContent.innerHTML = `
          <div class="animate-pulse text-center text-gray-500 dark:text-gray-400 space-y-4 font-sans">
            <div class="flex justify-center items-center space-x-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>
              <h3 class="text-lg font-semibold">Cover Letter Optimization in Progress</h3>
            </div>
            <p class="text-sm max-w-xs mx-auto">Optimizing your cover letter with your job description and resume...</p>
          </div>`;
      }
    }

    _showErrorState(error) {
      console.error("Cover Letter generation error:", error);
      if (this.streamingContent) {
        this.streamingContent.innerHTML = `<div class="text-red-500 text-center"><p class="font-semibold">Error Generating Cover Letter</p><p class="text-sm mt-2">${error.message || 'An unknown error occurred.'}</p></div>`;
      }
      this.errorHandler?.showError("Generation Failed", "There was an error generating your cover letter. Please try again.");
    }


    _showActionButtons() {
      this.actionButtons?.classList.remove("opacity-0", "translate-y-4");
    }

    _toggleModal(show) {
      if (!this.modal) return;

      if (show) {
        this.modal.classList.remove("hidden");
        this.modal.classList.add("flex");
        // Allow a brief moment for 'flex' to apply before starting transition
        setTimeout(() => {
          this.modal?.classList.remove("opacity-0");
          this.modal?.classList.add("opacity-100");
          // Add slide-up effect to the inner content
          const innerContent = this.modal!.querySelector(".relative.w-\\[95\\%\\]") as HTMLElement | null;
          if (innerContent) {
            innerContent.classList.remove("translate-y-4");
            innerContent.classList.add("translate-y-0");
          }
        }, 10); // Small delay
      } else {
        this.modal.classList.remove("opacity-100");
        this.modal.classList.add("opacity-0");
        // Add slide-down effect to the inner content
        const innerContent = this.modal!.querySelector(".relative.w-\\[95\\%\\]") as HTMLElement | null;
        if (innerContent) {
          innerContent.classList.remove("translate-y-0");
          innerContent.classList.add("translate-y-4");
        }

        // Hide after transition completes
        this.modal.addEventListener("transitionend", () => {
          if (this.modal?.classList.contains("opacity-0")) {
            this.modal.classList.add("hidden");
          }
        }, { once: true });
      }
    }

    // --- Validation Helper Methods ---

    _setValidationError(element, message) {
        const container = element.closest('.space-y-1\\.5, .space-y-2');
        let errorEl = container.querySelector(".validation-error");
        if (!errorEl) {
            errorEl = document.createElement("div");
            errorEl.className = "validation-error text-red-500 text-sm mt-1";
            container.appendChild(errorEl);
        }
        errorEl.textContent = message;
        (container.querySelector('textarea, div[id^="resume"]') || element).classList.add("border-red-500");
    }

    _clearValidationError(element) {
        const container = element.closest('.space-y-1\\.5, .space-y-2');
        const errorEl = container.querySelector(".validation-error");
        if (errorEl) errorEl.textContent = "";
        (container.querySelector('textarea, div[id^="resume"]') || element).classList.remove("border-red-500");
    }

    // --- Action Button Handlers ---

    _handleDownload() {
      const content = this.streamingContent?.textContent || "";
      const blob = new Blob([content], { type: "text/plain;charset=utf-8" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "cover-letter.txt";
      a.click();
      URL.revokeObjectURL(url);
    }

    _handleCopy() {
      const button = document.getElementById("copyButton") as HTMLButtonElement | null;
      if (button) {
        navigator.clipboard.writeText(this.streamingContent?.textContent || "").then(() => {
          const originalContent = button.innerHTML;
          button.textContent = "Copied!";
          button.classList.add("bg-green-500", "text-white");
          setTimeout(() => {
            button.innerHTML = originalContent;
            button.classList.remove("bg-green-500", "text-white");
          }, 2000);
        });
      }
    }

    /** Checks if the user has access to the feature and updates the UI accordingly. */
    async _checkFeatureAccess() {
      const access = await this.componentInitializer.checkFeatureAccess();
      const submitButton = document.getElementById("generateCoverLetterBtn") as HTMLButtonElement | null;
      if (submitButton && access.usageLimitReached) {
        submitButton.disabled = true;
        submitButton.classList.add("opacity-50", "cursor-not-allowed");
        submitButton.title = "Usage limit reached. Please upgrade your plan.";
      } else if (submitButton) {
        // Ensure button is enabled if access is granted or not limited
        submitButton.disabled = false;
        submitButton.classList.remove("opacity-50", "cursor-not-allowed");
        submitButton.title = "";
      }
      return access;
    }
  }

  // Initialize the component once the DOM is fully loaded.
  document.addEventListener("DOMContentLoaded", () => {
    new CoverLetterGenerator();
  });
</script>

<style>
  textarea.border-red-500, div.border-red-500 {
    border-color: rgb(239 68 68) !important;
  }
</style>
