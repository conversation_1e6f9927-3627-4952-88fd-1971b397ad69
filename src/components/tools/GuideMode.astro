---
// Import necessary components
import { SYSTEM_PROMPTS } from "../../lib/ai-copilot/config"; // Assuming this path is correct
import UpgradePrompt from "../UpgradePrompt.astro";
import ErrorModal from "../ErrorModal.astro";
import <PERSON><PERSON><PERSON>oader from "../ButtonLoader.astro"; // This component is imported but not explicitly used in the provided template. Ensure it's used or remove if not needed.

// Props interface
interface Props {
  initialQuery?: string;
  lastQ?: string;
  lastA?: string;
  promptSuggestions?: string[];
}

// Get props with defaults
const {
  initialQuery = "",
  lastQ = "",
  lastA = "",
  promptSuggestions = [
    "What are some good career paths for someone with my skills?",
    "How can I improve my networking skills?",
    "What are the latest trends in the job market?",
    "Can you help me identify my strengths and weaknesses?",
    "What are some strategies for dealing with workplace stress?",
  ],
} = Astro.props;

---

<script define:vars={{ initialQuery, lastQ, lastA }}>
  // Make props available on the window object for client-side script
  window.initialQuery = initialQuery;
  window.lastQ = lastQ;
  window.lastA = lastA;
</script>

<script>
  // Import necessary client-side libraries/modules
  import { sendGuideModeMessage } from "../tools/tools_api/GuideModeAPI"; // Assuming this path is correct
  import { firebaseInstance, getCurrentUser } from "../../lib/firebase"; // Assuming this path is correct
  import { authService } from "../../lib/auth"; // Assuming this path is correct
  // import "../../scripts/guideModeOptimistic.js"; // Ensure this script doesn't conflict with manual DOM updates

  // --- State Variables ---
  let conversationHistory = [] as { role: string; parts: { text: string }[] }[];
  let userContext: {
    skills: string[];
    interests: string[];
    experience: string[];
    goals: string[];
  } = { skills: [], interests: [], experience: [], goals: [] };

  interface ActionItem { id: string; text: string; completed: boolean; }
  let actionItems: ActionItem[] = [];

  // --- DOM Element References (initialized in DOMContentLoaded) ---
  let form, input, chatWindow, submitButton, suggestionButtons, topicButtons, voiceInputButton,
      promptSuggestionsContainer, contextPanel, actionItemsPanel, errorModalElement, // Renamed to avoid conflict with ErrorModal component
      newChatButton, showSuggestionsButton;

  // --- Local Storage Functions ---
  function saveConversationToLocalStorage() {
    try {
      localStorage.setItem('guideMode_conversation', JSON.stringify(conversationHistory));
      localStorage.setItem('guideMode_timestamp', Date.now().toString());
    } catch (error) { console.warn('Failed to save conversation to localStorage:', error); }
  }

  function loadConversationFromLocalStorage() {
    try {
      const savedConversation = localStorage.getItem('guideMode_conversation');
      const timestamp = localStorage.getItem('guideMode_timestamp');
      if (savedConversation && timestamp) {
        const hoursDiff = (Date.now() - parseInt(timestamp, 10)) / (1000 * 60 * 60);
        if (hoursDiff < 24) return JSON.parse(savedConversation);
        localStorage.removeItem('guideMode_conversation');
        localStorage.removeItem('guideMode_timestamp');
      }
    } catch (error) { console.warn('Failed to load conversation from localStorage:', error); }
    return [];
  }

  // --- Chat Management Functions ---
  function clearConversation() {
    console.log("Clearing conversation completely");
    conversationHistory = [];
    localStorage.removeItem('guideMode_conversation');
    localStorage.removeItem('guideMode_timestamp');
    if (chatWindow) chatWindow.innerHTML = '';

    userContext = { skills: [], interests: [], experience: [], goals: [] };
    actionItems = [];
    if (contextPanel) { contextPanel.classList.add('hidden'); contextPanel.innerHTML = ''; }
    if (actionItemsPanel) { actionItemsPanel.classList.add('hidden'); actionItemsPanel.innerHTML = ''; }

    const welcomeMessage = "👋 Hi there! I'm your Career Advisor. I can help with career planning, resume advice, interview preparation, and more. What would you like to discuss today?";
    addMessageBubble(welcomeMessage, false);
    conversationHistory.push({ role: "model", parts: [{ text: welcomeMessage }] });
    saveConversationToLocalStorage();
  }

  // --- Authentication ---
  function checkAuthentication() {
    setTimeout(async () => {
      try {
        const user = await authService.getCurrentUser();
        if (!user) {
          console.warn("Authentication check failed");
          const redirectUrl = `/login?redirect=${encodeURIComponent(window.location.pathname + window.location.search)}`;
          window.location.href = redirectUrl;
        } else {
          console.log("User authenticated:", user.email);
        }
      } catch (error) {
        console.error("Authentication check error:", error);
        const redirectUrl = `/login?redirect=${encodeURIComponent(window.location.pathname + window.location.search)}`;
        window.location.href = redirectUrl;
      }
    }, 1000);
  }
  // Conditional auth check
  if (typeof authService !== "undefined" && authService) {
    document.addEventListener("DOMContentLoaded", checkAuthentication);
  } else {
    console.warn("authService not found, skipping authentication check.");
  }

  // --- DOM Initialization ---
  function initializeDOMElements() {
    form = document.getElementById("chat-form");
    input = document.getElementById("user-input") as HTMLInputElement | null;
    chatWindow = document.getElementById("chat-window");
    submitButton = document.getElementById("guide-mode-submit-button") as HTMLButtonElement | null;
    suggestionButtons = document.querySelectorAll("#prompt-suggestions button.prompt-suggestion-btn");
    topicButtons = document.querySelectorAll("#prompt-suggestions button.topic-category");
    voiceInputButton = document.getElementById("voice-input-button");
    promptSuggestionsContainer = document.getElementById("prompt-suggestions");
    contextPanel = document.getElementById("context-panel");
    actionItemsPanel = document.getElementById("action-items-panel");
    errorModalElement = document.getElementById("guideModeErrorModal");
    newChatButton = document.getElementById("new-chat-button");
    showSuggestionsButton = document.getElementById("show-suggestions-button");
  }

  // --- Message Formatting and Display ---
  function formatMarkdown(text: string): string {
    return text
      .replace(/</g, "<").replace(/>/g, ">")
      .replace(/\*\*(.*?)\*\*/g, "<strong class='font-semibold'>$1</strong>")
      .replace(/\*(.*?)\*/g, "<em class='italic'>$1</em>")
      .replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-blue-500 dark:text-blue-400 hover:underline transition-colors">$1</a>')
      .replace(/\n\n/g, "</p><p class='mt-3'>")
      .replace(/\n/g, "<br>");
  }

  function addMessageBubble(message: string, isUser: boolean): void {
    if (!chatWindow) { console.error("Chat window element not found!"); return; }

    const messageWrapper = document.createElement("div");
    messageWrapper.className = `flex w-full ${isUser ? "justify-end animate-slide-in-right" : "justify-start animate-fade-in"}`; // mb-3 removed, using gap on #chat-window
    messageWrapper.setAttribute("data-message-type", isUser ? "user" : "ai");

    const bubble = document.createElement("div");
    bubble.className = `p-3 rounded-full text-sm ${isUser ? "bg-gradient-to-r from-blue-500 to-indigo-500 text-white max-w-[80%] sm:max-w-[70%] text-left" : "text-gray-800 dark:text-gray-200 max-w-[85%] sm:max-w-[75%] text-left"}`;
    
    const contentDiv = document.createElement("div");
    contentDiv.className = "message-content"; 
    contentDiv.innerHTML = `<p>${formatMarkdown(message)}</p>`; 
    bubble.appendChild(contentDiv);

    if (!isUser) {
      const resourcesSection = enhanceWithResources(message);
      if (resourcesSection) bubble.appendChild(resourcesSection);
    }
    
    messageWrapper.appendChild(bubble);
    chatWindow.appendChild(messageWrapper);

    setTimeout(() => { if (chatWindow) chatWindow.scrollTop = chatWindow.scrollHeight; }, 50);

    if (!isUser) {
      updateUserContext(message);
      extractActionItems(message);
    }
  }

  function addThinkingBubble(): HTMLDivElement | null {
    if (!chatWindow) { console.error("Chat window not found for thinking bubble."); return null; }
    const thinkingBubble = document.createElement("div");
    thinkingBubble.className = "flex justify-start thinking-bubble animate-fade-in w-full"; // mb-3 removed
    thinkingBubble.setAttribute("data-message-type", "thinking");
    thinkingBubble.innerHTML = `
      <div class="max-w-[80%] sm:max-w-[75%] p-3 rounded-xl bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 shadow-sm">
        <div class="h-3 bg-gray-200 dark:bg-gray-600 rounded-full w-20 animate-pulse"></div>
      </div>`;
    chatWindow.appendChild(thinkingBubble);
    setTimeout(() => { if (chatWindow) chatWindow.scrollTop = chatWindow.scrollHeight; }, 50);
    return thinkingBubble;
  }
  function removeThinkingBubble(): void {
    const thinkingBubble = chatWindow?.querySelector(".thinking-bubble");
    if (thinkingBubble) thinkingBubble.remove();
  }

  // --- Feature Functions (Context, Action Items, Resources) ---
  function updateUserContext(aiResponse: string): void { /* ... (Copy existing logic) ... */ }
  function updateContextPanel(): void { /* ... (Copy existing logic) ... */ }
  function extractActionItems(aiResponse: string): boolean { /* ... (Copy existing logic) ... */ return false; }
  function updateActionItemsUI(): void { /* ... (Copy existing logic, ensure querySelectorAll for checkboxes is relative to actionItemsPanel) ... */ }
  function enhanceWithResources(aiResponse: string): HTMLDivElement | null { return null; /* ... (Copy existing logic) ... */ }

  // --- Message Sending Logic ---
  async function sendMessage(message: string, isInitialQuery: boolean = false, fromDashboard: boolean = false): Promise<void> {
    if (!message || message.trim() === "") return;
    if (promptSuggestionsContainer && !promptSuggestionsContainer.classList.contains("hidden")) {
      promptSuggestionsContainer.classList.add("hiding");
      setTimeout(() => {
        if (promptSuggestionsContainer) promptSuggestionsContainer.classList.add("hidden");
        if (showSuggestionsButton) showSuggestionsButton.classList.add("visible");
      }, 300);
    }

    if (!isInitialQuery || fromDashboard) {
      const messageAlreadyInHistory = conversationHistory.some(item => item.role === "user" && item.parts[0].text === message);
      if (!messageAlreadyInHistory) conversationHistory.push({ role: "user", parts: [{ text: message }] });
      
      const messageAlreadyDisplayed = Array.from(chatWindow?.childNodes || []).some(
          node => node instanceof Element && node.getAttribute('data-message-type') === 'user' && node.textContent?.includes(message)
      );
      if (!messageAlreadyDisplayed) addMessageBubble(message, true);
      saveConversationToLocalStorage();
    }

    if (input instanceof HTMLInputElement) input.value = "";
    if (submitButton) { submitButton.disabled = true; submitButton.classList.add("opacity-70"); }
    const thinkingNode = addThinkingBubble();

    try {
      let authToken = '';
      if (firebaseInstance && firebaseInstance.auth) {
        const user = await getCurrentUser(firebaseInstance.auth);
        if (user) authToken = await user.getIdToken();
      }
      const isNewChat = fromDashboard ? true : conversationHistory.filter(m => m.role === 'user').length <= 1;
      const data = await sendGuideModeMessage(message, [], authToken, "careerDiscussion", false, isNewChat);
      
      if (thinkingNode) removeThinkingBubble(); // Remove specific thinking node

      if (data.success && data.data) {
        addMessageBubble(data.data, false);
        conversationHistory.push({ role: "model", parts: [{ text: data.data }] });
        saveConversationToLocalStorage();
      } else {
        const errorToShow = data.error || "Sorry, something went wrong.";
        addMessageBubble(`Error: ${errorToShow}`, false);
        if (errorModalElement) {
          const modalInstance = (window as any).errorModals?.[errorModalElement.id];
          if(modalInstance) {
            modalInstance.setMessage(errorToShow);
            modalInstance.open();
          }
        }
      }
    } catch (error) {
      if (thinkingNode) removeThinkingBubble();
      const errorMessage = error instanceof Error ? error.message : "Could not connect to AI service.";
      addMessageBubble(`Error: ${errorMessage}`, false);
      console.error("Error calling guide mode API:", error);
    } finally {
      if (submitButton) { submitButton.disabled = false; submitButton.classList.remove("opacity-70"); }
    }
  }

  // --- Speech Recognition ---
  let recognition: any;
  let isRecognizing = false;
  function setupSpeechRecognition() {
    if (!voiceInputButton || !input) return;
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (!SpeechRecognition) {
      voiceInputButton.style.display = "none";
      console.warn("Speech Recognition not supported.");
      return;
    }
    try {
      recognition = new SpeechRecognition();
      recognition.continuous = false;
      recognition.lang = "en-US";
      recognition.interimResults = false;
      recognition.maxAlternatives = 1;

      voiceInputButton.addEventListener("click", () => {
        if (isRecognizing) { recognition.stop(); return; }
        try { input.placeholder = "Listening..."; recognition.start(); }
        catch (e) { console.error("Speech recognition start error:", e); input.placeholder = "Ask me anything..."; }
      });
      recognition.onstart = () => { isRecognizing = true; voiceInputButton.classList.add("text-red-500", "animate-pulse"); };
      recognition.onresult = (event: any) => { input.value = event.results[0][0].transcript; input.focus(); };
      recognition.onerror = (event: any) => { console.error("Speech recognition error:", event.error); input.placeholder = "Ask me anything..."; };
      recognition.onend = () => { isRecognizing = false; input.placeholder = "Ask me anything..."; voiceInputButton.classList.remove("text-red-500", "animate-pulse"); };
    } catch (err) { console.error("Failed to initialize SpeechRecognition:", err); voiceInputButton.style.display = "none"; }
  }

  // --- Event Listeners Setup ---
  function setupEventListeners() {
    if (form && input) {
      input.addEventListener("keydown", (e) => { if (e.key === "Enter" && !e.shiftKey) { e.preventDefault(); const msg = input.value.trim(); if (msg) sendMessage(msg); } });
      form.addEventListener("submit", (e) => { e.preventDefault(); const msg = input.value.trim(); if (msg) sendMessage(msg); });
    }

    const handleSuggestionOrTopicClick = (element: Element) => {
        const topicAttr = element.getAttribute('data-topic');
        const text = topicAttr ? `Tell me about ${topicAttr.toLowerCase()}` : element.textContent?.trim();
        if (text) { sendMessage(text); if (input) input.focus(); }
    };
    suggestionButtons.forEach(button => button.addEventListener("click", () => handleSuggestionOrTopicClick(button)));
    topicButtons.forEach(button => button.addEventListener("click", () => handleSuggestionOrTopicClick(button)));
    
    if (newChatButton) newChatButton.addEventListener("click", () => {
        if (conversationHistory.length > 1 && confirm("Start a new chat? This will clear current conversation.")) {
            clearConversation();
        } else if (conversationHistory.length <=1 ) {
            clearConversation();
        }
    });
    if (showSuggestionsButton && promptSuggestionsContainer) {
      showSuggestionsButton.addEventListener("click", () => {
        promptSuggestionsContainer.classList.remove("hidden", "hiding");
        showSuggestionsButton.classList.remove("visible"); // This should hide itself
      });
    }
    setupSpeechRecognition();
  }

  // --- Page Initialization ---
  document.addEventListener("DOMContentLoaded", () => {
    initializeDOMElements();
    setupEventListeners();

    const urlParams = new URLSearchParams(window.location.search);
    const fromDashboard = urlParams.get('from') === 'dashboard';

    if (fromDashboard) {
      localStorage.removeItem('guideMode_conversation');
      localStorage.removeItem('guideMode_timestamp');
      sessionStorage.setItem('fromDashboard', 'true');
    } else if (sessionStorage.getItem('fromDashboard') === 'true') {
      sessionStorage.removeItem('fromDashboard');
    }

    const savedConversation = loadConversationFromLocalStorage();
    let conversationInitialized = false;

    if (savedConversation && savedConversation.length > 0) {
      conversationHistory = savedConversation;
      if(chatWindow) chatWindow.innerHTML = ''; // Clear before repopulating
      savedConversation.forEach(msg => addMessageBubble(msg.parts[0].text, msg.role === "user"));
      conversationInitialized = true;
    }

    if (!conversationInitialized) {
      const win = window as any;
      if (win.lastQ && win.lastA) {
        addMessageBubble(win.lastQ, true);
        conversationHistory.push({ role: "user", parts: [{ text: win.lastQ }] });
        addMessageBubble(win.lastA, false);
        conversationHistory.push({ role: "model", parts: [{ text: win.lastA }] });
        saveConversationToLocalStorage();
        conversationInitialized = true;
      } else if (win.initialQuery) {
        const query = win.initialQuery;
        if (fromDashboard && chatWindow) chatWindow.innerHTML = ''; // Clean slate for dashboard
        
        addMessageBubble(query, true);
        conversationHistory.push({ role: "user", parts: [{ text: query }] });
        // `sendMessage` will handle saving after AI response.
        setTimeout(() => sendMessage(query, true, fromDashboard), 100);
        conversationInitialized = true;
      }
    }

    if (!conversationInitialized) {
      clearConversation(); // This adds the welcome message
    }

    // Update context/actions from last loaded message if applicable
    if (conversationHistory.length > 0) {
        const lastMessage = conversationHistory[conversationHistory.length - 1];
        if (lastMessage.role === 'model') {
            updateUserContext(lastMessage.parts[0].text);
            extractActionItems(lastMessage.parts[0].text);
        }
    }
  });
</script>

<style>
  /* Animations */
  @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
  @keyframes slideInRight { from { opacity: 0; transform: translateX(10px); } to { opacity: 1; transform: translateX(0); } }
  .animate-fade-in { animation: fadeIn 0.3s ease-out forwards; }
  .animate-slide-in-right { animation: slideInRight 0.3s ease-out forwards; }

  #chat-interface-wrapper {
    display: flex;
    flex-direction: column;
    max-height: 70vh;
    overflow: hidden;
    background-color: transparent; /* Tailwind gray-100 */
    @apply px-3 lg:px-0;
  }
  .dark #chat-interface-wrapper { background-color: #1f2937; /* Tailwind gray-800 */ }

  .chat-panel {
    width: 100%;
    max-width: 60rem; /* Tailwind max-w-3xl */
    margin: auto;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    min-height: 0;
@apply bg-white dark:bg-gray-900;
    border-radius: 1.5rem; /* Tailwind rounded-3xl */
    /* box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05); */
    overflow: hidden;
@apply border border-gray-200 dark:border-gray-800;
    margin-top: 1rem;
    margin-bottom: 1rem;
    height: calc(100vh - 2rem); /* Adjust if you have global headers/footers */
    @apply shadow-lg;
  
  }
  .dark .chat-panel { background-color: #374151; /* Tailwind gray-700 */ }

  #prompt-suggestions {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb; /* Tailwind gray-200 */
    flex-shrink: 0;
    transition: all 0.3s ease-out;
    max-height: 500px;
    opacity: 1;
  }
  .dark #prompt-suggestions { border-bottom-color: #4b5563; /* Tailwind gray-600 */ }
  #prompt-suggestions.hiding { max-height: 0; opacity: 0; padding-top: 0; padding-bottom: 0; margin-bottom:0; overflow: hidden; border-bottom: none;}

  .chat-area-container {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    min-height: 0;
    max-height: 100vh;
    position: relative;
    padding: 0.75rem; /* Tailwind p-3 */
  }
  
  .chat-area-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem; /* Tailwind mb-4 */
    flex-shrink: 0;
  }

  #chat-window {
    flex-grow: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0.5rem 0.25rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem; /* Tailwind space-y-3 through gap */
  }
  #chat-window .message-content p,
  #chat-window .message-content div {
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    text-align: left;
    line-height: 1.6;
  }
   #chat-window .message-content a { display: inline-block; max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }


  #chat-form-wrapper {
    padding-top: 0.75rem;
@apply border-t border-gray-200 dark:border-gray-600;
    flex-shrink: 0;
    margin-top: auto; /* Pushes form to bottom if chat-window is short */
  }
  .dark #chat-form-wrapper { border-top-color: #4b5563; /* Tailwind gray-600 */ }
  
  #chat-form {
    display: flex;
    align-items: center;
    gap: 0.75rem; /* Tailwind gap-3 */
    padding: 0.5rem; /* Tailwind p-2 */
    border-radius: 9999px; /* Tailwind rounded-full */
@apply bg-gray-50 dark:bg-gray-800;

  }
  
  #user-input {
    flex-grow: 1;
    padding: 0.5rem 0.75rem; /* Tailwind px-3 py-2 */
    font-size: 0.875rem; /* Tailwind text-sm */
    border: none;
    outline: none;
    background-color: transparent;
  }

  #show-suggestions-button {
    position: absolute; top: calc(0.75rem + 0.5rem); /* chat-area-container padding + desired offset */
    right: 0.75rem; /* chat-area-container padding */
    z-index: 10;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-out;
    pointer-events: none;
  }
  #show-suggestions-button.visible {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }
</style>

<section id="chat-interface-wrapper">
  <div class="chat-panel">
    <!-- Combined Topics and Suggestions Section -->
    <div id="prompt-suggestions" class="bg-gray-50 dark:bg-gray-900">
      <div>
        <div class="text-xs text-gray-500 dark:text-gray-400 mb-2">Common Questions</div>
        <div class="flex flex-nowrap md:flex-wrap gap-2 pb-1 overflow-x-auto">
          {promptSuggestions.map(suggestion => (
            <button class="prompt-suggestion-btn flex-shrink-0 flex items-center gap-1.5 px-3 py-1.5 rounded-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 text-xs text-gray-700 dark:text-gray-200 transition whitespace-nowrap">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              {suggestion}
            </button>
          ))}
        </div>
      </div>
    </div>

    <!-- Main Chat Area -->
    <div class="chat-area-container">
      <!-- <button id="show-suggestions-button" class="text-xs px-2 py-1 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center gap-1 shadow-sm">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
        </svg>
        <span>Topics</span>
      </button> -->
      
      <div class="chat-area-header">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          <span id="context-text">Chat</span>
        </div>
        <button id="new-chat-button" class="text-xs px-3 py-1.5 rounded-md bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 flex items-center gap-1.5">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
          </svg>
          New Chat
        </button>
      </div>

      <div id="context-panel" class="hidden flex-shrink-0 mb-2"></div>
      <div id="action-items-panel" class="hidden flex-shrink-0 mb-2"></div>

      <div id="chat-window">
        <!-- Messages will be appended here by JavaScript -->
      </div>

      <div id="chat-form-wrapper">
        <form id="chat-form">
          <button type="button" id="voice-input-button" class="p-2 rounded-full text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 flex-shrink-0" aria-label="Use voice input">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 14a3 3 0 003-3V5a3 3 0 00-6 0v6a3 3 0 003 3zm5.71-3a1 1 0 00-1.42 0A4.993 4.993 0 0113 15.93V19h-2v-3.07A4.993 4.993 0 017.71 11a1 1 0 00-1.42 0 7.004 7.004 0 006.71 6.93V21h.01a1 1 0 00.99.99.989.989 0 00.99-.99V17.93A7.004 7.004 0 0017.71 11z"/>
            </svg>
          </button>
          <input type="text" id="user-input" placeholder="Ask me anything..." class="text-gray-800 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500" />
          <button type="submit" id="guide-mode-submit-button" class="p-2.5 rounded-full bg-blue-600 hover:bg-blue-700 text-white flex-shrink-0" aria-label="Send message">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </button>
        </form>
      </div>
    </div>
  </div>
</section>

<ErrorModal id="guideModeErrorModal" title="Error" message="An error occurred." isOpen={false} zIndex={60} />
<UpgradePrompt id="guideModeUpgradePrompt" featureName="AI career advisor" isOpen={false} />