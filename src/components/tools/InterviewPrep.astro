---
import UpgradePrompt from "../UpgradePrompt.astro";
import ErrorModal from "../ErrorModal.astro";
import ButtonLoader from "../ButtonLoader.astro";
---

<section class="relative py-16 sm:py-24 lg:py-32 bg-white dark:bg-gray-950">
  <div
    aria-hidden="true"
    class="absolute inset-0 grid grid-cols-2 -space-x-52 opacity-0 dark:opacity-0 transition-opacity duration-300 ease-in-out"
  >
    <div
      class="blur-[106px] h-56 bg-gradient-to-br from-primary to-purple-400 dark:from-blue-700 dark:to-indigo-600 opacity-40 dark:opacity-20"
    >
    </div>
    <div
      class="blur-[106px] h-32 bg-gradient-to-r from-cyan-400 to-sky-300 dark:to-indigo-600 dark:from-emerald-500 opacity-40 dark:opacity-20"
    >
    </div>
  </div>
  <div class="relative w-full">
    <div class="container mx-auto px-3 md:px-6 lg:px-8 max-w-6xl">
      <div class="w-full mx-auto">
        <form
          id="interviewPrepForm"
          class="w-full space-y-6 sm:space-y-8 bg-white dark:bg-gray-900/80 backdrop-blur-md rounded-3xl p-4 sm:p-8
                    shadow-xl border-gray-100 border dark:border-gray-700/50
                    transition-all duration-300 ease-in-out hover:shadow-2xl"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-6">
            <!-- Company Name -->
            <div class="space-y-1.5 sm:space-y-2">
              <label
                for="companyName"
                class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                Company Name <span class="text-red-600"> *</span>
              </label>
              <input
                type="text"
                id="companyName"
                name="companyName"
                placeholder="Enter the company name (e.g., Google, Amazon)"
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-700/70 bg-white dark:bg-gray-800/30 rounded-xl
                                dark:text-gray-100 focus:border-primary dark:focus:border-primary focus:ring-1 focus:ring-primary
                                outline-none text-md transition-colors duration-200"
                required
              />
            </div>

            <!-- Job Role -->
            <div class="space-y-1.5 sm:space-y-2">
              <label
                for="jobRole"
                class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                Job Role <span class="text-red-600"> *</span>
              </label>
              <input
                type="text"
                id="jobRole"
                name="jobRole"
                placeholder="Enter the job role (e.g., Software Engineer, Product Manager)"
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-700/70 bg-white dark:bg-gray-800/30 rounded-xl
                                dark:text-gray-100 focus:border-primary dark:focus:border-primary focus:ring-1 focus:ring-primary
                                outline-none text-md transition-colors duration-200"
                required
              />
            </div>
          </div>

          <!-- Resume -->
          <div class="space-y-4 sm:space-y-5">
            <div class="flex justify-between items-center">
              <label
                class="block text-left text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                Resume <span class="text-gray-500 dark:text-gray-400">(Optional)</span>
              </label>
            </div>

            <!-- Hidden file input -->
            <input type="file" id="resumeFileInput" style="display: none;" />

            <!-- Resume Options Selector -->
            <div
              id="resumeOptionsSelector"
              class="grid grid-cols-3 md:grid-cols-3 xs:gap-2 sm:gap-4 md:gap-4 lg:gap-4 gap-2"
            >
              <button
                type="button"
                id="resumeFileUploadButton"
                class="relative flex flex-col items-center justify-center p-2 border-2 border-gray-200 dark:border-gray-700 rounded-xl bg-white/10 dark:bg-gray-800/20 hover:bg-gray-50 dark:hover:bg-gray-700/40 transition-all duration-300 group"
              >
                <div
                  class="flex flex-col items-center justify-center space-y-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-7 w-7 text-primary group-hover:text-primary-dark transition-colors duration-300"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    ></path>
                  </svg>
                  <span class="font-medium text-gray-900 dark:text-white"
                    >Upload File</span
                  >
                  <span class="text-xs text-gray-500 dark:text-gray-400 mt-1"
                    >PDF, DOCX, TXT</span
                  >
                </div>
              </button>

              <button
                type="button"
                id="importResumeButton"
                class="relative flex flex-col items-center justify-center p-2 border-2 border-gray-200 dark:border-gray-700 rounded-xl bg-white/10 dark:bg-gray-800/20 hover:bg-gray-50 dark:hover:bg-gray-700/40 transition-all duration-300 group"
              >
                <div
                  class="flex flex-col items-center justify-center space-y-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-7 w-7 text-primary group-hover:text-primary-dark transition-colors duration-300"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
                    ></path>
                  </svg>
                  <span class="font-medium text-gray-900 dark:text-white"
                    >Resume Manager</span
                  >
                  <span class="text-xs text-gray-500 dark:text-gray-400 mt-1"
                    >Select saved resume</span
                  >
                </div>
              </button>

              <button
                type="button"
                id="enterManuallyButton"
                class="relative flex flex-col items-center justify-center p-2 border-2 border-gray-200 dark:border-gray-700 rounded-xl bg-white/10 dark:bg-gray-800/20 hover:bg-gray-50 dark:hover:bg-gray-700/40 transition-all duration-300 group"
              >
                <div
                  class="flex flex-col items-center justify-center space-y-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-7 w-7 text-primary group-hover:text-primary-dark transition-colors duration-300"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                    ></path>
                  </svg>
                  <span class="font-medium text-gray-900 dark:text-white"
                    >Enter Manually</span
                  >
                  <span class="text-xs text-gray-500 dark:text-gray-400 mt-1"
                    >Paste or type resume</span
                  >
                </div>
              </button>
            </div>

            <!-- File Added UI -->
            <div
              id="resumeFileAddedUI"
              class="hidden w-full p-6 border-2 border-gray-300 dark:border-gray-700/70 rounded-xl bg-white/10 dark:bg-gray-800/20 min-h-[100px]"
            >
              <div class="flex items-center justify-center mb-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-10 w-10 text-green-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <p
                id="resumeFileName"
                class="text-center text-lg text-gray-900 dark:text-white font-medium"
              >
                Resume added successfully
              </p>
              <button
                id="changeResumeSource"
                class="mt-4 px-4 py-2 bg-black text-white dark:bg-white dark:text-black rounded-full text-sm hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200"
              >
                Upload Another
              </button>
            </div>

            <!-- Manual Entry UI -->
            <div id="manualEntryContainer" class="hidden w-full">
              <textarea
                id="resumeContent"
                name="resumeContent"
                rows="10"
                placeholder="Paste or type your resume here..."
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-700/70 bg-white dark:bg-gray-800/30 rounded-xl
                                dark:text-gray-100 focus:border-primary dark:focus:border-primary focus:ring-1 focus:ring-primary
                                outline-none text-md transition-colors duration-200 resize-y"
              ></textarea>
              <div class="flex justify-end mt-2">
                <button
                  id="cancelManualEntry"
                  class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                >
                  Back to options
                </button>
              </div>
            </div>

            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              This helps us generate more relevant interview questions specific
              to your background.
            </p>
          </div>

          <!-- Submit Button -->
          <div
            class="flex flex-col sm:flex-row justify-center items-center space-y-2.5 sm:space-y-0 sm:space-x-4 pt-4"
          >
            <button
              type="submit"
              id="generateQuestionsBtn"
              class="w-full sm:w-auto inline-flex h-11 sm:h-12 items-center justify-center px-6 sm:px-8 py-2 sm:py-3
                                text-sm sm:text-base font-semibold
                                text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700
                                dark:text-white dark:bg-gradient-to-r dark:from-blue-500 dark:to-indigo-500 dark:hover:from-blue-600 dark:hover:to-indigo-600
                                rounded-full
                                transition-all duration-300 ease-in-out
                                transform hover:scale-[1.03] active:scale-95
                                focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900
                                shadow-lg hover:shadow-xl"
            >
              <ButtonLoader
                id="generateButtonLoader"
                isLoading={false}
                loadingText="Generating..."
                spinnerPosition="left"
                spinnerSize="md"
                spinnerColor="white"
              >
                <svg
                  class="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                  ><path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg
                >
                Generate Questions
              </ButtonLoader>
            </button>
            <button
              type="button"
              id="clearForm"
              class="w-full sm:w-auto inline-flex h-11 sm:h-12 items-center justify-center px-6 sm:px-8 py-2 sm:py-3
                                text-sm sm:text-base font-semibold
                                text-gray-700 bg-gray-100 hover:bg-gray-200
                                dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600
                                border border-transparent
                                rounded-full
                                transition-all duration-300 ease-in-out
                                transform hover:scale-[1.03] active:scale-95
                                focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-offset-gray-900
                                shadow-md hover:shadow-lg"
            >
              <svg
                class="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
                ><path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                ></path></svg
              >
              Clear
            </button>
          </div>

          <!-- Clear Form Button -->
          <div class="flex justify-center"></div>
        </form>
      </div>

      <div id="questionsResult" class="mt-6"></div>
      <div id="answerSection" class="mt-6 hidden"></div>
    </div>
  </div>
</section>

<!-- Upgrade Prompt Component -->
<UpgradePrompt
  id="interviewPrepUpgradePrompt"
  featureName="interview practice sessions"
  isOpen={false}
/>

<!-- Error Modal Component -->
<ErrorModal
  id="interviewPrepErrorModal"
  title="Error"
  message="An error occurred. Please try again."
  isOpen={false}
  zIndex={60}
/>

<!-- Modal Container -->
<div
  id="answerModal"
  class="fixed inset-0 z-50 hidden"
>
  <!-- Overlay -->
  <div
    class="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity duration-300 ease-in-out"
    aria-hidden="true"
    onclick="closeModal()"
  >
  </div>

  <!-- Modal Content -->
  <div
    class="relative bg-white dark:bg-gray-900 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700/50 w-[95vw] sm:w-[90vw] md:w-[80vw] lg:w-[70vw] max-w-4xl max-h-[90vh] flex flex-col transition-all duration-300 ease-in-out overflow-hidden"
  >
    <!-- Header -->
    <div
      class="flex justify-between items-center p-4 sm:p-5 border-b border-gray-200 dark:border-gray-700"
    >
      <h2
        class="text-xl sm:text-2xl font-semibold text-gray-900 dark:text-white"
      >
        Practice Your Answer
      </h2>
      <button
        id="closeModalBtnTop"
        class="p-1.5 text-gray-500 hover:text-gray-900 dark:hover:text-white rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
        onclick="closeModal()"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 sm:h-6 sm:w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          stroke-width="2"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <span class="sr-only">Close modal</span>
      </button>
    </div>

    <!-- Body -->
    <div class="flex-grow overflow-y-auto p-4 sm:p-6">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Left Column: Question & Answer Input -->
        <div class="space-y-5">
          <div
            class="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl border border-gray-100 dark:border-gray-700/50"
          >
            <h3
              id="modalQuestion"
              class="font-semibold text-lg text-gray-900 dark:text-white mb-1.5 text-left"
            >
              {/* Question will be populated by JS */}
            </h3>
            <p
              id="modalContext"
              class="text-sm text-gray-600 dark:text-gray-400 text-left"
            >
              {/* Context will be populated by JS */}
            </p>
          </div>

          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <label
                for="modalAnswerText"
                class="block text-base font-medium text-gray-900 dark:text-white"
              >
                Your Answer
              </label>
              <button
                id="toggleStarTips"
                class="text-sm text-primary dark:text-primary-light hover:underline focus:outline-none"
                type="button"
              >
                Show STAR Method Tips
              </button>
            </div>
            <div class="relative">
              <textarea
                id="modalAnswerText"
                rows="8"
                placeholder="Type or use the microphone to record your answer using the STAR method:
- Situation: Describe the context
- Task: Explain what you needed to do
- Action: Detail the steps you took
- Result: Share the outcome and what you learned"
                class="w-full px-4 py-3 pr-12 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark focus:border-transparent placeholder:text-gray-400 dark:placeholder:text-gray-500 transition-colors duration-200"
              ></textarea>
              <button
                id="voiceInputBtn"
                class="absolute top-3 right-3 p-3 text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                title="Record Answer"
              >
                <svg
                  id="micIcon"
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z"
                    clip-rule="evenodd"></path>
                </svg>
                <svg
                  id="stopIcon"
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6 hidden text-red-500 animate-pulse"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z"
                    clip-rule="evenodd"></path>
                </svg>
              </button>
            </div>
            <p
              id="recordingStatus"
              class="text-xs text-gray-500 dark:text-gray-400 h-4"
            >
            </p>
            <p
              id="speechError"
              class="text-xs text-red-600 dark:text-red-400 h-4"
            >
            </p>

            <!-- STAR Method Tips -->
            <div id="starMethodTips" class="hidden mt-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/30">
              <h4 class="font-semibold text-blue-700 dark:text-blue-300 mb-2">STAR Method Tips</h4>
              <ul class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                <li>
                  <span class="font-medium text-blue-600 dark:text-blue-400">Situation:</span>
                  Describe the context and background. Where were you working? What was your role?
                </li>
                <li>
                  <span class="font-medium text-blue-600 dark:text-blue-400">Task:</span>
                  Explain the challenge or responsibility you faced. What needed to be done and why?
                </li>
                <li>
                  <span class="font-medium text-blue-600 dark:text-blue-400">Action:</span>
                  Detail the specific steps you took to address the task. Focus on YOUR contribution.
                </li>
                <li>
                  <span class="font-medium text-blue-600 dark:text-blue-400">Result:</span>
                  Share the outcome of your actions. Quantify results when possible and include what you learned.
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Right Column: Feedback -->
        <div id="modalFeedbackContainer" class="space-y-5">
          <div
            class="p-6 bg-gray-50 dark:bg-gray-800/50 rounded-xl border border-gray-100 dark:border-gray-700/50 h-full flex items-center justify-center"
          >
            <p class="text-gray-500 dark:text-gray-400 text-center">
              Submit your answer to get feedback.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div
      class="flex flex-col sm:flex-row justify-end items-center p-4 sm:p-5 border-t border-gray-200 dark:border-gray-700 space-y-3 sm:space-y-0 sm:space-x-3"
    >
      <button
        onclick="closeModal()"
        class="w-full sm:w-auto px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 text-sm sm:text-base"
      >
        Cancel
      </button>
      <button
        id="modalSubmitBtn"
        class="w-full sm:w-auto px-5 py-2 bg-black text-white dark:bg-white dark:text-black rounded-lg hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors duration-200 flex items-center justify-center text-sm sm:text-base font-medium"
      >
        <span id="submitBtnText">Submit for Review</span>
        <svg
          id="submitBtnSpinner"
          class="animate-spin ml-2 h-4 w-4 text-white dark:text-black hidden"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      </button>
    </div>
  </div>
</div>

<style>
  textarea.border-red-500,
  input.border-red-500,
  button.border-red-500 {
    border-color: rgb(239 68 68);
  }

  #resumeOptionsSelector button.border-red-500:hover {
    border-color: rgb(239 68 68);
    background-color: rgba(239, 68, 68, 0.05);
  }

  .question-card {
    border: 1px solid #e5e7eb;
    border-radius: 1rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: white;
    transition: all 0.3s ease;
  }

  .dark .question-card {
    border-color: rgba(75, 85, 99, 0.4);
    background-color: rgba(31, 41, 55, 0.4);
  }

  .question-card:hover {
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .dark .question-card:hover {
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.2),
      0 2px 4px -1px rgba(0, 0, 0, 0.12);
  }

  .category-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
  }

  .dark .category-title {
    border-bottom-color: rgba(75, 85, 99, 0.4);
  }

  .feedback-card {
    border-radius: 1rem;
    padding: 1.5rem;
    margin-top: 1.5rem;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
  }

  .dark .feedback-card {
    background-color: rgba(31, 41, 55, 0.5);
    border-color: rgba(75, 85, 99, 0.4);
  }

  .feedback-section {
    margin-bottom: 1rem;
  }

  .feedback-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .feedback-list {
    list-style-type: disc;
    padding-left: 1.5rem;
  }

  .rating-pill {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-weight: 600;
    font-size: 0.875rem;
  }

  /* Modal display fixes */
  #answerModal.fixed:not(.hidden) {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  #resumeFileAddedUI:not(.hidden) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
</style>

<script>
  // Moved imports here to be available for the first script logic
  import { authService } from "../../lib/auth";
  import { ErrorHandler } from "../../lib/errorHandler";
  import { LoadingManager } from "../../lib/loadingManager";
  import { FormValidator } from "../../lib/formValidator";
  import { ComponentInitializer } from "../../lib/componentInitializer";
  import "../../scripts/interviewPrepOptimistic.js";

  // Define interfaces for Speech Recognition API if not available globally
  // Use 'any' for broader compatibility if specific types cause issues
  declare global {
    interface Window {
      SpeechRecognition: any;
      webkitSpeechRecognition: any;
      closeModal: () => void; // Declare closeModal globally
    }
  }

  interface SpeechRecognitionEvent extends Event {
    readonly resultIndex: number;
    readonly results: SpeechRecognitionResultList;
  }

  interface SpeechRecognitionErrorEvent extends Event {
    readonly error: string;
    readonly message: string;
  }

  interface SpeechRecognition extends EventTarget {
    grammars: any; // Use 'any' for SpeechGrammarList if type is unavailable
    lang: string;
    continuous: boolean;
    interimResults: boolean;
    maxAlternatives: number;
    serviceURI: string;

    start(): void;
    stop(): void;
    abort(): void;

    onaudiostart: ((this: SpeechRecognition, ev: Event) => any) | null;
    onaudioend: ((this: SpeechRecognition, ev: Event) => any) | null;
    onend: ((this: SpeechRecognition, ev: Event) => any) | null;
    onerror:
      | ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => any)
      | null;
    onnomatch:
      | ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any)
      | null;
    onresult:
      | ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any)
      | null;
    onsoundstart: ((this: SpeechRecognition, ev: Event) => any) | null;
    onsoundend: ((this: SpeechRecognition, ev: Event) => any) | null;
    onspeechstart: ((this: SpeechRecognition, ev: Event) => any) | null;
    onspeechend: ((this: SpeechRecognition, ev: Event) => any) | null;
    onstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  }

  interface SpeechRecognitionStatic {
    new (): SpeechRecognition;
  }

  let questionsData: any = null;
  let recognition: SpeechRecognition | null = null; // Use the defined interface
  let isRecording = false;
  // Store the text present before recording starts
  let initialTextBeforeRecording = "";

  // Check for browser support for Web Speech API
  const SpeechRecognitionAPI: SpeechRecognitionStatic | undefined =
    window.SpeechRecognition || window.webkitSpeechRecognition;
  const recognitionSupported = SpeechRecognitionAPI !== undefined;

  document.addEventListener("DOMContentLoaded", () => {
    const form = document.getElementById(
      "interviewPrepForm"
    ) as HTMLFormElement;
    const resultContainer = document.getElementById(
      "questionsResult"
    ) as HTMLDivElement;
    const clearButton = document.getElementById(
      "clearForm"
    ) as HTMLButtonElement;

    // Check for stored form data
    const storedData = localStorage.getItem("interviewPrepFormData");
    if (storedData) {
      try {
        const parsedData = JSON.parse(storedData);
        (document.getElementById("companyName") as HTMLInputElement).value =
          parsedData.companyName || "";
        (document.getElementById("jobRole") as HTMLInputElement).value =
          parsedData.jobRole || "";
        // Resume content is handled by the utility module
      } catch (error) {
        console.error("Error parsing stored form data:", error);
      }
    }

    // Event listener for clear button
    clearButton.addEventListener("click", () => {
      // Clear form fields
      form.reset();
      // Clear resume content using our utility
      if (window.resumeInputHandler) {
        window.resumeInputHandler.clearResumeContent();
      }
      // Clear result container
      resultContainer.innerHTML = "";
      // Clear stored form data
      localStorage.removeItem("interviewPrepFormData");
    });

    // Function to check user's tier access using ComponentInitializer
    async function checkInterviewPrepAccess() {
      try {
        // Use ComponentInitializer to check feature access
        const initializer = new ComponentInitializer("interviewPrep");
        const access = await initializer.checkFeatureAccess();

        // Disable submit button if usage limit is reached
        if (access.usageLimitReached) {
          const submitButton = document.getElementById("generateQuestionsBtn") as HTMLButtonElement;
          if (submitButton) {
            submitButton.disabled = true;
            submitButton.classList.add("opacity-50", "cursor-not-allowed");
            submitButton.title = "Usage limit reached. Please upgrade your plan.";
          }
        }

        return access;
      } catch (error) {
        console.error("Error checking access:", error);
        return { canAccess: false, userId: null };
      }
    }

    // Check access on page load
    checkInterviewPrepAccess();

    // Handle form submission
    form.addEventListener("submit", async (e) => {
      e.preventDefault();

      // Check tier status before generation
      const { canAccess, userId } = await checkInterviewPrepAccess();
      if (!canAccess) {
        return;
      }

      // Get form values
      const companyName = (
        document.getElementById("companyName") as HTMLInputElement
      ).value;
      const jobRole = (document.getElementById("jobRole") as HTMLInputElement)
        .value;
      // Get resume content using our utility module
      const resumeContent = window.resumeInputHandler ? window.resumeInputHandler.getResumeContent() : "";

      // Validate inputs using FormValidator static methods
      let isValid = true;

      // Validate company name
      const companyNameInput = document.getElementById("companyName") as HTMLInputElement;
      if (!FormValidator.validateInput(companyNameInput, { required: true })) {
        isValid = false;
      }

      // Validate job role
      const jobRoleInput = document.getElementById("jobRole") as HTMLInputElement;
      if (!FormValidator.validateInput(jobRoleInput, { required: true })) {
        isValid = false;
      }
 
       // Resume content is now optional, no validation needed here
       // The backend will handle cases where resume is empty
 
       if (!isValid) {
         return;
       }

      // Save form data to localStorage
      localStorage.setItem(
        "interviewPrepFormData",
        JSON.stringify({
          companyName,
          jobRole,
          // Don't need to save resumeContent as it's handled by the utility module
        })
      );

      // Show loading state with progress indicator
      resultContainer.innerHTML = `
                <div class="p-6 bg-white dark:bg-gray-800/30 rounded-xl border border-gray-200 dark:border-gray-700/50">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4 text-left">Interview Questions</h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">Searching the web to find the most up-to-date information about ${companyName} and the ${jobRole} position.</p>
                    <div id="progressContainer" class="mb-6">
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300" id="progressStatus">Analyzing your resume and job details...</span>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300" id="progressCounter">0/4 categories</span>
                        </div>
                        <ProgressBar
                          id="interviewProgressBar"
                          progress={0}
                          color="primary"
                          height="sm"
                          rounded="full"
                        />
                    </div>
                    <div id="questionsProgressContainer">
                        <!-- Categories will be added here as they're generated -->
                    </div>
                </div>
            `;

      try {
        // Track interview prep usage using ComponentInitializer
        if (userId === null) {
          throw new Error("User not authenticated");
        }

        // Track feature usage
        const initializer = new ComponentInitializer("interviewPrep");
        await initializer.trackFeatureUsage(userId);

        // Get ID token for API calls
        const currentUser = await authService.getCurrentUser();
        if (!currentUser) {
          throw new Error("User not authenticated");
        }


        // Initialize progress tracking
        const progressContainer = document.getElementById("progressContainer");
        const progressStatus = document.getElementById("progressStatus");
        const progressCounter = document.getElementById("progressCounter");
        const questionsProgressContainer = document.getElementById("questionsProgressContainer");

        // Categories to track
        const categories = ["companySpecific", "roleSpecific", "behavioral", "technical"];
        const categoryTitles = {
          companySpecific: "Company Specific",
          roleSpecific: "Role Specific",
          behavioral: "Behavioral",
          technical: "Technical",
        };

        // Create a LoadingManager instance
        const loadingManager = new LoadingManager("interviewPrep");

        // Function to update progress UI
        const updateProgress = (completedCategories: number) => {
          // Update progress bar using the loadingState utility
          import("../../lib/loadingState").then(({ updateProgress }) => {
            // Update progress bar using the utility
            const percentage = (completedCategories / categories.length) * 100;
            updateProgress("interviewProgressBar", percentage);

            if (progressCounter && progressStatus) {
              progressCounter.textContent = `${completedCategories}/${categories.length} categories`;

              // Update status message based on progress
              if (completedCategories === 0) {
                progressStatus.textContent = "Analyzing your resume and job details...";
              } else if (completedCategories === 1) {
                progressStatus.textContent = "Company-specific questions ready! Finding role-specific questions...";
              } else if (completedCategories === 2) {
                progressStatus.textContent = "Role-specific questions ready! Finding behavioral questions...";
              } else if (completedCategories === 3) {
                progressStatus.textContent = "Behavioral questions ready! Finding technical questions...";
              } else {
                progressStatus.textContent = "All questions ready!";
              }
            }
          }).catch(err => {
            console.error("Failed to import loading state utility:", err);

            // Fallback to direct DOM manipulation if import fails
            const progressBar = document.getElementById("interviewProgressBar");
            if (progressBar && progressCounter && progressStatus) {
              const percentage = (completedCategories / categories.length) * 100;
              progressBar.style.width = `${percentage}%`;
              progressCounter.textContent = `${completedCategories}/${categories.length} categories`;

              // Update status message based on progress
              if (completedCategories === 0) {
                progressStatus.textContent = "Analyzing your resume and job details...";
              } else if (completedCategories === 1) {
                progressStatus.textContent = "Company-specific questions ready! Finding role-specific questions...";
              } else if (completedCategories === 2) {
                progressStatus.textContent = "Role-specific questions ready! Finding behavioral questions...";
              } else if (completedCategories === 3) {
                progressStatus.textContent = "Behavioral questions ready! Finding technical questions...";
              } else {
                progressStatus.textContent = "All questions ready!";
              }
            }
          });
        };

        // Function to handle loading more questions for a category
        const handleLoadMore = async (category: string) => {
          // Find the load more button and update its state
          const categoryElement = document.querySelector(`[data-category-id="${category}"]`);
          if (!categoryElement) return;

          const loadMoreButton = categoryElement.querySelector(".load-more-button") as HTMLButtonElement;
          if (!loadMoreButton) return;

          // Show loading state using the LoadingManager utility
          loadingManager.setButtonLoading(loadMoreButton.id || "loadMoreButton", true, "Loading more questions...");

          // Show optimization status
          const loadMoreStatus = categoryElement.querySelector(".load-more-status");
          if (loadMoreStatus) {
            loadMoreStatus.classList.remove("hidden");
          }

          try {
            // Get existing questions for this category
            const existingQuestions = questionsData[category] || [];

            // Fetch more questions
            const response = await fetch(
              "/.netlify/functions/interview-questions",
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${idToken}`,
                },
                body: JSON.stringify({
                  companyName,
                  jobRole,
                  // Omit resume for load more requests to improve performance
                  resume: "", // Removing resume from follow-up requests
                  category: category,
                  loadMore: true,
                  existingQuestions: existingQuestions,
                }),
              }
            );

            if (!response.ok) {
              throw new Error(`API request failed with status ${response.status}`);
            }

            const data = await response.json();

            // Add new questions to the questionsData
            if (data[category] && data[category].length > 0) {
              // Append the new questions to the existing ones
              questionsData[category] = [...existingQuestions, ...data[category]];

              // Update the UI with the new questions
              addCategoryToUI(category, data[category], true);

              // Hide the optimization status message
              const loadMoreStatus = categoryElement.querySelector(".load-more-status");
              if (loadMoreStatus) {
                loadMoreStatus.classList.add("hidden");
              }
            } else {
              throw new Error("No additional questions were generated");
            }
          } catch (error) {
            console.error(`Error loading more questions for ${category}:`, error);

            // Reset the load more button using the loadingState utility
            import("../../lib/loadingState").then(({ setButtonLoading }) => {
              setButtonLoading(loadMoreButton.id || "loadMoreButton", false);
            }).catch(err => {
              console.error("Failed to import loading state utility:", err);

              // Fallback to direct DOM manipulation if import fails
              loadMoreButton.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
                Load More Questions
              `;
              loadMoreButton.disabled = false;
            });

            // Hide the optimization status message
            const loadMoreStatus = categoryElement.querySelector(".load-more-status");
            if (loadMoreStatus) {
              loadMoreStatus.classList.add("hidden");
            }

            // Show error message
            const categoryContent = categoryElement.querySelector(".category-content");
            if (categoryContent) {
              const errorElement = document.createElement("div");
              errorElement.className = "text-red-500 dark:text-red-400 text-sm text-center mt-2";
              errorElement.textContent = "Failed to load more questions. Please try again.";

              // Remove existing error message if any
              const existingError = categoryContent.querySelector(".text-red-500");
              if (existingError) {
                existingError.remove();
              }

              // Add the error message before the load more button
              const loadMoreContainer = categoryContent.querySelector(".load-more-button-container");
              if (loadMoreContainer) {
                loadMoreContainer.before(errorElement);

                // Remove the error message after 3 seconds
                setTimeout(() => {
                  errorElement.remove();
                }, 3000);
              }
            }
          }
        };

        // Function to add a category to the UI as it's generated
        const addCategoryToUI = (category: string, questions: any[], append = false) => {
          if (!questionsProgressContainer) return;

          const categoryTitle = categoryTitles[category as keyof typeof categoryTitles] || category;

          // If we're appending to an existing category
          if (append) {
            const existingCategory = document.querySelector(`[data-category-id="${category}"]`);
            if (existingCategory) {
              const categoryContent = existingCategory.querySelector(".category-content");
              if (categoryContent) {
                // Get the current number of questions
                const currentQuestionCount = categoryContent.querySelectorAll(".question-card").length;

                // Create HTML for new questions
                const newQuestionsHTML = questions.map((item, index) => `
                  <div class="question-card p-4 border border-gray-100 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors mb-3">
                    <div class="flex justify-between items-start">
                      <h4 class="font-medium text-lg text-gray-900 dark:text-white text-left">${currentQuestionCount + index + 1}. ${item.question}</h4>
                      <button
                        class="practice-button ml-4 px-3 py-1 bg-black text-white dark:bg-white dark:text-black rounded-full text-sm hover:bg-primary-dark transition-colors flex-shrink-0"
                        data-category="${category}"
                        data-index="${currentQuestionCount + index}"
                      >
                        Practice
                      </button>
                    </div>
                    ${item.context ? `<p class="mt-2 text-sm text-gray-600 dark:text-gray-300 text-left">${item.context}</p>` : ""}
                  </div>
                `).join("");

                // Remove the load more button if it exists
                const loadMoreButton = categoryContent.querySelector(".load-more-button-container");
                if (loadMoreButton) {
                  loadMoreButton.remove();
                }

                // Append new questions and load more button
                categoryContent.insertAdjacentHTML("beforeend", newQuestionsHTML);
                categoryContent.insertAdjacentHTML("beforeend", `
                  <div class="load-more-button-container flex justify-center mt-4">
                    <button
                      class="load-more-button px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors text-sm flex items-center"
                      data-category="${category}"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                      </svg>
                      Load More Questions
                    </button>
                  </div>
                `);

                // Add event listeners to new practice buttons
                categoryContent.querySelectorAll(".practice-button").forEach((button) => {
                  if (!button.hasAttribute("data-event-attached")) {
                    button.setAttribute("data-event-attached", "true");
                    button.addEventListener("click", (e) => {
                      const target = e.currentTarget as HTMLButtonElement;
                      const categoryName = target.getAttribute("data-category") || "";
                      const index = parseInt(target.getAttribute("data-index") || "0", 10);
                      showAnswerSection(categoryName, index);
                    });
                  }
                });

                // Add event listener to the new load more button
                const newLoadMoreButton = categoryContent.querySelector(".load-more-button");
                if (newLoadMoreButton) {
                  newLoadMoreButton.addEventListener("click", () => {
                    handleLoadMore(category);
                  });
                }

                return;
              }
            }
          }

          // Create a new category element
          const categoryElement = document.createElement("div");
          categoryElement.className = "category mb-6";
          categoryElement.setAttribute("data-category-id", category);
          categoryElement.innerHTML = `
            <div class="category-header flex justify-between items-center cursor-pointer p-4 bg-gray-50 dark:bg-gray-800/30 rounded-xl">
              <h3 class="text-xl font-semibold text-gray-900 dark:text-white">${categoryTitle}</h3>
              <svg class="category-chevron h-6 w-6 text-gray-500 dark:text-gray-400 transform transition-transform" viewBox="0 0 20 20" fill="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            <div class="category-content p-4">
              ${questions.map((item, index) => `
                <div class="question-card p-4 border border-gray-100 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors mb-3">
                  <div class="flex justify-between items-start">
                    <h4 class="font-medium text-lg text-gray-900 dark:text-white text-left">${index + 1}. ${item.question}</h4>
                    <button
                      class="practice-button ml-4 px-3 py-1 bg-black text-white dark:bg-white dark:text-black rounded-full text-sm hover:bg-primary-dark transition-colors flex-shrink-0"
                      data-category="${category}"
                      data-index="${index}"
                    >
                      Practice
                    </button>
                  </div>
                  ${item.context ? `<p class="mt-2 text-sm text-gray-600 dark:text-gray-300 text-left">${item.context}</p>` : ""}
                </div>
              `).join("")}

              <div class="load-more-button-container flex justify-center mt-4">
                <button
                  class="load-more-button px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors text-sm flex items-center"
                  data-category="${category}"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                  Load More Questions
                </button>
                <div class="load-more-status text-xs text-gray-500 dark:text-gray-400 mt-2 hidden">
                  Optimized for speed: Loading without resume data
                </div>
              </div>
            </div>
          `;

          questionsProgressContainer.appendChild(categoryElement);

          // Add event listeners to the new category header
          const header = categoryElement.querySelector(".category-header");
          const content = categoryElement.querySelector(".category-content");
          const chevron = categoryElement.querySelector(".category-chevron");

          if (header && content && chevron) {
            header.addEventListener("click", () => {
              content.classList.toggle("hidden");
              if (content.classList.contains("hidden")) {
                chevron.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
              } else {
                chevron.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>';
              }
            });
          }

          // Add event listeners to practice buttons
          categoryElement.querySelectorAll(".practice-button").forEach((button) => {
            button.addEventListener("click", (e) => {
              const target = e.currentTarget as HTMLButtonElement;
              const categoryName = target.getAttribute("data-category") || "";
              const index = parseInt(target.getAttribute("data-index") || "0", 10);
              showAnswerSection(categoryName, index);
            });
          });

          // Add event listener to the load more button
          const loadMoreButton = categoryElement.querySelector(".load-more-button");
          if (loadMoreButton) {
            loadMoreButton.addEventListener("click", () => {
              handleLoadMore(category);
            });
          }
        };

        // Initialize data storage
        questionsData = {};
        let completedCategories = 0;

        // Usage already tracked at the beginning of the function
        // No need to track again

        // Make initial API request for company-specific questions
        // Ensure user is authenticated before making the call
        const user = await authService.getCurrentUser();
        if (!user) {
          throw new Error("Authentication required to generate interview questions.");
        }
        const idToken = await user.getIdToken();

        let response = await fetch(
          "/.netlify/functions/interview-questions",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${idToken}`, // Include the ID token
            },
            body: JSON.stringify({
              companyName,
              jobRole,
              resume: resumeContent,
            }),
          }
        );

        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        // Process the initial response (company-specific questions)
        let data = await response.json();

        // Add the first category to the UI immediately
        if (data.companySpecific) {
          questionsData.companySpecific = data.companySpecific;
          addCategoryToUI("companySpecific", data.companySpecific);
          completedCategories++;
          updateProgress(completedCategories);
        }

        // Fetch the remaining categories in sequence
        const remainingCategories = ["roleSpecific", "behavioral", "technical"];

        // Function to fetch a specific category
        const fetchCategory = async (category: string) => {
          try {
            // idToken is already available from the outer scope
            const categoryResponse = await fetch(
              "/.netlify/functions/interview-questions",
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${idToken}`, // Use the outer idToken
                },
                body: JSON.stringify({
                  companyName,
                  jobRole,
                  resume: resumeContent,
                  category: category,
                }),
              }
            );

            if (!categoryResponse.ok) {
              console.error(`Failed to fetch ${category} questions`);
              return null;
            }

            const categoryData = await categoryResponse.json();
            return categoryData;
          } catch (error) {
            console.error(`Error fetching ${category} questions:`, error);
            return null;
          }
        };

        // Fetch each remaining category and update the UI as they arrive
        for (const category of remainingCategories) {
          const categoryData = await fetchCategory(category);

          if (categoryData && categoryData[category]) {
            // Add this category to the UI
            questionsData[category] = categoryData[category];
            addCategoryToUI(category, categoryData[category]);

            // Update progress
            completedCategories++;
            updateProgress(completedCategories);
          }
        }

        // Hide progress container when all categories are loaded
        if (progressContainer && completedCategories === categories.length) {
          setTimeout(() => {
            if (progressContainer) {
              progressContainer.style.display = "none";
            }
          }, 1000);
        }

        // Scroll to results
        resultContainer.scrollIntoView({ behavior: "smooth" });
      } catch (error) {
        console.error("Error generating interview questions:", error);

        // Show error in the result container
        resultContainer.innerHTML = `
          <div class="p-4 bg-red-50 dark:bg-red-900/20 rounded-xl border border-red-200 dark:border-red-800/30">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <p>There was an error generating your interview questions. Please try again later.</p>
                </div>
              </div>
            </div>
          </div>
        `;

        // Show error modal using our centralized error handling
        import("../../lib/errorHandling").then(({ showErrorModal }) => {
          showErrorModal(
            "interviewPrepErrorModal",
            "Error Generating Questions",
            "There was an error generating your interview questions. Please try again later."
          );
        }).catch(err => {
          console.error("Failed to import error handling utility:", err);
        });
      }
    });

    // Function to display questions is now replaced by progressive loading

    // --- Voice Input Functions ---
    function setupSpeechRecognition() {
      if (!recognitionSupported || !SpeechRecognitionAPI) {
        console.warn("Speech Recognition not supported by this browser.");
        const voiceBtn = document.getElementById("voiceInputBtn");
        if (voiceBtn) voiceBtn.style.display = "none"; // Hide button if not supported
        return;
      }

      recognition = new SpeechRecognitionAPI(); // Instantiate using the API reference
      recognition.continuous = true; // Keep listening even after pauses
      recognition.interimResults = true; // Get results while speaking
      recognition.lang = "en-US"; // Set language

      recognition.onresult = (event: SpeechRecognitionEvent) => {
        let interim_transcript = "";
        let final_transcript_this_session = ""; // Build final transcript *for this session*

        // Iterate from the beginning of results for this recognition instance
        // to reconstruct the full transcript based on final/interim status.
        for (let i = 0; i < event.results.length; ++i) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            final_transcript_this_session += transcript + " ";
          } else {
            // Append all interim parts together
            interim_transcript += transcript;
          }
        }

        // Update the textarea with the initial text + session's final + current interim
        const answerTextArea = document.getElementById(
          "modalAnswerText"
        ) as HTMLTextAreaElement;
        if (answerTextArea) {
          // Trim the final part to avoid double spaces if interim is empty
          // Combine initial text, finalized text from this session, and current interim text
          answerTextArea.value =
            initialTextBeforeRecording +
            final_transcript_this_session.trim() +
            (interim_transcript ? " " + interim_transcript : "");
        }
        updateRecordingStatus(`Recording...`);
      };

      recognition.onerror = (event: any) => {
        // Changed to any
        const error = event.error || "unknown";
        console.error("Speech recognition error:", error);
        let errorMsg = `Speech recognition error: ${error}`;
        if (error === "not-allowed" || error === "service-not-allowed") {
          errorMsg =
            "Microphone access denied. Please allow microphone access in your browser settings.";
        } else if (error === "no-speech") {
          errorMsg = "No speech detected. Please try again.";
        } else if (error === "audio-capture") {
          errorMsg = "Microphone not found or not working.";
        }
        updateSpeechError(errorMsg);
        stopRecording(); // Ensure recording stops on error
      };

      recognition.onend = () => {
        // Only log if it ends while we expected it to be recording
        if (isRecording) {
          // If it ended unexpectedly, ensure UI is reset
          stopRecording(); // Clean up UI if it ended prematurely
        }
      };
    }

    function startRecording() {
      if (!recognitionSupported || !recognition) {
        updateSpeechError("Speech recognition not available.");
        return;
      }
      if (isRecording) return;

      // Store the text currently in the textarea before starting
      initialTextBeforeRecording =
        (document.getElementById("modalAnswerText") as HTMLTextAreaElement)
          ?.value || "";
      // Add a space if there's text and it doesn't end with one, for separation
      if (
        initialTextBeforeRecording &&
        !/\s$/.test(initialTextBeforeRecording)
      ) {
        initialTextBeforeRecording += " ";
      }

      try {
        recognition.start();
        isRecording = true;
        updateVoiceButtonState(true);
        updateRecordingStatus("Recording started...");
        updateSpeechError(""); // Clear previous errors
      } catch (e: any) {
        console.error("Error starting recognition:", e);
        let errorMsg = "Could not start recording.";
        if (e.name === "NotAllowedError") {
          errorMsg = "Microphone access denied. Please allow access.";
        } else if (e.name === "InvalidStateError") {
          errorMsg = "Recording already started.";
          // Force UI update in case state is inconsistent
          isRecording = true;
          updateVoiceButtonState(true);
        } else {
          errorMsg = `Could not start recording: ${e.message || e.name}`;
        }
        updateSpeechError(errorMsg);
        isRecording = false; // Ensure state is correct
        updateVoiceButtonState(false);
      }
    }

    function stopRecording() {
      if (!recognitionSupported || !recognition) return;
      if (!isRecording) return; // Prevent stopping if not recording

      recognition.stop(); // This will trigger onend eventually
      isRecording = false; // Set state immediately
      updateVoiceButtonState(false);
      updateRecordingStatus("Processing final result..."); // Indicate processing

      // The final result processing happens in onresult and onend.
      // Add a slight delay to update status after processing might complete.
      setTimeout(() => {
        const currentStatus =
          document.getElementById("recordingStatus")?.textContent || "";
        // Only update if it's still showing "Processing"
        if (currentStatus.includes("Processing")) {
          updateRecordingStatus("Recording stopped.");
        }
      }, 500); // Adjust delay if needed
    }

    function toggleRecording() {
      if (isRecording) {
        stopRecording();
      } else {
        startRecording();
      }
    }

    function updateVoiceButtonState(recording: boolean) {
      const micIcon = document.getElementById("micIcon");
      const stopIcon = document.getElementById("stopIcon");
      const voiceBtn = document.getElementById("voiceInputBtn");

      if (micIcon && stopIcon && voiceBtn) {
        micIcon.classList.toggle("hidden", recording);
        stopIcon.classList.toggle("hidden", !recording);
        voiceBtn.title = recording ? "Stop Recording" : "Record Answer";
      }
    }

    function updateRecordingStatus(message: string) {
      const statusEl = document.getElementById("recordingStatus");
      if (statusEl) statusEl.textContent = message;
    }

    function updateSpeechError(message: string) {
      const errorEl = document.getElementById("speechError");
      if (errorEl) errorEl.textContent = message;
    }

    // --- End Voice Input Functions ---

    // Function to show answer section
    function showAnswerSection(category: string, index: number) {
      if (!questionsData) return;

      const modal = document.getElementById("answerModal");
      const questionText = document.getElementById("modalQuestion");
      const contextText = document.getElementById("modalContext");
      const answerInput = document.getElementById(
        "modalAnswerText"
      ) as HTMLTextAreaElement | null;
      const feedbackContainer = document.getElementById(
        "modalFeedbackContainer"
      );
      const voiceInputBtn = document.getElementById(
        "voiceInputBtn"
      ) as HTMLButtonElement | null; // Cast for style access
      const modalSubmitBtn = document.getElementById(
        "modalSubmitBtn"
      ) as HTMLButtonElement | null; // Cast for disabled access
      const submitBtnText = document.getElementById("submitBtnText");
      const submitBtnSpinner = document.getElementById("submitBtnSpinner");

      if (
        !modal ||
        !questionText ||
        !contextText ||
        !answerInput ||
        !feedbackContainer ||
        !voiceInputBtn ||
        !modalSubmitBtn ||
        !submitBtnText ||
        !submitBtnSpinner
      ) {
        console.error("Could not find required modal elements");
        return;
      }

      // Clear previous content & state
      questionText.textContent = "";
      contextText.textContent = "";
      answerInput.value = "";
      answerInput.classList.remove("border-red-500"); // Clear validation error
      feedbackContainer.innerHTML = `
         <div class="p-6 bg-gray-50 dark:bg-gray-800/50 rounded-xl border border-gray-100 dark:border-gray-700/50 h-full flex items-center justify-center">
           <p class="text-gray-500 dark:text-gray-400 text-center">Submit your answer to get feedback.</p>
         </div>`; // Reset feedback
      updateSpeechError(""); // Clear speech errors
      updateRecordingStatus(""); // Clear recording status
      if (isRecording) stopRecording(); // Stop recording if active

      // Reset STAR method tips
      const starTips = document.getElementById("starMethodTips");
      const toggleTipsBtn = document.getElementById("toggleStarTips");
      if (starTips) starTips.classList.add("hidden");
      if (toggleTipsBtn) toggleTipsBtn.textContent = "Show STAR Method Tips";

      // Reset submit button state
      submitBtnText.textContent = "Submit for Review";
      submitBtnSpinner.classList.add("hidden");
      modalSubmitBtn.disabled = false;

      // Populate question details
      const question = questionsData[category][index];
      questionText.textContent = question.question;
      contextText.textContent =
        question.context || "No additional context provided.";

      // Setup voice input
      if (recognitionSupported && SpeechRecognitionAPI) {
        // Check API exists too
        voiceInputBtn.style.display = "block";
        voiceInputBtn.onclick = toggleRecording; // Use existing toggle function
        setupSpeechRecognition(); // Re-initialize recognition instance for this modal session
      } else {
        voiceInputBtn.style.display = "none";
        updateSpeechError("Voice input not supported in this browser.");
      }

      // --- Detach previous listener before adding a new one ---
      // Clone the button and replace it to remove all old listeners
      const newSubmitBtn = modalSubmitBtn.cloneNode(true) as HTMLButtonElement;
      modalSubmitBtn.parentNode?.replaceChild(newSubmitBtn, modalSubmitBtn);
      // Re-assign references after cloning
      const currentSubmitBtn = document.getElementById(
        "modalSubmitBtn"
      ) as HTMLButtonElement; // Re-get reference
      const currentSubmitBtnText = document.getElementById(
        "submitBtnText"
      ) as HTMLSpanElement; // Re-get reference
      const currentSubmitBtnSpinner = document.getElementById(
        "submitBtnSpinner"
      ) as SVGElement | null; // Re-get reference, allow null
      // --- End Detach ---

      // Set up submit handler for the new button
      currentSubmitBtn.addEventListener("click", async (event: MouseEvent) => {
        event.preventDefault();
        if (isRecording) stopRecording(); // Stop recording before submitting

        const currentAnswerInput = document.getElementById(
          "modalAnswerText"
        ) as HTMLTextAreaElement | null; // Get fresh reference
        if (!currentAnswerInput) return;

        const answerText = currentAnswerInput.value.trim();
        if (!answerText) {
          currentAnswerInput.classList.add("border-red-500");
          updateSpeechError("Please provide an answer before submitting."); // Use speech error field for consistency
          return;
        } else {
          currentAnswerInput.classList.remove("border-red-500");
          updateSpeechError(""); // Clear error
        }

        // Show loading state on button
        currentSubmitBtnText.textContent = "Reviewing...";
        currentSubmitBtnSpinner?.classList.remove("hidden"); // Use optional chaining
        currentSubmitBtn.disabled = true;

        // Show loading state in feedback area
        const currentFeedbackContainer = document.getElementById(
          "modalFeedbackContainer"
        ); // Get fresh reference
        if (currentFeedbackContainer) {
          currentFeedbackContainer.innerHTML = `
              <div class="animate-pulse space-y-4 p-6 bg-gray-50 dark:bg-gray-800/50 rounded-xl border border-gray-100 dark:border-gray-700/50 h-full">
                <div class="h-5 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
                <div class="space-y-2">
                  <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                  <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
                </div>
                 <div class="h-5 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mt-4"></div>
                 <div class="space-y-2">
                  <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                  <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                  <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-4/6"></div>
                </div>
              </div>
            `;
        }

        // Ensure user is authenticated before making the call
        const user = await authService.getCurrentUser();
        if (!user) {
          throw new Error("Authentication required to submit answers for review.");
        }
        const idToken = await user.getIdToken();

        // Call review-answer API
        try {
          const response = await fetch("/.netlify/functions/review-answer", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${idToken}` // Include the ID token
            },
            body: JSON.stringify({
              question: question.question,
              answer: answerText,
            }),
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({})); // Try to parse error
            throw new Error(
              errorData.error ||
                `API request failed with status ${response.status}`
            );
          }

          const result = await response.json();

          if (currentFeedbackContainer) {
            // Format the feedback from the response
            const feedbackHTML = `
                <div class="space-y-4 text-sm">
                  ${
                    result.weaknesses && result.weaknesses.length > 0
                      ? `
                  <div class="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-800/30">
                    <h4 class="font-semibold text-red-700 dark:text-red-300 mb-1.5 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" /></svg>
                      Areas for Improvement
                    </h4>
                    <ul class="list-disc pl-6 space-y-1 text-gray-700 dark:text-gray-300 text-left">
                      ${result.weaknesses.map((w: string) => `<li>${w}</li>`).join("")}
                    </ul>
                  </div>`
                      : ""
                  }

                  ${
                    result.improvements && result.improvements.length > 0
                      ? `
                  <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/30">
                    <h4 class="font-semibold text-blue-700 dark:text-blue-300 mb-1.5 flex items-center">
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                      Suggestions
                    </h4>
                    <ul class="list-disc pl-6 space-y-1 text-gray-700 dark:text-gray-300 text-left">
                      ${result.improvements.map((i: string) => `<li>${i}</li>`).join("")}
                    </ul>
                  </div>`
                      : ""
                  }

                  ${
                    result.alternativeApproach
                      ? `
                  <div class="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-100 dark:border-purple-800/30">
                    <h4 class="font-semibold text-purple-700 dark:text-purple-300 mb-1.5 flex items-center">
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor"><path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 011.087-.225l1.884.942a1 1 0 00.946 0l1.884-.942a.999.999 0 011.087.225l2.655 1.328a1 1 0 000-1.84l-7-3zM3 14a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" /></svg>
                      Alternative Approach
                    </h4>
                    <p class="text-gray-700 dark:text-gray-300 text-left"">${result.alternativeApproach}</p>
                  </div>`
                      : ""
                  }

                   ${
                     result.suggestedAnswer
                       ? `
                  <div class="p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg border border-indigo-100 dark:border-indigo-800/30">
                    <h4 class="font-semibold text-indigo-700 dark:text-indigo-300 mb-1.5 flex items-center">
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" /></svg>
                      Example Answer
                    </h4>
                    <p class="text-gray-700 dark:text-gray-300 text-left"">${result.suggestedAnswer}</p>
                  </div>`
                       : ""
                   }

                  ${
                    result.keyTakeaway
                      ? `
                  <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-100 dark:border-green-800/30">
                    <h4 class="font-semibold text-green-700 dark:text-green-300 mb-1.5 flex items-center">
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                      Key Takeaway
                    </h4>
                    <p class="text-gray-700 dark:text-gray-300 font-medium text-left"">${result.keyTakeaway}</p>
                  </div>`
                      : ""
                  }
                </div>
              `;

            currentFeedbackContainer.innerHTML = `
                <div class="p-4 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
                  <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Feedback</h3>
                  ${feedbackHTML}
                </div>
              `;
            // Hide the submit button after successfully displaying feedback
            currentSubmitBtn.classList.add("hidden");
          }
        } catch (error) {
          console.error("API Error:", error);

          // Use ErrorHandler to show error
          const errorHandler = new ErrorHandler("interviewPrep");
          const errorMessage = error instanceof Error ? error.message : "Failed to get feedback. Please try again.";

          // Show error in the feedback container
          if (currentFeedbackContainer) {
            currentFeedbackContainer.innerHTML = `
                <div class="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-800/30">
                  <h4 class="font-semibold text-red-700 dark:text-red-300 mb-1.5">Error</h4>
                  <p class="text-sm text-red-600 dark:text-red-400">${errorMessage}</p>
                </div>
              `;
          }

          // Also show error in the modal
          errorHandler.showError("API Error", errorMessage);
        } finally {
          // Reset button state
          currentSubmitBtnText.textContent = "Submit for Review";
          currentSubmitBtnSpinner?.classList.add("hidden"); // Use optional chaining
          currentSubmitBtn.disabled = false;
        }
      });

      // Set up STAR method tips toggle
      const toggleStarTipsBtn = document.getElementById("toggleStarTips");
      const starMethodTips = document.getElementById("starMethodTips");

      if (toggleStarTipsBtn && starMethodTips) {
        // Remove previous event listeners by cloning and replacing
        const newToggleBtn = toggleStarTipsBtn.cloneNode(true);
        toggleStarTipsBtn.parentNode?.replaceChild(newToggleBtn, toggleStarTipsBtn);

        // Add event listener to the new button
        newToggleBtn.addEventListener("click", () => {
          starMethodTips.classList.toggle("hidden");
          newToggleBtn.textContent = starMethodTips.classList.contains("hidden")
            ? "Show STAR Method Tips"
            : "Hide STAR Method Tips";
        });
      }

      // Show modal
      modal.classList.remove("hidden");
      answerInput.focus(); // Focus textarea when modal opens
    }

    // Global closeModal function needs to be attached to window
    window.closeModal = () => {
      const modal = document.getElementById("answerModal");
      if (modal) {
        modal.classList.add("hidden");
        if (isRecording) {
          stopRecording(); // Ensure recording stops if modal is closed
        }
        // Clear fields & state
        const answerTextArea = document.getElementById(
          "modalAnswerText"
        ) as HTMLTextAreaElement | null;
        if (answerTextArea) answerTextArea.value = "";

        const feedbackContainer = document.getElementById(
          "modalFeedbackContainer"
        );
        if (feedbackContainer)
          feedbackContainer.innerHTML = `
         <div class="p-6 bg-gray-50 dark:bg-gray-800/50 rounded-xl border border-gray-100 dark:border-gray-700/50 h-full flex items-center justify-center">
           <p class="text-gray-500 dark:text-gray-400 text-left">Submit your answer to get feedback.</p>
         </div>`;
        updateSpeechError("");
        updateRecordingStatus("");
      }
    };
  });
</script>

<script>
  // Import the resume input handler and error handler
  import { setupResumeInput } from "../../lib/resumeInputHandler";
  import { ErrorHandler } from "../../lib/errorHandler";

  // Declare global type for window
  declare global {
    interface Window {
      resumeInputHandler: any;
    }
  }

  // This script block handles resume input functionality
  document.addEventListener("DOMContentLoaded", () => {
    // Initialize the resume input handler with custom error handling
    const resumeInputHandler = setupResumeInput({
      componentId: "interviewPrep",
      fileInputId: "resumeFileInput",
      fileUploadButtonId: "resumeFileUploadButton",
      fileAddedUIId: "resumeFileAddedUI",
      fileNameElementId: "resumeFileName",
      changeSourceButtonId: "changeResumeSource",
      optionsSelectorId: "resumeOptionsSelector",
      manualEntryContainerId: "manualEntryContainer",
      enterManuallyButtonId: "enterManuallyButton",
      cancelManualEntryButtonId: "cancelManualEntry",
      resumeContentTextareaId: "resumeContent",
      importResumeButtonId: "importResumeButton",
      // Custom error handler that uses our ErrorHandler utility
      onError: (title: string, message: string) => {
        const errorHandler = new ErrorHandler("interviewPrep");
        errorHandler.showError(title, message);
      },
      // Callback when resume content changes
      onResumeContentChanged: (content: string) => {
        // You can add any additional logic here when resume content changes
        // For example, validation or UI updates
        const resumeContent = document.getElementById("resumeContent") as HTMLTextAreaElement;
        if (resumeContent && content !== resumeContent.value) {
          resumeContent.value = content;
          resumeContent.dispatchEvent(new Event("input"));
        }
      }
    });

    // Make the resume input handler available globally
    window.resumeInputHandler = resumeInputHandler;
  });
</script>
