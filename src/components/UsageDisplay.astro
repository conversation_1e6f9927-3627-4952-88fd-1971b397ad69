---
interface Props {
  current: number;
  limit: number;
  featureName: string;
  showUpgradeLink?: boolean;
}

const { 
  current, 
  limit, 
  featureName,
  showUpgradeLink = true
} = Astro.props;

const percentage = Math.min(100, (current / limit) * 100);
const colorClass = percentage > 80 ? "bg-red-500" : percentage > 50 ? "bg-yellow-500" : "bg-green-500";
const displayLimit = limit === 300 ? "∞" : limit;
---

<div class="mt-4 p-4 bg-gray-50 dark:bg-gray-800/30 rounded-lg border border-gray-200 dark:border-gray-700/50">
  <div class="flex justify-between items-center mb-1">
    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{featureName} Usage</span>
    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
      {current} / {displayLimit}
    </span>
  </div>
  <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
    <div class={`${colorClass} h-2.5 rounded-full transition-all duration-300 ease-in-out`} style={`width: ${percentage}%`}></div>
  </div>
  {percentage > 80 && showUpgradeLink && (
    <p class="mt-2 text-xs text-red-600 dark:text-red-400">
      You're approaching your usage limit. <a href="/pricing" class="underline">Upgrade</a> for increased limits.
    </p>
  )}
</div>
