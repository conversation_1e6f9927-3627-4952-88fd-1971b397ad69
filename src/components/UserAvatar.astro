---
// Simple component to display user avatar with fallback
interface Props {
  photoURL?: string | null;
  displayName: string;
  size?: 'small' | 'medium' | 'large';
  isPro?: boolean;
}

const { photoURL, displayName, size = 'medium', isPro = false } = Astro.props;

const firstLetter = displayName.charAt(0).toUpperCase();

// Size classes
const sizeClasses = {
  small: 'w-8 h-8 text-sm',
  medium: 'w-10 h-10 text-base',
  large: 'w-12 h-12 text-lg'
};

// Pro ring classes
const proRingClasses = isPro 
  ? 'ring-2 ring-[var(--color-purple)] ring-offset-2 ring-offset-white dark:ring-offset-gray-900' 
  : '';

// Base classes
const baseClasses = `user-avatar flex items-center justify-center rounded-full ${sizeClasses[size]} ${proRingClasses}`;
---

{photoURL ? (
  <div class={baseClasses}>
    <img 
      src={photoURL} 
      alt={`${displayName}'s avatar`} 
      class="w-full h-full rounded-full object-cover"
      onerror="this.onerror=null; this.parentNode.innerHTML=`<span class='font-semibold'>${this.alt.charAt(0).toUpperCase()}</span>`; this.parentNode.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-800', 'dark:text-white');"
    />
  </div>
) : (
  <div class={`${baseClasses} bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white`}>
    <span class="font-semibold">{firstLetter}</span>
  </div>
)}