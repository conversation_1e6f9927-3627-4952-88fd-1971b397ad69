---
interface Props {
  userId?: string;
  userEmail?: string;
}

const { userId = '', userEmail = '' } = Astro.props;
---

<div class="feedback-widget bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden">
  <div class="p-6">
    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Share Your Feedback</h3>

    <p class="text-gray-600 dark:text-gray-300 mb-4">
      We value your feedback! Please take a moment to share your thoughts about our platform.
      Your input helps us improve and provide a better experience for everyone.
    </p>

    <form id="feedbackForm" class="space-y-4">
      <div>
        <label for="feedback" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Your Feedback</label>
        <textarea
          id="feedback"
          name="feedback"
          rows="4"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white"
          placeholder="Share your thoughts, suggestions, or experiences..."
          required
        ></textarea>
      </div>

      <div>
        <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category (Optional)</label>
        <select
          id="category"
          name="category"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white"
        >
          <option value="">Select a category</option>
          <option value="General">General</option>
          <option value="User Interface">User Interface</option>
          <option value="Features">Features</option>
          <option value="Performance">Performance</option>
          <option value="Bug Report">Bug Report</option>
          <option value="Suggestion">Suggestion</option>
        </select>
      </div>

      <div>
        <label for="rating" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Rating (Optional)</label>
        <div class="flex items-center space-x-1">
          <input type="hidden" id="rating" name="rating" value="">
          <button type="button" data-rating="1" class="rating-star text-gray-300 dark:text-gray-600 hover:text-yellow-400 dark:hover:text-yellow-400 text-2xl focus:outline-none">★</button>
          <button type="button" data-rating="2" class="rating-star text-gray-300 dark:text-gray-600 hover:text-yellow-400 dark:hover:text-yellow-400 text-2xl focus:outline-none">★</button>
          <button type="button" data-rating="3" class="rating-star text-gray-300 dark:text-gray-600 hover:text-yellow-400 dark:hover:text-yellow-400 text-2xl focus:outline-none">★</button>
          <button type="button" data-rating="4" class="rating-star text-gray-300 dark:text-gray-600 hover:text-yellow-400 dark:hover:text-yellow-400 text-2xl focus:outline-none">★</button>
          <button type="button" data-rating="5" class="rating-star text-gray-300 dark:text-gray-600 hover:text-yellow-400 dark:hover:text-yellow-400 text-2xl focus:outline-none">★</button>
        </div>
      </div>

      <div class="pt-2">
        <button
          type="submit"
          id="submitFeedback"
          class="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent rounded-lg shadow-sm font-medium text-white bg-black dark:bg-white dark:text-black hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-150"
        >
          Submit Feedback
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </form>

    <div id="feedbackStatus" class="mt-4 hidden">
      <div id="successMessage" class="p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hidden">
        Thank you for your feedback! We appreciate your input.
      </div>
      <div id="errorMessage" class="p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-lg hidden">
        There was an error submitting your feedback. Please try again.
      </div>
    </div>

    <p class="text-sm text-gray-500 dark:text-gray-400 mt-6 text-center">
      Your feedback is anonymous unless you choose to provide contact information.
    </p>
  </div>
</div>

<script define:vars={{ userId, userEmail }}>
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('feedbackForm');
    const submitButton = document.getElementById('submitFeedback');
    const feedbackStatus = document.getElementById('feedbackStatus');
    const successMessage = document.getElementById('successMessage');
    const errorMessage = document.getElementById('errorMessage');
    const ratingInput = document.getElementById('rating');
    const ratingStars = document.querySelectorAll('.rating-star');

    // Handle star rating selection
    ratingStars.forEach(star => {
      star.addEventListener('click', () => {
        const rating = star.getAttribute('data-rating');
        ratingInput.value = rating;

        // Update star colors
        ratingStars.forEach(s => {
          const starRating = parseInt(s.getAttribute('data-rating'));
          if (starRating <= parseInt(rating)) {
            s.classList.add('text-yellow-400');
            s.classList.remove('text-gray-300', 'dark:text-gray-600');
          } else {
            s.classList.remove('text-yellow-400');
            s.classList.add('text-gray-300', 'dark:text-gray-600');
          }
        });
      });
    });

    // Handle form submission
    form.addEventListener('submit', async (e) => {
      e.preventDefault();

      // Disable submit button and show loading state
      submitButton.disabled = true;
      submitButton.innerHTML = `
        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white dark:text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Submitting...
      `;

      // Get form data
      const formData = new FormData(form);
      const feedback = formData.get('feedback');
      const category = formData.get('category');
      const rating = formData.get('rating') ? parseInt(formData.get('rating').toString()) : undefined;

      try {
        // Send data to Netlify function
        const response = await fetch('/.netlify/functions/feedback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            feedback,
            category,
            rating,
            userId,
            userEmail,
            source: 'Dashboard Feedback Form'
          }),
        });

        const result = await response.json();

        // Show appropriate message
        feedbackStatus.classList.remove('hidden');
        if (response.ok) {
          successMessage.classList.remove('hidden');
          errorMessage.classList.add('hidden');
          form.reset();
          ratingStars.forEach(s => {
            s.classList.remove('text-yellow-400');
            s.classList.add('text-gray-300', 'dark:text-gray-600');
          });
        } else {
          successMessage.classList.add('hidden');
          errorMessage.classList.remove('hidden');
          errorMessage.textContent = result.error || 'There was an error submitting your feedback. Please try again.';
        }
      } catch (error) {
        // Show error message
        feedbackStatus.classList.remove('hidden');
        successMessage.classList.add('hidden');
        errorMessage.classList.remove('hidden');
        errorMessage.textContent = 'Network error. Please check your connection and try again.';
        console.error('Feedback submission error:', error);
      } finally {
        // Reset button state
        submitButton.disabled = false;
        submitButton.innerHTML = `
          Submit Feedback
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        `;
      }
    });
  });
</script>
