---
interface Props {
  id: string;
  title?: string;
  message?: string;
  isOpen?: boolean;
  zIndex?: number;
}

const {
  id,
  title = "Error",
  message = "An unexpected error occurred. Please try again.",
  isOpen = false,
  zIndex = 60
} = Astro.props;
---

<div
  id={id}
  class={`fixed inset-0 z-[${zIndex}] flex items-center justify-center p-4 modal-transition ${isOpen ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'}`}
>
  <!-- Backdrop -->
  <div id={`${id}-backdrop`} class="absolute inset-0 bg-white/50 dark:bg-black/50 backdrop-blur-sm modal-transition"></div>

  <!-- Modal Content -->
  <div
    id={`${id}-content`}
    class="bg-white dark:bg-gray-900 rounded-2xl p-6 max-w-md w-full text-center transform modal-content-transition relative"
    class:list={[
      isOpen ? "scale-100 opacity-100" : "scale-95 opacity-0"
    ]}
  >
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-bold text-red-600 dark:text-red-400">{title}</h2>
      <button
        id={`${id}-close-button`}
        class="text-gray-500 hover:text-gray-900 dark:hover:text-white"
        aria-label="Close error modal"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
    <p class="text-gray-700 dark:text-gray-300 mb-6" id={`${id}-message`}>{message}</p>
    <div class="flex justify-end">
      <button
        id={`${id}-close-button-bottom`}
        class="px-4 py-2 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors"
      >
        Close
      </button>
    </div>
  </div>
</div>

<script is:inline define:vars={{ id }}>
  // Simple modal functionality without TypeScript errors
  document.addEventListener('DOMContentLoaded', function() {
    // The modal ID is passed from the component via define:vars
    if (!id) return;

    // Get modal elements
    const modal = document.getElementById(id);
    const modalContent = document.getElementById(`${id}-content`);
    const backdrop = document.getElementById(`${id}-backdrop`);
    const closeButtons = [
      document.getElementById(`${id}-close-button`),
      document.getElementById(`${id}-close-button-bottom`)
    ];

    if (!modal || !modalContent) return;

    // Set up close button handlers
    closeButtons.forEach(function(button) {
      if (button) {
        button.addEventListener('click', hideModal);
      }
    });

    // Close when clicking backdrop
    if (modal) {
      modal.addEventListener('click', function(e) {
        if (e.target === modal || e.target === backdrop) {
          hideModal();
        }
      });
    }

    // Close on Escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && modal && !modal.classList.contains('opacity-0')) {
        hideModal();
      }
    });

    // Hide modal function
    function hideModal() {
      // Fade out content
      if (modalContent) {
        modalContent.style.opacity = '0';
        modalContent.style.transform = 'scale(0.95) translateY(10px)';
      }

      // Fade out backdrop
      if (backdrop) {
        backdrop.style.opacity = '0';
      }

      // Hide modal after animation
      setTimeout(function() {
        if (modal) {
          modal.classList.add('opacity-0', 'pointer-events-none');
          modal.classList.remove('opacity-100');
        }

        // Remove body overflow hidden
        document.body.classList.remove('overflow-hidden');
      }, 200);
    }

    // Show modal function
    function showModal() {
      // Make modal visible
      if (modal) {
        modal.classList.remove('opacity-0', 'pointer-events-none');
        modal.classList.add('opacity-100');
      }

      // Add body overflow hidden
      document.body.classList.add('overflow-hidden');

      // Animate in content
      if (modalContent) {
        modalContent.style.opacity = '1';
        modalContent.style.transform = 'scale(1) translateY(0)';
      }

      // Fade in backdrop
      if (backdrop) {
        backdrop.style.opacity = '1';
      }
    }

    // Update message function
    function updateMessage(message) {
      const messageEl = document.getElementById(`${id}-message`);
      if (messageEl) {
        messageEl.textContent = message;
      }
    }

    // Expose functions to window
    if (!window.errorModalFunctions) {
      window.errorModalFunctions = {};
    }

    window.errorModalFunctions[id] = {
      show: showModal,
      hide: hideModal,
      updateMessage: updateMessage
    };
  });
</script>
