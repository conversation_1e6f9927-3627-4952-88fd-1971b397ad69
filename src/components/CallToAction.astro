---
import Container from "./Container.astro";
---

<div class="relative py-16">
  <div
    aria-hidden="true"
    class="absolute inset-0 h-max w-full m-auto grid grid-cols-2 -space-x-52 opacity-40 dark:opacity-20"
  >
    <div
      class="blur-[106px] h-56 bg-gradient-to-br from-primary to-purple-400 dark:from-blue-700"
    >
    </div>
    <div
      class="blur-[106px] h-32 bg-gradient-to-r from-cyan-400 to-sky-300 dark:to-indigo-600"
    >
    </div>
  </div>
  <Container>
    <div class="relative">
      <div class="mt-6 m-auto text-center space-y-6 md:w-8/12 lg:w-7/12">
        <h1
          class="text-center text-4xl font-bold text-gray-800 dark:text-white md:text-5xl"
        >
          Transform Your Career with AI
        </h1>
        <p
          class="mt-4 text-gray-600 dark:text-gray-300 md:mx-auto md:w-10/12 lg:w-8/12"
        >
          Elevate your career with tailored resume optimization, insightful job
          analysis, and efficient application tracking. Start your journey
          today.
        </p>
        <div class="flex flex-wrap justify-center gap-6">
          <a
            href="/login"
            class="relative flex text-white dark:text-black bg-black dark:bg-white h-14 w-full items-center justify-center px-10 rounded-full border border-primary/20 bg-primary/10 text-primary hover:bg-primary/20 transition-all duration-300 ease-in-out transform hover:scale-105 active:scale-95 sm:w-max"
          >
            <span class="text-lg font-semibold">Get Started</span>
          </a>
        </div>
        <p class="text-center text-sm text-gray-500 dark:text-gray-400 mt-4">
          Join 10,000+ professionals transforming their careers
        </p>
      </div>
    </div>
  </Container>
</div>
