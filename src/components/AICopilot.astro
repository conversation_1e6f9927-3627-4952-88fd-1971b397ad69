---
import ErrorModal from "./ErrorModal.astro";
// LoadingSpinner import removed as it was unused in the template.

// The `copilot` constant was unused and has been removed.
---

<!-- Message Templates for safe, programmatic cloning -->
<template id="user-message-template">
  <div class="flex justify-end mb-3 animate-slide-up">
    <div class="relative max-w-[80%] px-4 py-2.5 shadow-sm bg-indigo-50 dark:bg-indigo-900/30 rounded-2xl rounded-tr-sm">
      <div class="absolute right-0 top-[50%] -translate-y-[50%] translate-x-[6px] transform rotate-45 w-2 h-2 bg-indigo-50 dark:bg-indigo-900/30"></div>
      <div class="relative">
        <p class="text-indigo-900 dark:text-indigo-100 text-sm" data-message-text></p>
      </div>
    </div>
  </div>
</template>

<template id="ai-message-template">
  <div class="flex mb-3 animate-slide-up">
    <div class="relative max-w-[80%] px-4 py-2.5 shadow-sm bg-gray-50 dark:bg-gray-800/50 rounded-2xl rounded-tl-sm">
      <div class="absolute left-0 top-[50%] -translate-y-[50%] -translate-x-[6px] transform rotate-45 w-2 h-2 bg-gray-50 dark:bg-gray-800/50"></div>
      <div class="relative">
        <p class="text-gray-800 dark:text-gray-200 text-sm whitespace-pre-line" data-message-text></p>
      </div>
    </div>
  </div>
</template>

<template id="loading-indicator-template">
  <div class="flex mb-3 animate-slide-up" id="loading-indicator">
    <div class="relative max-w-[80%]">
      <div class="absolute left-0 top-[50%] -translate-y-[50%] -translate-x-[6px] transform rotate-45 w-2 h-2 bg-gray-50 dark:bg-gray-800/50"></div>
      <div class="relative bg-gray-50 dark:bg-gray-800/50 rounded-2xl rounded-tl-sm px-4 py-2.5 flex items-center gap-2">
        <div class="flex items-center gap-2">
          <div class="w-4 h-4 relative">
            <div class="absolute top-0 left-0 w-full h-full rounded-full border-2 border-t-indigo-500 dark:border-t-indigo-400 animate-spin"></div>
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400">Career Buddy is typing...</span>
        </div>
      </div>
    </div>
  </div>
</template>

<!-- Initial Floating Button -->
<button
  id="copilot-trigger"
  aria-label="Open career buddy chat"
  class="fixed bottom-4 right-4 sm:bottom-6 sm:right-6 z-50 p-4 bg-gradient-to-r from-purple-500 to-blue-500 dark:from-blue-700 dark:to-purple-700 backdrop-blur-lg hover:bg-gradient-to-r hover:from-purple-600 hover:to-blue-600 dark:hover:from-blue-800 dark:hover:to-purple-800 text-gray-800 dark:text-white rounded-full shadow-[0_4px_16px_0_rgba(168,85,247,0.34)] dark:shadow-[0_4px_32px_0_rgba(168,85,247,0.48)] border border-gray-200/50 dark:border-gray-700/50 transition-all duration-300 ease-in-out flex items-center gap-2 sm:gap-3 animate-fade-in hidden"
>
  <svg class="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
  </svg>
</button>

<!-- Chat Interface -->
<div
  id="copilot-chat"
  class="fixed inset-y-0 right-0 z-50 w-full sm:w-[400px] lg:w-[450px] bg-white/15 dark:bg-gray-900/15 backdrop-blur-xl shadow-2xl border-l border-gray-100 dark:border-gray-800 hidden transform-gpu sm:rounded-l-3xl"
>
  <div class="flex flex-col h-full sm:rounded-l-3xl overflow-hidden">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-100 dark:border-gray-800/50">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-md p-1">
            <svg class="h-6 w-6 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 13.8214 2.48697 15.5291 3.33782 17L2.5 21.5L7 20.6622C8.47087 21.513 10.1786 22 12 22Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M9.5 12H9.51" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M14.5 12H14.51" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 9.5V9.51" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 14.5V14.51" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div>
            <h3 class="font-semibold text-gray-900 dark:text-white">Career Buddy</h3>
            <p class="text-xs text-green-600 dark:text-green-400 font-medium">● Online</p>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <button id="reset-chat" aria-label="Reset chat" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-xl transition-colors">
            <!-- Changed icon to a more intuitive "refresh" -->
            <svg class="h-5 w-5 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M23 4v6h-6"></path>
              <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"></path>
            </svg>
          </button>
          <button id="close-copilot" aria-label="Close chat" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-xl transition-colors">
            <svg class="h-5 w-5 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <div id="chat-messages" class="flex-1 overflow-y-auto px-6 py-4 space-y-4">
      <!-- Messages will be added here dynamically -->
    </div>

    <!-- Input -->
    <div class="px-6 py-4 border-t border-gray-100 dark:border-gray-800/50">
      <div class="flex gap-3">
        <div class="relative flex-1">
          <input type="text" id="user-input" placeholder="Type your message..." class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-800 border-0 rounded-xl text-sm focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 placeholder:text-gray-400 dark:placeholder:text-gray-500 pr-10"/>
        </div>
        <button id="send-message" aria-label="Send message" class="p-3 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white rounded-xl transition-all duration-300 flex items-center justify-center group">
          <svg class="h-5 w-5 transform group-hover:translate-x-0.5 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  import { authService } from "../lib/auth";
  // aiCopilotOptimistic.js import removed as its logic is now integrated here for clarity.

  document.addEventListener("DOMContentLoaded", async () => {
    // --- Element Selectors ---
    const triggerButton = document.getElementById("copilot-trigger");
    const chatInterface = document.getElementById("copilot-chat");
    const closeButton = document.getElementById("close-copilot");
    const resetButton = document.getElementById("reset-chat");
    const chatMessages = document.getElementById("chat-messages");
    const userInput = document.getElementById("user-input") as HTMLInputElement;
    const sendButton = document.getElementById("send-message");

    // --- Template Selectors ---
    const userMessageTemplate = document.getElementById("user-message-template") as HTMLTemplateElement;
    const aiMessageTemplate = document.getElementById("ai-message-template") as HTMLTemplateElement;
    const loadingIndicatorTemplate = document.getElementById("loading-indicator-template") as HTMLTemplateElement;

    // --- Helper Functions ---
    const scrollToBottom = () => {
      if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
      }
    };

    const showError = (title, message) => {
      import("../lib/errorHandling").then(({ showErrorModal }) => {
        showErrorModal("copilotErrorModal", title, message);
      }).catch(err => console.error("Failed to import error handling utility:", err));
    };

    const renderWelcomeMessage = () => {
      // This function creates the welcome message programmatically to avoid duplicating HTML.
      const welcomeMessageEl = document.createElement("div");
      welcomeMessageEl.id = "welcome-message";
      welcomeMessageEl.className = "p-4 rounded-2xl bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 shadow-md border border-gray-200 dark:border-gray-700 animate-slide-up";
      
      const p = document.createElement("p");
      p.className = "text-gray-800 dark:text-gray-200 text-sm mb-3";
      p.textContent = "Hi! I'm here to help you with your career journey. What can I help you with today?";
      welcomeMessageEl.appendChild(p);

      const buttonContainer = document.createElement("div");
      buttonContainer.className = "flex flex-wrap gap-2";

      const quickActions = [
        { text: "Resume Tips", message: "Can you give me some resume tips?" },
        { text: "Interview Help", message: "Can you help me prepare for interviews?" },
        { text: "Career Advice", message: "Can you provide some career advice?" },
        { text: "Job Search", message: "What are some effective job search strategies?" },
      ];

      quickActions.forEach(action => {
        const button = document.createElement("button");
        button.className = "quick-action px-3 py-1 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300 text-xs hover:bg-indigo-200 dark:hover:bg-indigo-800 transition";
        button.textContent = action.text;
        button.dataset.message = action.message;
        buttonContainer.appendChild(button);
      });

      welcomeMessageEl.appendChild(buttonContainer);
      chatMessages?.appendChild(welcomeMessageEl);
    };

    const appendMessage = (template, message) => {
      const messageClone = template.content.cloneNode(true);
      const textElement = messageClone.querySelector("[data-message-text]");
      if (textElement) {
        textElement.textContent = message;
      }
      chatMessages?.appendChild(messageClone);
      scrollToBottom();
      return messageClone;
    };

    const appendUserMessage = (message) => appendMessage(userMessageTemplate, message);
    const appendAiMessage = (message) => appendMessage(aiMessageTemplate, message);
    
    const showLoadingIndicator = () => {
      const indicator = loadingIndicatorTemplate.content.cloneNode(true);
      chatMessages?.appendChild(indicator);
      scrollToBottom();
      // Return the element itself so it can be found and removed/updated later
      return chatMessages?.querySelector("#loading-indicator");
    };

    const sendMessage = async (prompt?: string) => {
      const message = prompt ?? userInput.value.trim();
      if (!message) return;

      document.getElementById("welcome-message")?.remove();
      appendUserMessage(message);
      userInput.value = "";

      const loadingEl = showLoadingIndicator();

      try {
        const response = await fetch("/api/ai-copilot", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ message, mode: "careerGuidance" }),
        });

        if (!response.ok) {
          throw new Error(`API responded with status ${response.status}`);
        }

        const aiResponse = await response.json();
        loadingEl?.remove();
        appendAiMessage(aiResponse.data.message);

      } catch (error) {
        console.error("AI Copilot Error:", error);
        showError("AI Response Error", "There was an error getting a response. Please try again.");
        
        // Update the loading element to show an error message
        if (loadingEl) {
          loadingEl.innerHTML = `
            <div class="relative bg-red-50 dark:bg-red-900/30 rounded-2xl rounded-tl-sm px-4 py-2.5">
              <p class="text-red-700 dark:text-red-300 text-sm">Sorry, I encountered an error. Please try again.</p>
            </div>
          `;
        }
      }
    };

    // --- Initial Authentication Check ---
    try {
      const user = await authService.getCurrentUser();
      if (user) {
        triggerButton?.classList.remove("hidden");
        renderWelcomeMessage(); // Initial render of the welcome message
      }
    } catch (error) {
      console.error("Error checking authentication:", error);
      showError("Authentication Error", "There was an error checking your authentication status.");
    }

    // --- Event Listeners ---
    triggerButton?.addEventListener("click", () => {
      chatInterface?.classList.remove("hidden");
      chatInterface?.classList.add("slide-in");
      triggerButton.classList.add("hidden");
      userInput?.focus(); // Accessibility improvement
    });

    closeButton?.addEventListener("click", () => {
      chatInterface?.classList.add("slide-out");
      setTimeout(() => {
        chatInterface?.classList.add("hidden");
        chatInterface?.classList.remove("slide-out", "slide-in");
        triggerButton?.classList.remove("hidden");
      }, 300);
    });

    resetButton?.addEventListener("click", () => {
      if (chatMessages) chatMessages.innerHTML = "";
      renderWelcomeMessage();
      
      fetch("/api/ai-copilot/reset", { method: "POST" })
        .then(response => {
          if (!response.ok) console.error("Failed to reset chat on server.");
        })
        .catch(error => {
          console.error("Error resetting chat:", error);
          showError("Reset Error", "There was an error resetting the chat.");
        });
    });

    sendButton?.addEventListener("click", () => sendMessage(undefined));
    userInput?.addEventListener("keypress", (e) => {
      if (e.key === "Enter") sendMessage(undefined);
    });

    // Event Delegation for quick actions. This is more efficient.
    chatMessages?.addEventListener("click", (e) => {
      const target = e.target as HTMLElement;
      if (target && target.classList.contains("quick-action")) {
        const quickMessage = target.dataset.message;
        if (quickMessage) {
          sendMessage(quickMessage);
        }
      }
    });
  });
</script>

<!-- Error Modal Component -->
<ErrorModal
  id="copilotErrorModal"
  title="Error"
  message="An error occurred. Please try again."
  isOpen={false}
  zIndex={60}
/>