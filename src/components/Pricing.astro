<section class="relative py-32 transition-colors duration-300 ease-in-out">
  <div class="mx-auto max-w-5xl px-6">
    <div class="mx-auto max-w-2xl space-y-6 text-center">
      <h1
        class="text-title text-center text-4xl font-semibold lg:text-5xl bg-gradient-to-r from-gray-900 via-gray-700 to-gray-900 dark:from-white dark:via-gray-200 dark:to-white bg-clip-text text-transparent animate-gradient-x"
      >
        Pricing that Scales with You
      </h1>
      <p class="text-body text-gray-600 dark:text-gray-300 max-w-xl mx-auto">
        Choose the plan that best fits your needs.
      </p>
    </div>

    <div class="mt-20 grid gap-8 lg:grid-cols-2 lg:gap-12">
      <!-- Free Tier -->
      <div
        class="relative group rounded-2xl bg-gray-100 dark:bg-gray-950 border dark:border-gray-800 dark: p-8 lg:p-10 shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-sm"
      >
        <div class="space-y-6">
          <div class="space-y-3">
            <span
              class="inline-block text-primary dark:text-blue-400 font-medium"
              >Starter</span
            >
            <div class="flex items-baseline">
              <span class="text-4xl font-bold text-gray-900 dark:text-white"
                >Free</span
              >
            </div>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              Perfect for getting started
            </p>
          </div>

          <button
            class="w-full inline-flex items-center justify-center px-6 py-3 rounded-xl text-base font-medium text-gray-900 dark:text-white bg-gray-200 dark:bg-gray-800 hover:bg-gray-300 dark:hover:bg-gray-700 transition-colors duration-200 group-hover:scale-[1.02]"
            id="getStarted"
          >
            Get Started
          </button>

          <ul class="space-y-4 text-sm text-gray-600 dark:text-gray-300">
            <li class="flex items-start gap-3">
              <svg
                class="w-5 h-5 text-primary dark:text-blue-400 shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
              <span>5 Resume Generations</span>
            </li>
            <li class="flex items-start gap-3">
              <svg
                class="w-5 h-5 text-primary dark:text-blue-400 shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
              <span>5 Cover Letter Generations</span>
            </li>
            <li class="flex items-start gap-3">
              <svg
                class="w-5 h-5 text-primary dark:text-blue-400 shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
              <span>5 Job research Requests</span>
            </li>
            <li class="flex items-start gap-3">
              <svg
                class="w-5 h-5 text-primary dark:text-blue-400 shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
              <span>5 Job Application Tracking</span>
            </li>
            <li class="flex items-start gap-3">
              <svg
                class="w-5 h-5 text-primary dark:text-blue-400 shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
              <span>5 Job Interview Preparation</span>
            </li>
            <li class="flex items-start gap-3">
              <svg
                class="w-5 h-5 text-primary dark:text-blue-400 shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
              <span>5 LinkedIn Profile Analysis</span>
            </li>
            <li class="flex items-start gap-3">
              <svg
                class="w-5 h-5 text-primary dark:text-blue-400 shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
              <span>Basic Support</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- Pro Tier -->
      <div
        class="relative group border dark:border-gray-800 dark:bg-gray-950 rounded-2xl p-8 lg:p-10 shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-sm"
      >
        <!-- Popular badge -->
        <div
          class="absolute -right-12 top-8 rotate-45 bg-gradient-to-r from-primary to-blue-600 text-gray-900 dark:text-white text-xs font-bold py-1.5 px-12 transform"
        >
          Most Popular
        </div>

        <div class="space-y-6">
          <div class="space-y-3">
            <span class="inline-block text-blue-400 font-medium">Pro</span>
            <div class="flex items-baseline">
              <span class="text-4xl font-bold text-black dark:text-white"
                >₹599</span
              >
              <span class="ml-1 text-sm text-gray-400">/month</span>
            </div>
            <p class="text-sm text-gray-400">
              For power users and professionals
            </p>
          </div>

          <button
            id="upgradeButton"
            class="w-full inline-flex items-center justify-center px-6 py-3 rounded-xl text-base font-medium text-white bg-gray-800 dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200 group-hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span class="btn-label">Upgrade to Pro</span>
          </button>
          <div
            id="upgradeErrorContainer"
            class="text-red-400 text-sm text-center hidden"
          >
          </div>

          <ul class="space-y-4 text-sm text-gray-800 dark:text-gray-100">
            <li class="flex items-start gap-3">
              <svg
                class="w-5 h-5 text-blue-400 shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-900 dark:text-gray-100"
                >300 Resume Generation</span
              >
            </li>
            <li class="flex items-start gap-3">
              <svg
                class="w-5 h-5 text-blue-400 shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-900 dark:text-gray-100"
                >300 Cover Letter Generation</span
              >
            </li>
            <li class="flex items-start gap-3">
              <svg
                class="w-5 h-5 text-blue-400 shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-900 dark:text-gray-100"
                >300 Job research Requests</span
              >
            </li>
            <li class="flex items-start gap-3">
              <svg
                class="w-5 h-5 text-blue-400 shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-900 dark:text-gray-100"
                >300 Job Application Tracking</span
              >
            </li>
            <li class="flex items-start gap-3">
              <svg
                class="w-5 h-5 text-blue-400 shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-900 dark:text-gray-100"
                >300 Job Interview Preparation</span
              >
            </li>
            <li class="flex items-start gap-3">
              <svg
                class="w-5 h-5 text-blue-400 shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-900 dark:text-gray-100"
                >300 LinkedIn Profile Analysis</span
              >
            </li>
            <li class="flex items-start gap-3">
              <svg
                class="w-5 h-5 text-blue-400 shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-900 dark:text-gray-100"
                >Priority Support</span
              >
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  const getStartedButton = document.getElementById("getStarted");
  getStartedButton?.addEventListener("click", () => {
    window.location.href = "/login";
  });

  const upgradeButton = document.getElementById("upgradeButton");
  upgradeButton?.addEventListener("click", () => {
    window.location.href = "/pricing";
  });
</script>
