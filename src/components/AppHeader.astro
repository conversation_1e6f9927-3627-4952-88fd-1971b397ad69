---
import Container from "./Container.astro";
import ThemeToggle from "./ThemeToggle.astro";
import UserAvatar from "./UserAvatar.astro";
---

<header
  class="fixed top-0 left-0 right-0 z-20"
  id="mainHeader"
>
  <Container>
    <nav class="flex items-center justify-between h-20 relative w-full">
      <!-- Logo -->
      <a href="/" class="flex items-center gap-3 px-2 py-1.5 rounded-full bg-white backdrop-blur-lg dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200 shadow-md">
        <img
          src="/logo.svg"
          alt="PraxJobs Logo"
          class="h-8 w-8 transform hover:scale-105 transition-transform duration-300 filter dark:invert"
        />
        <span class="font-base text-2xl text-gray-800 dark:text-white">PraxJobs</span>
      </a>

      <!-- Right Side Menu Items -->
      <div class="flex items-center gap-4">
        <!-- Mobile Menu Button -->
        <button
          id="mobileMenuButton"
          class="md:hidden p-3 text-gray-800 bg-white dark:bg-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full focus:outline-none focus:ring-2 focus:ring-primary/30 shadow-md"
          aria-label="Open mobile menu"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>

        <!-- Desktop Auth/User Menu -->
        <div class="hidden md:flex items-center gap-4">
          <div
            id="authStateContainer"
            class="flex items-center gap-4 opacity-0 transition-opacity duration-300"
            data-authenticated="false"
            data-auth-ready="false"
          >
            <!-- Auth Buttons (Unauthenticated) -->
            <div id="authButtons" class="flex items-center gap-4">
              <a href="/pricing" class="px-4 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200 shadow-md">
                Plans
              </a>
              <a href="/login" class="px-4 py-2 bg-gray-900 dark:bg-white text-white dark:text-gray-950 rounded-full hover:bg-primary-dark transition-colors duration-200 shadow-md">
                Sign In
              </a>
            </div>

            <!-- Tools Dropdown (Authenticated & Unauthenticated) -->
            <div class="relative">
              <button id="toolsDropdownButton" class="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200 shadow-md" aria-haspopup="true" aria-expanded="false">
                Tools
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </button>
              <div id="toolsDropdownMenu" class="absolute left-0 top-full mt-2 w-48 bg-white dark:bg-gray-900 rounded-2xl shadow-lg py-1 invisible opacity-0 scale-95 transition-all duration-300 ease-in-out origin-top-left z-50 border border-gray-200 dark:border-gray-700">
                <!-- Desktop Tool links will be injected here by JS -->
              </div>
            </div>

            <!-- Dashboard Link (Authenticated) -->
            <a id="dashboardNavLink" href="/dashboard" class="hidden px-4 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200 shadow-md">
              Dashboard
            </a>

            <!-- User Profile Dropdown (Authenticated) -->
            <div id="userProfileDropdown" class="relative hidden">
              <button id="userDropdownButton" class="flex items-center gap-1 px-1 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200 shadow-md" aria-haspopup="true" aria-expanded="false">
                <div id="desktopAvatarWrapper" class="h-8 w-8 rounded-full flex items-center justify-center mr-1"></div>
                <!-- Direct implementation of UserAvatar (hidden by default, will be shown by JS) -->
                <div id="directAvatarTest" class="hidden"></div>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </button>
              <div id="userDropdownMenu" class="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-900 rounded-2xl shadow-lg py-1 invisible opacity-0 scale-95 transition-all duration-300 ease-in-out origin-top-right z-50 border border-gray-200 dark:border-gray-700">
                <div class="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700 mb-2">
                  <p id="dropdownFullName" class="font-semibold"></p>
                  <p id="dropdownEmail" class="text-xs text-gray-500 dark:text-gray-400 overflow-hidden text-ellipsis whitespace-nowrap"></p>
                  <p id="dropdownTier" class="text-xs text-gray-500 dark:text-gray-400 mt-1"></p>
                </div>
                <a href="/settings" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Settings</a>
                <a href="/pricing" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Pricing</a>
                <a href="/help" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Help</a>
                <button id="signOutButton" class="w-full text-left text-gray-700 dark:text-gray-300 px-4 py-2 text-sm border-t border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700">
                  Sign Out
                </button>
              </div>
            </div>
          </div>
          <ThemeToggle />
        </div>
      </div>
    </nav>
  </Container>
</header>

<!-- Mobile Menu -->
<div slot="mobile-menu">
  <div id="mobile-menu-overlay" class="fixed inset-0 bg-white/50 dark:bg-black/50 backdrop-blur-sm z-[60] opacity-0 invisible transition-all duration-200 ease-in-out" aria-hidden="true"></div>
  <div id="mobile-menu-panel" class="fixed inset-y-0 right-0 w-full max-w-md bg-white dark:bg-gray-900/90 shadow-2xl transform translate-x-full transition-transform duration-200 ease-in-out overflow-y-auto z-[70] rounded-l-2xl md:rounded-l-3xl">
    <div class="sticky top-0 z-20 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm p-4 flex justify-end">
      <button id="mobile-menu-close-btn" class="p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary/30" aria-label="Close mobile menu">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
      </button>
    </div>
    <div class="px-6 py-2 space-y-2">
      <div id="mobile-user-section" class="space-y-4 opacity-0 transition-opacity duration-300" data-auth-ready="false">
        <!-- Authenticated User View -->
        <div id="mobile-user-authenticated" class="hidden flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 p-4 rounded-full">
          <div class="user-avatar ml-1 w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
            <!-- Mobile avatar will be injected here -->
          </div>
          <!-- Direct implementation for mobile avatar -->
          <div id="mobileDirectAvatar" class="hidden w-12 h-12 rounded-full overflow-hidden"></div>
          <div class="flex flex-col">
            <p id="mobile-user-name" class="text-lg font-semibold text-gray-900 dark:text-white"></p>
            <p id="mobile-user-email" class="text-sm text-gray-500 dark:text-gray-400"></p>
          </div>
        </div>
        <!-- Navigation Links -->
        <nav class="space-y-2" aria-label="Mobile Navigation">
          <!-- Mobile Nav links will be injected here -->
        </nav>
        <!-- Tools Section -->
        <div class="space-y-2">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 px-4">Tools</h3>
          <nav id="mobile-tools-menu" class="space-y-2">
            <!-- Mobile Tool links will be injected here -->
          </nav>
        </div>
        <!-- Theme Toggle -->
        <div class="space-y-4">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 px-4">Appearance</h3>
          <div class="grid grid-cols-2 gap-4">
            <button data-theme="light" class="theme-toggle-btn w-full btn btn-outline flex items-center justify-center space-x-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m3.343-5.657L5.929 5.929m12.728 12.728L18.071 18.07M12 7a5 5 0 110 10 5 5 0 010-10z"></path></svg>
              <span>Light</span>
            </button>
            <button data-theme="dark" class="theme-toggle-btn w-full btn btn-outline flex items-center justify-center space-x-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path></svg>
              <span>Dark</span>
            </button>
          </div>
        </div>
        <!-- Login/Signup & Signout Buttons -->
        <div id="mobile-user-unauthenticated" class="space-y-6">
          <a href="/login" class="mt-10 w-full btn bg-gray-900 dark:bg-white text-white dark:text-gray-950 p-4 rounded-full flex items-center justify-center space-x-2 hover:bg-primary-dark transition-colors duration-200 shadow-md">
            <span>Sign In</span>
          </a>
        </div>
        <div id="mobile-signout-container">
          <button id="mobile-signout-btn" class="mt-10 w-full btn bg-gray-900 text-white dark:bg-white/90 dark:text-gray-950 p-4 rounded-full flex items-center justify-center space-x-2 hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors duration-200 shadow-sm">
            <span>Sign Out</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  import { signOut, getAuth, reload } from "firebase/auth";
  import { firebaseInstance } from "../lib/firebase";
  import { authStore, isAuthenticated } from "../lib/authStore";
  import { TierManagementService } from "../lib/tierManagement";
  import { preloadFirebaseData } from "../lib/firebasePreloader";

  // --- CONFIGURATION ---
  const CONFIG = {
    toolLinks: [
      { name: "Job Research", href: "/job-research" },
      { name: "Resume Tailoring", href: "/resume" },
      { name: "Cover Letter Generator", href: "/cover-letter" },
      { name: "Job Tracker", href: "/job-tracker" },
      { name: "Interview Preparation", href: "/interview-prep" },
      { name: "LinkedIn Optimizer", href: "/linkedin" },
    ],
    mobileNavLinks: [
        { name: "Dashboard", href: "/dashboard", requiresAuth: true },
        { name: "Settings", href: "/settings", requiresAuth: true },
        { name: "Plans", href: "/pricing", requiresAuth: false },
    ]
  };

  // --- DOM ELEMENT CACHE ---
  const elements = {
    header: document.getElementById("mainHeader"),
    // Desktop
    authStateContainer: document.getElementById("authStateContainer"),
    authButtons: document.getElementById("authButtons"),
    userProfileDropdown: document.getElementById("userProfileDropdown"),
    desktopAvatarWrapper: document.getElementById("desktopAvatarWrapper"),
    directAvatarTest: document.getElementById("directAvatarTest"),
    dropdownFullName: document.getElementById("dropdownFullName"),
    dropdownEmail: document.getElementById("dropdownEmail"),
    dropdownTier: document.getElementById("dropdownTier"),
    dashboardNavLink: document.getElementById("dashboardNavLink"),
    // Dropdowns
    toolsDropdown: {
      button: document.getElementById("toolsDropdownButton"),
      menu: document.getElementById("toolsDropdownMenu"),
    },
    userDropdown: {
      button: document.getElementById("userDropdownButton"),
      menu: document.getElementById("userDropdownMenu"),
    },
    // Mobile
    mobileMenu: {
      button: document.getElementById("mobileMenuButton"),
      overlay: document.getElementById("mobile-menu-overlay"),
      panel: document.getElementById("mobile-menu-panel"),
      closeBtn: document.getElementById("mobile-menu-close-btn"),
    },
    mobileUserSection: document.getElementById("mobile-user-section"),
    mobileUserAuthenticated: document.getElementById("mobile-user-authenticated"),
    mobileUserUnauthenticated: document.getElementById("mobile-user-unauthenticated"),
    mobileUserName: document.getElementById("mobile-user-name"),
    mobileUserEmail: document.getElementById("mobile-user-email"),
    mobileDirectAvatar: document.getElementById("mobileDirectAvatar"),
    mobileNavContainer: document.querySelector('[aria-label="Mobile Navigation"]'),
    mobileToolsContainer: document.getElementById("mobile-tools-menu"),
    mobileSignOutContainer: document.getElementById("mobile-signout-container"),
    // Shared
    signOutButtons: document.querySelectorAll("#signOutButton, #mobile-signout-btn"),
    themeToggleButtons: document.querySelectorAll(".theme-toggle-btn"),
  };

  // --- RENDER FUNCTIONS ---
  function renderDynamicLinks() {
    // Desktop Tools
    if (elements.toolsDropdown.menu) {
      elements.toolsDropdown.menu.innerHTML = CONFIG.toolLinks.map(link => `
        <a href="#" data-tool-href="${link.href}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md">
          ${link.name}
        </a>
      `).join('');
    }

    // Mobile Tools
    if (elements.mobileToolsContainer) {
      elements.mobileToolsContainer.innerHTML = CONFIG.toolLinks.map(link => `
        <a href="#" data-tool-href="${link.href}" class="mobile-nav-link text-gray-950 dark:text-white group flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
          <span>${link.name}</span>
        </a>
      `).join('');
    }

    // Mobile Nav
    if (elements.mobileNavContainer) {
      elements.mobileNavContainer.innerHTML = CONFIG.mobileNavLinks.map(link => `
          <a href="${link.href}" class="mobile-nav-link group flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors" data-requires-auth="${link.requiresAuth}">
              <span>${link.name}</span>
          </a>
      `).join('');
    }
  }

  // --- UI STATE MANAGEMENT ---
  function manageAuthUI(state) {
    const { user, loading } = state;
    const authenticated = !!user;

    const authReadyContainers = [elements.authStateContainer, elements.mobileUserSection];
    authReadyContainers.forEach(el => {
      if (!el) return;
      el.classList.toggle("opacity-0", loading);
      el.setAttribute("data-auth-ready", String(!loading));
    });

    if (loading) return;

    // Desktop UI
    elements.authButtons?.classList.toggle("hidden", authenticated);
    elements.userProfileDropdown?.classList.toggle("hidden", !authenticated);
    elements.dashboardNavLink?.classList.toggle("hidden", !authenticated);

    // Mobile UI
    elements.mobileUserAuthenticated?.classList.toggle("hidden", !authenticated);
    elements.mobileUserUnauthenticated?.classList.toggle("hidden", authenticated);
    elements.mobileSignOutContainer?.classList.toggle("hidden", !authenticated);

    // Update user-specific details
    if (user) {
      updateUserDisplayDetails(user);
    }

    // Update mobile nav link visibility
    document.querySelectorAll('.mobile-nav-link[data-requires-auth="true"]').forEach(link => {
        link.classList.toggle('hidden', !authenticated);
    });
  }

  async function updateUserDisplayDetails(user) {
    if (!user) return;

    const userTierProfile = await TierManagementService.getUserTierProfile(user.uid).catch(err => {
        console.error("Error fetching user tier profile:", err);
        return { currentTier: 'free' }; // Default to free on error
    });
    
    const isPro = ['pro', 'pro_quarterly'].includes(userTierProfile.currentTier);
    const displayName = user.displayName || user.email?.split("@")[0] || "User";
    const firstLetter = displayName.charAt(0).toUpperCase();

    // Update Dropdown Info
    if (elements.dropdownFullName) elements.dropdownFullName.textContent = displayName;
    if (elements.dropdownEmail) elements.dropdownEmail.textContent = user.email || '';
    if (elements.dropdownTier) {
      elements.dropdownTier.innerHTML = isPro
        ? `<span class="pro-badge bg-[var(--color-purple)] text-white text-xs font-bold px-2 py-0.5 rounded-full">PRO</span>`
        : `<span class="free-badge bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-white text-xs font-bold px-2 py-0.5 rounded-full">Starter</span>`;
    }

    // HYBRID APPROACH: Try to load Google image but with reliable fallback
    const isGoogleUser = user.providerData.some(p => p.providerId === 'google.com');
    
    // Create a fallback avatar element
    const createFallbackAvatar = (size) => {
        const sizeClass = size === 'mobile' ? 'w-12 h-12' : 'w-8 h-8';
        const textSize = size === 'mobile' ? 'text-xl' : 'text-lg';
        return `<div class="${sizeClass} rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-800 dark:text-white">
            <span class="font-semibold ${textSize}">${firstLetter}</span>
        </div>`;
    };
    
    // Create an image element with fallback
    const createAvatarWithFallback = (size, photoURL) => {
        const sizeClass = size === 'mobile' ? 'w-12 h-12' : 'w-8 h-8';
        // Add cache busting to URL to prevent stale images
        const cacheBuster = `?cb=${Date.now()}`;
        const imageUrl = photoURL + cacheBuster;
        
        return `
            <div class="${sizeClass} rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700">
                <img 
                    src="${imageUrl}" 
                    alt="User Avatar" 
                    class="w-full h-full object-cover"
                    onerror="this.style.display='none'; this.parentNode.innerHTML='<span class=\\'font-semibold ${size === 'mobile' ? 'text-xl' : 'text-lg'} flex items-center justify-center h-full text-gray-800 dark:text-white\\'>${firstLetter}</span>';"
                    referrerpolicy="no-referrer"
                    crossorigin="anonymous"
                />
            </div>
        `;
    };
    
    // Update desktop avatar
    if (elements.desktopAvatarWrapper) {
        elements.desktopAvatarWrapper.classList.remove('hidden');
        
        // Try to load Google image if it's a Google user and we have a photoURL
        if (isGoogleUser && user.photoURL) {
            elements.desktopAvatarWrapper.innerHTML = createAvatarWithFallback('desktop', user.photoURL);
        } else {
            elements.desktopAvatarWrapper.innerHTML = createFallbackAvatar('desktop');
        }
            
        // Add pro styling if needed
        if (isPro) {
            elements.desktopAvatarWrapper.classList.add('ring-2', 'ring-[var(--color-purple)]', 'ring-offset-2', 'ring-offset-white', 'dark:ring-offset-gray-900');
        }
    }
    
    // Update mobile avatar
    const mobileAvatar = elements.mobileUserAuthenticated?.querySelector('.user-avatar');
    if (mobileAvatar) {
        mobileAvatar.classList.remove('hidden');
        
        // Try to load Google image if it's a Google user and we have a photoURL
        if (isGoogleUser && user.photoURL) {
            mobileAvatar.innerHTML = createAvatarWithFallback('mobile', user.photoURL);
        } else {
            mobileAvatar.innerHTML = createFallbackAvatar('mobile');
        }
            
        // Add pro styling if needed
        if (isPro) {
            mobileAvatar.classList.add('ring-2', 'ring-[var(--color-purple)]', 'ring-offset-2', 'ring-offset-white', 'dark:ring-offset-gray-900');
        }
    }
    
    // Hide the direct avatar tests to avoid duplication
    if (elements.directAvatarTest) elements.directAvatarTest.classList.add('hidden');
    if (elements.mobileDirectAvatar) elements.mobileDirectAvatar.classList.add('hidden');
    
    // Always refresh the Google profile image on every page load
    if (isGoogleUser && user.photoURL) {
        // Force a refresh of the user profile to get the latest photoURL
        if (firebaseInstance?.auth?.currentUser) {
            firebaseInstance?.auth?.currentUser?.reload()
                .then(() => {
                    
                    // After reload, get the fresh photoURL
                    const freshPhotoURL = firebaseInstance?.auth?.currentUser?.photoURL;
                    if (freshPhotoURL && freshPhotoURL !== user.photoURL) {
                        
                        // Update the avatars with the fresh URL
                        if (elements.desktopAvatarWrapper) {
                            elements.desktopAvatarWrapper.innerHTML = createAvatarWithFallback('desktop', freshPhotoURL);
                        }
                        
                        if (mobileAvatar) {
                            mobileAvatar.innerHTML = createAvatarWithFallback('mobile', freshPhotoURL);
                        }
                    }
                })
                .catch(err => {
                    console.error('Failed to refresh user profile:', err);
                });
        }
    }

    // Update Mobile User Info
    if (elements.mobileUserName) elements.mobileUserName.textContent = displayName.split(" ")[0];
    if (elements.mobileUserEmail) elements.mobileUserEmail.textContent = user.email || '';
  }

  // --- EVENT HANDLERS & SETUP ---
  function setupDropdowns() {
    const dropdowns = [elements.toolsDropdown, elements.userDropdown];
    
    const toggleDropdown = (menu, button, forceClose = false) => {
        const isOpen = !menu.classList.contains('invisible');
        if (forceClose || isOpen) {
            menu.classList.add('invisible', 'opacity-0', 'scale-95');
            button.setAttribute('aria-expanded', 'false');
            button.querySelector('svg')?.classList.remove('rotate-180');
        } else {
            menu.classList.remove('invisible', 'opacity-0', 'scale-95');
            button.setAttribute('aria-expanded', 'true');
            button.querySelector('svg')?.classList.add('rotate-180');
        }
    };

    dropdowns.forEach(dropdown => {
        if (!dropdown.button || !dropdown.menu) return;
        dropdown.button.addEventListener('click', (e) => {
            e.stopPropagation();
            // Close other dropdowns before opening this one
            dropdowns.forEach(other => {
                if (other !== dropdown) toggleDropdown(other.menu, other.button, true);
            });
            toggleDropdown(dropdown.menu, dropdown.button);
        });
    });

    // Global click listener to close dropdowns
    document.addEventListener('click', () => {
        dropdowns.forEach(d => toggleDropdown(d.menu, d.button, true));
    });
  }

  function setupMobileMenu() {
    const { button, overlay, panel, closeBtn } = elements.mobileMenu;
    if (!button || !overlay || !panel || !closeBtn) return;

    const open = () => {
      overlay.classList.remove("invisible", "opacity-0");
      panel.classList.remove("translate-x-full");
      document.body.classList.add("overflow-hidden");
    };
    const close = () => {
      panel.classList.add("translate-x-full");
      overlay.classList.add("opacity-0");
      overlay.addEventListener('transitionend', () => overlay.classList.add('invisible'), { once: true });
      document.body.classList.remove("overflow-hidden");
    };

    button.addEventListener("click", open);
    closeBtn.addEventListener("click", close);
    overlay.addEventListener("click", (e) => e.target === overlay && close());
    document.addEventListener("keydown", (e) => e.key === "Escape" && close());
  }

  function setupThemeToggle() {
    const applyTheme = (theme) => {
      document.documentElement.classList.toggle("dark", theme === "dark");
      localStorage.setItem("color-theme", theme);
    };
    elements.themeToggleButtons.forEach(btn => {
      (btn as HTMLElement).addEventListener("click", () => applyTheme((btn as HTMLElement).dataset.theme));
    });
    applyTheme(localStorage.getItem("color-theme") || "light");
  }


  async function handleSignOut() {
    try {
      if (firebaseInstance?.auth) {
        await signOut(firebaseInstance.auth);
        window.location.href = "/login";
      }
    } catch (error) {
      console.error("Sign out failed", error);
    }
  }

  function setupActionListeners() {
    // Sign Out
    elements.signOutButtons.forEach(btn => btn.addEventListener("click", handleSignOut));

    // Tool Links (Event Delegation)
    document.body.addEventListener('click', async (e) => {
        const toolLink = (e.target as HTMLElement)?.closest('[data-tool-href]');
        if (!toolLink) return;

        e.preventDefault();
        const targetUrl = (toolLink as HTMLElement).dataset.toolHref;
        if (!targetUrl) return;

        if (await isAuthenticated()) {
            preloadFirebaseData();
            window.location.href = targetUrl;
        } else {
            window.location.href = `/login?redirect=${encodeURIComponent(targetUrl)}`;
        }
    });
  }

  function setupScrollBehavior() {
    let lastScrollTop = 0;
    const header = elements.header;
    if (!header) return;

    window.addEventListener("scroll", () => {
      let scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      if (scrollTop > lastScrollTop && scrollTop > header.offsetHeight) {
        // Downscroll
        header.classList.add("-translate-y-full");
      } else {
        // Upscroll
        header.classList.remove("-translate-y-full");
      }
      lastScrollTop = scrollTop <= 0 ? 0 : scrollTop; // For Mobile or negative scrolling
    }, false);
  }

  // --- INITIALIZATION ---
  function init() {
    renderDynamicLinks();
    setupDropdowns();
    setupMobileMenu();
    setupThemeToggle();
    setupActionListeners();
    setupScrollBehavior();
    authStore.subscribe(manageAuthUI);
  }

  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", init);
  } else {
    init();
  }
</script>

<style>
  #mainHeader {
    transition: transform 0.3s ease-in-out;
  }
  .mobile-nav-link.hidden {
    display: none;
  }
</style>