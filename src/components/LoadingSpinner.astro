---
interface Props {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'white' | 'gray' | 'black';
  label?: string;
  showLabel?: boolean;
  className?: string;
}

const {
  size = 'md',
  color = 'primary',
  label = 'Loading...',
  showLabel = false,
  className = ''
} = Astro.props;

// Size mappings
const sizeMap = {
  xs: 'h-3 w-3',
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12'
};

// Color mappings
const colorMap = {
  primary: 'text-primary',
  white: 'text-white',
  gray: 'text-gray-700 dark:text-gray-300',
  black: 'text-black dark:text-white'
};

const spinnerSize = sizeMap[size] || sizeMap.md;
const spinnerColor = colorMap[color] || colorMap.primary;
---

<div class={`inline-flex items-center justify-center ${className}`}>
  <svg 
    class={`animate-spin ${spinnerSize} ${spinnerColor}`} 
    xmlns="http://www.w3.org/2000/svg" 
    fill="none" 
    viewBox="0 0 24 24"
    aria-hidden="true"
  >
    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
    <path 
      class="opacity-75" 
      fill="currentColor" 
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    ></path>
  </svg>
  {showLabel && (
    <span class={`ml-2 text-sm font-medium ${spinnerColor}`}>{label}</span>
  )}
</div>
