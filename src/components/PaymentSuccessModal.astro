---
interface Props {
  isOpen: boolean;
}

const { isOpen } = Astro.props;
---

<div 
  id="paymentSuccessModal"
  class:list={[
    "fixed inset-0 z-50 flex items-center justify-center p-4 transition-all duration-300",
    isOpen ? "opacity-100 pointer-events-auto" : "opacity-0 pointer-events-none"
  ]}
>
  <!-- Backdrop -->
  <div class="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>
  
  <!-- Modal Content -->
  <div class="relative bg-white dark:bg-gray-900 rounded-2xl max-w-md w-full p-6 shadow-xl transform transition-all duration-300 scale-100">
    <!-- Success Icon -->
    <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30 mb-4">
      <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
      </svg>
    </div>
    
    <h3 class="text-xl font-semibold text-gray-900 dark:text-white text-center mb-2">
      Payment Successful!
    </h3>
    
    <p class="text-gray-600 dark:text-gray-300 text-center mb-6">
      Thank you for upgrading! Your pro features are now activated.
    </p>
    
    <!-- Action Button -->
    <div class="flex justify-center">
      <a
        href="/dashboard"
        id="continueButton"
        class="relative flex h-11 w-full max-w-xs rounded-full items-center justify-center px-6 bg-black text-white dark:bg-white dark:text-black before:absolute before:inset-0 before:rounded-full before:transition before:duration-300 hover:before:scale-105 active:duration-75 active:before:scale-95"
      >
        <span class="relative text-base font-semibold">Continue to Dashboard</span>
      </a>
    </div>
  </div>
</div>

<script>
  import { initializeFirebase } from '../lib/firebase';

  // Ensure Firebase is initialized
  initializeFirebase();

  // Get modal elements
  const modal = document.getElementById('paymentSuccessModal');
  const continueButton = document.getElementById('continueButton');

  // Close modal when clicking outside
  modal?.addEventListener('click', (e) => {
    if (e.target === modal) {
      window.location.href = '/dashboard';
    }
  });

  // Close on escape key
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && modal?.classList.contains('opacity-100')) {
      window.location.href = '/dashboard';
    }
  });
</script>
