---
// Dark Mode Toggle Component
const savedTheme = Astro.cookies.get('color-theme')?.value || 'light';
// Use server-side logic only, avoid client-side code in component initialization
const systemPrefersDark = savedTheme === 'dark';
---

<button
  id="dark-mode-toggle"
  type="button"
  aria-label="Toggle Dark Mode"
  class="p-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 transition-colors duration-300 ease-in-out hover:bg-gray-300 dark:hover:bg-gray-600 cursor-pointer"
  onclick="
    const button = document.getElementById('dark-mode-toggle');
    if (!button) {
      console.error('Dark mode toggle button not found');
      return;
    }

    const darkIcon = document.getElementById('theme-toggle-dark-icon');
    const lightIcon = document.getElementById('theme-toggle-light-icon');
    const html = document.documentElement;

    if (!darkIcon || !lightIcon) {
      console.error('Theme toggle icons not found');
      return;
    }

    const newTheme = window.ThemeManager.toggleTheme();

    if (newTheme === 'light') {
      darkIcon.classList.add('hidden');
      lightIcon.classList.remove('hidden');
    } else {
      lightIcon.classList.add('hidden');
      darkIcon.classList.remove('hidden');
    }

    // Dispatch a custom event for theme change
    window.dispatchEvent(new CustomEvent('theme-changed', {
      detail: { theme: newTheme }
    }));
  "
>
  <svg
    id="theme-toggle-dark-icon"
    class={`w-5 h-5 ${systemPrefersDark ? '' : 'hidden'}`}
    fill="currentColor"
    viewBox="0 0 20 20"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
  </svg>
  <svg
    id="theme-toggle-light-icon"
    class={`w-5 h-5 ${systemPrefersDark ? 'hidden' : ''}`}
    fill="currentColor"
    viewBox="0 0 20 20"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path>
  </svg>
</button>

<script>
  // Ensure the button is interactive
  document.addEventListener('astro:page-load', () => {
    const button = document.getElementById('dark-mode-toggle');
    // Removed console.log for production
  });

  // Listen for theme changes
  document.addEventListener('theme-changed', ((event: CustomEvent) => {
    // Removed console.log for production
  }) as EventListener);
</script>
