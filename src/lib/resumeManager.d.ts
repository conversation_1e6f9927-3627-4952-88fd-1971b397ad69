/**
 * Type declarations for the resumeManager module
 */

/**
 * Resume data interface
 */
export interface Resume {
  id: string;
  name: string;
  content: string;
  lastUpdated: number;
  userId?: string;
  createdAt: number;
}

/**
 * Shows the resume manager modal
 * @param modalId The ID for the modal
 * @param onResumeSelected Callback function called when a resume is selected
 */
export function showResumeManagerModal(
  modalId: string,
  onResumeSelected: (selectedResume: Resume | null) => void
): void;
