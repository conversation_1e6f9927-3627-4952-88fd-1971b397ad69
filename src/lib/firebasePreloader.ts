/**
 * Firebase Data Preloader
 *
 * This utility preloads common Firebase data in the background
 * while page transitions are happening, improving perceived performance.
 */

import { authService } from "./auth";
import { TierManagementService } from "./tierManagement";
import { firebaseInstance } from "./firebase";

/**
 * Data cache to store preloaded data
 */
interface DataCache {
  userProfile?: any;
  userTierProfile?: any;
  featureUsage?: any;
  lastUpdated: number;
}

// In-memory cache with 5-minute expiration
const dataCache: DataCache = {
  lastUpdated: 0
};

// Cache expiration time in milliseconds (5 minutes)
const CACHE_EXPIRATION = 5 * 60 * 1000;

/**
 * Preloads common Firebase data that will likely be needed
 * @returns Promise that resolves when preloading is complete
 */
export async function preloadFirebaseData(): Promise<void> {
  try {
    // Check if cache is still valid
    if (dataCache.lastUpdated > Date.now() - CACHE_EXPIRATION) {
      return;
    }

    // Start preloading data in the background without awaiting
    // This ensures page transitions aren't blocked by Firebase operations
    _preloadData().catch(err => {
      // Silently catch errors - preloading is just an optimization
      console.error("Background Firebase preload error:", err);
    });

    // Return immediately to not block page transitions
    return;
  } catch (error) {
    // Silently fail - preloading is just an optimization
    console.error("Error preloading Firebase data:", error);
  }
}

/**
 * Internal function to preload data and update the cache
 */
async function _preloadData(): Promise<void> {
  try {
    // Check if user is authenticated
    const user = await authService.getCurrentUser();

    if (!user) {
      // No user, so no data to preload
      return;
    }

    // Start multiple requests in parallel
    const [userTierProfile, allFeatureUsage] = await Promise.all([
      TierManagementService.getUserTierProfile(user.uid),
      TierManagementService.getAllFeatureUsage(user.uid)
    ]);

    // Update the cache
    dataCache.userProfile = user;
    dataCache.userTierProfile = userTierProfile;
    dataCache.featureUsage = allFeatureUsage;
    dataCache.lastUpdated = Date.now();

    console.log("Firebase data preloaded successfully");
  } catch (error) {
    console.error("Error in _preloadData:", error);
    throw error;
  }
}

/**
 * Gets preloaded user profile data if available
 * @returns The cached user profile or null
 */
export function getPreloadedUserProfile(): any | null {
  if (dataCache.lastUpdated > Date.now() - CACHE_EXPIRATION && dataCache.userProfile) {
    return dataCache.userProfile;
  }
  return null;
}

/**
 * Gets preloaded user tier profile if available
 * @returns The cached user tier profile or null
 */
export function getPreloadedUserTierProfile(): any | null {
  if (dataCache.lastUpdated > Date.now() - CACHE_EXPIRATION && dataCache.userTierProfile) {
    return dataCache.userTierProfile;
  }
  return null;
}

/**
 * Gets preloaded feature usage data if available
 * @returns The cached feature usage data or null
 */
export function getPreloadedFeatureUsage(): any | null {
  if (dataCache.lastUpdated > Date.now() - CACHE_EXPIRATION && dataCache.featureUsage) {
    return dataCache.featureUsage;
  }
  return null;
}

/**
 * Clears the preloaded data cache
 */
export function clearPreloadedData(): void {
  dataCache.userProfile = undefined;
  dataCache.userTierProfile = undefined;
  dataCache.featureUsage = undefined;
  dataCache.lastUpdated = 0;
}
