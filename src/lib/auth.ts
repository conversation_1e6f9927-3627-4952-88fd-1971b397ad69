import {
  getAuth,
  signInWithPopup,
  signInWithEmailAndPassword,
  sendPasswordResetEmail,
  sendEmailVerification,
  sendSignInLinkToEmail,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
  type User,
  type AuthError,
  setPersistence,
  browserLocalPersistence,
  createUserWithEmailAndPassword,
  updatePassword,
} from "firebase/auth";
import { PersistentDocumentService } from "./persistentDocumentService";
import { TierManagementService } from "./tierManagement";
import {
  getFirestore,
  doc,
  getDoc,
  setDoc,
  Timestamp,
} from "firebase/firestore";
import { initializeFirebase } from "./firebase";
import { authStore } from "./authStore";

const firebaseInstance = initializeFirebase();
const auth = firebaseInstance?.auth;
const app = firebaseInstance?.app;

// Custom type guard to check if error is an AuthError
function isAuthError(error: unknown): error is AuthError {
  return error instanceof Error && "code" in error && "message" in error;
}

// Get base URL for API endpoints
function getBaseUrl(): string {
  // For server-side rendering or environments without window
  if (typeof window === "undefined") {
    return import.meta.env.PUBLIC_BASE_URL || "http://localhost:4321";
  }

  // For client-side rendering, use the current origin to ensure absolute URLs
  return window.location.origin;
}

// Centralized API request function
async function apiRequest(
  endpoint: string,
  method: "POST" | "GET",
  body?: any,
  token?: string // Optional: pass token for Authorization header
) {
  try {
    const url = `${getBaseUrl()}/api/auth/${endpoint}`;
    console.log("[apiRequest Debug] Sending request:", {
      // Added debug log
      url,
      method,
      tokenProvided: !!token,
      bodyKeys: body ? Object.keys(body) : null,
    });
    // console.log("API Request", { // Removed for prod
    //   url,
    //   method,
    //   body: body ? JSON.stringify(body) : "No body",
    //   timestamp: new Date().toISOString(),
    // });

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    const response = await fetch(url, {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("API Request Failed", { // Keep minimal for prod
        status: response.status,
        statusText: response.statusText,
        errorText,
        timestamp: new Date().toISOString(),
      });
      throw new Error(`API request failed: ${errorText}`); // Keep error throwing
    }

    return await response.json();
  } catch (error) {
    console.error("API Request Error", { // Keep minimal for prod
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : "No stack trace",
      timestamp: new Date().toISOString(),
    });
    throw error; // Keep error throwing
  }
}

class AuthService {
  private app: any;
  private _auth: any;
  private initialized: boolean = false;
  private googleProvider: GoogleAuthProvider;
  private loginCallbacks: (() => void)[] = [];

  constructor() {
    this.app = app;
    this._auth = auth;
    this.googleProvider = new GoogleAuthProvider();
    // Add scopes for profile and email to ensure we get the best profile image URL
    this.googleProvider.addScope('profile');
    this.googleProvider.addScope('email');
    // Request offline access to get refresh token
    this.googleProvider.setCustomParameters({
      prompt: 'select_account'
    });
    this.initialize();
  }

  private initPromise: Promise<void> | null = null;

  private initialize() {
    if (!this.initPromise) {
      this.initPromise = new Promise((resolve) => {
        try {
          // Setup auth state listener
          onAuthStateChanged(
            this._auth,
            (user) => {
              this.initialized = true;
              authStore.set({
                user,
                loading: false,
                subscriptionExpired: false,
              });
              this.loginCallbacks.forEach((callback) => callback());
              resolve();
            },
            (error) => {
              console.error("[AuthService] Auth state change error:", error); // Keep minimal for prod
              this.initialized = false;
              resolve(); // Resolve anyway to prevent hanging
            }
          );
        } catch (error) {
          console.error("[AuthService] Initialization error:", error);
          resolve(); // Resolve anyway to prevent hanging
        }
      });
    }
    return this.initPromise;
  }

  public get auth(): any {
    return this._auth;
  }

  async getCurrentUser(): Promise<User | null> {
    try {
      // Wait for initialization
      await this.initPromise;

      // Get user from auth
      const user = this._auth.currentUser;

      // if (!user && !this.initialized) { // Removed console.warn for prod
      //   console.warn("[AuthService] Auth not fully initialized");
      // }

      return user;
    } catch (error) {
      console.error("[AuthService] Error getting current user:", error);
      return null;
    }
  }

  public async signInWithGoogle(): Promise<User | null> {
    try {
      // console.log("Starting Google Sign-In process", { // Removed for prod
      //   timestamp: new Date().toISOString(),
      //   windowDefined: typeof window !== "undefined",
      //   baseUrl: getBaseUrl(),
      // });

      // Ensure browser persistence is set
      await setPersistence(this._auth, browserLocalPersistence);

      const result = await signInWithPopup(this._auth, this.googleProvider);

      // Additional validation
      if (!result.user) {
        throw new Error("No user returned from Google sign-in");
      }

      // Email reuse eligibility check removed since account deletion is disabled

      // Get and set the auth token via API
      const token = await result.user.getIdToken();
      console.log(
        "[AuthService Debug] Got Firebase ID Token:",
        token ? token.substring(0, 20) + "..." : "null"
      ); // Log token (truncated)
      // Send token in Authorization header
      console.log("[AuthService Debug] Calling API /login with token...");
      await apiRequest("login", "POST", {}, token);

      // Initialize user data in Firestore
      await this._initializeUserProfile(result.user);

      return result.user;
    } catch (error) {
      let userFriendlyMessage =
        "An unexpected error occurred during Google Sign-In. Please try again.";

      console.error("[Google Sign-In Error]", {
        errorType: error instanceof Error ? error.name : "Unknown Error",
        message: error instanceof Error ? error.message : "No error message",
        stack: error instanceof Error ? error.stack : "No stack trace",
        code: (error as any)?.code,
        timestamp: new Date().toISOString(),
      });

      if (isAuthError(error)) {
        switch ((error as any).code) {
          case "auth/popup-closed-by-user":
            userFriendlyMessage =
              "Google Sign-In was cancelled. Please try again.";
            break;
          case "auth/popup-blocked":
            userFriendlyMessage =
              "Popup blocked. Please allow popups for this site.";
            break;
          case "auth/network-request-failed":
            userFriendlyMessage =
              "Network error. Please check your internet connection.";
            break;
          case "auth/cancelled-popup-request":
            userFriendlyMessage =
              "Sign-in request was interrupted. Please try again.";
            break;
        }
      }

      const authError = new Error(userFriendlyMessage);
      authError.name = "GoogleSignInError";
      throw authError;
    }
  }

  public async signUpWithEmailAndPassword(
    email: string,
    password: string
  ): Promise<User | null> {
    try {
      // Set persistence before sign up
      await setPersistence(this._auth, browserLocalPersistence);

      // Use Firebase's createUserWithEmailAndPassword method
      const userCredential = await createUserWithEmailAndPassword(
        this._auth,
        email,
        password
      );
      const user = userCredential.user;

      // Initialize user profile
      await this._initializeUserProfile(user);

      // Get and set the auth token via API
      const token = await user.getIdToken();
      await apiRequest("login", "POST", {}, token);

      // Send email verification
      await sendEmailVerification(user);

      return user;
    } catch (error) {
      throw error;
    }
  }

  public async signInWithEmailAndPassword(
    email: string,
    password: string
  ): Promise<User | null> {
    try {
      // Set persistence before sign in
      await setPersistence(this._auth, browserLocalPersistence);

      const userCredential = await signInWithEmailAndPassword(
        this._auth,
        email,
        password
      );
      const user = userCredential.user;

      // Get and set the auth token via API
      const token = await user.getIdToken();
      await apiRequest("login", "POST", {}, token);

      // Log successful login
      // console.log("Email sign-in successful", { // Removed for prod
      //   uid: user.uid,
      //   email: user.email,
      //   timestamp: new Date().toISOString(),
      // });

      return user;
    } catch (error) {
      // User-friendly error messages
      let userFriendlyMessage =
        "An unexpected error occurred. Please try again.";

      if (isAuthError(error)) {
        switch (error.code) {
          case "auth/invalid-credential":
            userFriendlyMessage =
              "Invalid email or password. Please check and try again.";
            break;
          case "auth/user-not-found":
            userFriendlyMessage =
              "No account found with this email. Please sign up.";
            break;
          case "auth/wrong-password":
            userFriendlyMessage = "Incorrect password. Please try again.";
            break;
          case "auth/too-many-requests":
            userFriendlyMessage =
              "Too many login attempts. Please try again later.";
            break;
          case "auth/network-request-failed":
            userFriendlyMessage =
              "Network error. Please check your internet connection.";
            break;
        }
      }

      // Comprehensive error logging
      console.error("Email Sign-In Error", { // Keep minimal for prod
        errorType: error instanceof Error ? error.name : "Unknown Error",
        message: error instanceof Error ? error.message : "No error message",
        stack: error instanceof Error ? error.stack : "No stack trace",
        userFriendlyMessage,
        timestamp: new Date().toISOString(),
      });

      // Throw with user-friendly message
      const authError = new Error(userFriendlyMessage);
      authError.name = "AuthenticationError";
      throw authError;
    }
  }

  public async resetPassword(email: string): Promise<void> {
    try {
      // Use Firebase's sendPasswordResetEmail method
      await sendPasswordResetEmail(this._auth, email);
    } catch (error) {
      throw error;
    }
  }

  public async sendSignInLinkToEmail(email: string): Promise<void> {
    const actionCodeSettings = {
      url: `${getBaseUrl()}/finish-login`,
      handleCodeInApp: true,
    };
    try {
      console.log(`[AuthService] Sending sign-in link to ${email} with URL: ${actionCodeSettings.url}`);
      await sendSignInLinkToEmail(this._auth, email, actionCodeSettings);
      window.localStorage.setItem('emailForSignIn', email);
      console.log(`[AuthService] Sign-in link sent successfully to ${email}`);
    } catch (error) {
        console.error("[AuthService] Error sending sign-in link:", {
        errorType: error instanceof Error ? error.name : "Unknown Error",
        message: error instanceof Error ? error.message : "No error message",
        stack: error instanceof Error ? error.stack : "No stack trace",
        code: (error as any)?.code,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  public async updatePassword(password: string): Promise<void> {
    const user = this._auth.currentUser;
    if (user) {
      try {
        await updatePassword(user, password);
      } catch (error) {
        throw error;
      }
    } else {
      throw new Error("No user is signed in to update the password.");
    }
  }

  public async signOut(): Promise<void> {
    try {
      await signOut(this._auth);
      authStore.set({ user: null, loading: false, subscriptionExpired: false });
      // Clear auth cookie via API
      await apiRequest("logout", "POST");
    } catch (error) {
      throw error; // Keep error throwing
    }
  }

  public async setSessionCookie(token: string): Promise<void> {
    try {
      await apiRequest("login", "POST", {}, token);
    } catch (error) {
      console.error("[AuthService] Error setting session cookie:", error);
      throw new Error("Failed to set session cookie.");
    }
  }

  private getFirestoreInstance() {
    return getFirestore(this.app);
  }

  public addLoginCallback(callback: () => void) {
    this.loginCallbacks.push(callback);
  }

  private async _initializeUserProfile(user: User) {
    const db = this.getFirestoreInstance();
    const userRef = doc(db, "users", user.uid);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      const timestamp = Timestamp.now();
      await setDoc(userRef, {
        email: user.email,
        displayName: user.displayName || user.email?.split('@')[0] || 'User',
        photoURL: user.photoURL,
        createdAt: timestamp,
        lastLoginAt: timestamp,
      });

      await TierManagementService.initUserTierProfile(user.uid);
    }
  }
}

const authService = new AuthService();

async function onUserLogin() {
  try {
    await PersistentDocumentService.syncDocuments();

    const auth = getAuth();
    const user = auth.currentUser;

    if (user) {
      const userId = user.uid;
      const subscriptionProfile =
        await TierManagementService.getUserTierProfile(userId);

      const startDate = subscriptionProfile.subscriptionStartDate;
      const endDate = subscriptionProfile.subscriptionEndDate;
      const paymentStatus = subscriptionProfile.paymentStatus;

      if (
        paymentStatus === "active" &&
        endDate &&
        endDate.toDate() < new Date()
      ) {
        console.log(
          `[Subscription Check] Subscription expired. Downgrading user tier...`
        );
        await TierManagementService.downgradeUserTier(userId);
      } else {
      }
    }
  } catch (error) {
    console.error(
      "Error syncing documents or checking subscription expiry on login:",
      error
    );
  }
}

authService.addLoginCallback(onUserLogin);

export {
  getAuth,
  signInWithPopup,
  signInWithEmailAndPassword,
  sendPasswordResetEmail,
  sendSignInLinkToEmail,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
  type User,
  type AuthError,
  setPersistence,
  browserLocalPersistence,
  createUserWithEmailAndPassword,
  updatePassword,
} from "firebase/auth";

export { authService, type AuthService };
