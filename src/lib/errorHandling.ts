/**
 * Shows an error modal with the specified ID and message
 * @param id The ID of the error modal to show
 * @param title Optional title for the error modal
 * @param message Optional message for the error modal
 * @param options Additional options for the error modal
 * @returns true if the modal was found and shown, false otherwise
 */
export function showErrorModal(
  id: string = 'errorModal',
  title?: string,
  message?: string,
  options?: {
    autoClose?: boolean;
    autoCloseDelay?: number;
  }
): boolean {
  // Check if the error modal functions exist
  if (window.errorModalFunctions && window.errorModalFunctions[id]) {
    // Update the message if provided
    if (message && window.errorModalFunctions[id].updateMessage) {
      window.errorModalFunctions[id].updateMessage(message);
    }

    // Show the modal
    window.errorModalFunctions[id].show();

    // Set up auto-close if requested
    if (options?.autoClose) {
      setTimeout(() => {
        if (window.errorModalFunctions && window.errorModalFunctions[id]) {
          window.errorModalFunctions[id].hide();
        }
      }, options.autoCloseDelay || 5000);
    }

    return true;
  }

  // If the modal doesn't exist yet, create it dynamically
  if (!document.getElementById(id)) {
    createDynamicErrorModal(id, title, message);

    // Set up auto-close if requested
    if (options?.autoClose) {
      setTimeout(() => {
        hideErrorModal(id);
      }, options.autoCloseDelay || 5000);
    }

    return true;
  }

  return false;
}

/**
 * Hides the error modal with the specified ID
 * @param id The ID of the error modal to hide
 * @returns true if the modal was found and hidden, false otherwise
 */
export function hideErrorModal(id: string = 'errorModal'): boolean {
  if (window.errorModalFunctions && window.errorModalFunctions[id]) {
    window.errorModalFunctions[id].hide();
    return true;
  }

  const modal = document.getElementById(id);
  if (modal) {
    modal.classList.remove('opacity-100', 'pointer-events-auto');
    modal.classList.add('opacity-0', 'pointer-events-none');
    return true;
  }

  return false;
}

/**
 * Creates a dynamic error modal with the specified ID, title, and message
 * @param id The ID for the new error modal
 * @param title Optional title for the error modal
 * @param message Optional message for the error modal
 */
function createDynamicErrorModal(
  id: string,
  title?: string,
  message?: string
): void {
  const modalHTML = `
    <div
      id="${id}"
      class="fixed inset-0 z-[60] flex items-center justify-center bg-black/50 backdrop-blur-sm p-4 transition-all duration-300 opacity-100 pointer-events-auto"
    >
      <div
        class="bg-white dark:bg-gray-900 rounded-2xl p-6 max-w-md w-full text-center transform transition-all duration-300 ease-out scale-100 opacity-100"
      >
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-bold text-red-600 dark:text-red-400">${title || 'Error'}</h2>
          <button
            id="${id}-close-button"
            class="text-gray-500 hover:text-gray-900 dark:hover:text-white"
            aria-label="Close error modal"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <p class="text-gray-700 dark:text-gray-300 mb-6" id="${id}-message">${message || 'An unexpected error occurred. Please try again.'}</p>
        <div class="flex justify-end">
          <button
            id="${id}-close-button-bottom"
            class="px-4 py-2 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  `;

  const modalContainer = document.createElement('div');
  modalContainer.innerHTML = modalHTML;
  document.body.appendChild(modalContainer);

  // Set up event listeners
  const modal = document.getElementById(id);
  const closeButtons = [
    document.getElementById(`${id}-close-button`),
    document.getElementById(`${id}-close-button-bottom`)
  ];

  // Handle close button clicks
  closeButtons.forEach(button => {
    button?.addEventListener('click', () => {
      hideErrorModal(id);
    });
  });

  // Close modal when clicking outside
  modal?.addEventListener('click', (e) => {
    if (e.target === modal) {
      hideErrorModal(id);
    }
  });

  // Set up functions for external access
  if (window.errorModalFunctions === undefined) {
    window.errorModalFunctions = {};
  }

  window.errorModalFunctions[id] = {
    show: () => {
      const modal = document.getElementById(id);
      if (modal) {
        modal.classList.remove('opacity-0', 'pointer-events-none');
        modal.classList.add('opacity-100', 'pointer-events-auto');
      }
    },
    hide: () => {
      hideErrorModal(id);
    },
    updateMessage: (newMessage: string) => {
      const messageEl = document.getElementById(`${id}-message`);
      if (messageEl) {
        messageEl.textContent = newMessage;
      }
    }
  };
}

// Extend the Window interface to include our custom properties
declare global {
  interface Window {
    errorModalFunctions?: {
      [key: string]: {
        show: () => void;
        hide: () => void;
        updateMessage?: (message: string) => void;
      };
    };
  }
}
