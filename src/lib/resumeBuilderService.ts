// Define the structure of the resume data
export interface ResumeData {
  id: string | null;
  selectedTemplate: string;
  layout: 'one-column' | 'two-column';
  fontFamily: string;
  fontSize: string;
  lineSpacing: string;
  bodyTextColor: string;
  headingColor: string | null;
  margins: string;
  sectionSpacing: string;
  headingStyles: {
    h1FontSize: string;
    h2FontSize: string;
    h1FontWeight: string;
    h2FontWeight: string;
    underlined: boolean;
    allCaps: boolean;
    horizontalRule: boolean;
  };
  contactInfoIcons: boolean;
  sectionVisibility: {
    summary: boolean;
    workExperience: boolean;
    education: boolean;
    skills: boolean;
  };
  dateFormat: string;
  sectionsOrder: string[];
  metadata: {
    name: string;
  };
  contactInfo: {
    fullName: string;
    email: string;
    phone: string;
    location: string;
    linkedin: string;
    portfolio: string;
  };
  summary: string;
  workExperience: {
    id: string;
    jobTitle: string;
    company: string;
    location: string;
    startDate: string;
    endDate: string;
    bulletPoints: { id: string; text: string }[];
  }[];
  education: {
    id: string;
    institution: string;
    degree: string;
    fieldOfStudy: string;
    graduationDate: string;
    details: string;
  }[];
  skills: string[];
  customSections: {
    id: string;
    title: string;
    content: string;
    visible: boolean;
  }[];
}

import { map } from 'nanostores';

const defaultStyles = {
  layout: 'one-column' as const,
  fontFamily: 'Calibri, sans-serif',
  fontSize: '11pt',
  lineSpacing: '1.15',
  bodyTextColor: '#374151', // gray-700
  headingColor: null,
  margins: 'normal',
  sectionSpacing: 'normal',
  headingStyles: {
    h1FontSize: '2.25rem',
    h2FontSize: '1.25rem',
    h1FontWeight: '700',
    h2FontWeight: '600',
    underlined: false,
    allCaps: false,
    horizontalRule: false,
  },
  contactInfoIcons: false,
  dateFormat: 'long',
};

export const resumeData = map<ResumeData>({
  id: null,
  selectedTemplate: 'reverse-chronological',
  ...defaultStyles,
  sectionVisibility: {
    summary: true,
    workExperience: true,
    education: true,
    skills: true,
  },
  sectionsOrder: ['summary', 'workExperience', 'education', 'skills', 'customSections'],
  metadata: {
    name: 'Sample Resume',
  },
  contactInfo: {
    fullName: 'John Doe',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'New York, NY',
    linkedin: 'https://linkedin.com/in/johndoe',
    portfolio: 'https://johndoe.dev',
  },
  summary: 'Experienced software developer with 5+ years of expertise in full-stack development, specializing in React, Node.js, and cloud technologies. Passionate about creating scalable solutions and mentoring junior developers.',
  workExperience: [
    {
      id: crypto.randomUUID(),
      jobTitle: 'Senior Software Engineer',
      company: 'Tech Solutions Inc.',
      location: 'New York, NY',
      startDate: '2022-01',
      endDate: 'Present',
      bulletPoints: [
        { id: crypto.randomUUID(), text: 'Led development of microservices architecture serving 1M+ users.' },
        { id: crypto.randomUUID(), text: 'Implemented CI/CD pipelines reducing deployment time by 60%.' },
        { id: crypto.randomUUID(), text: 'Mentored 3 junior developers and conducted code reviews.' }
      ]
    },
    {
      id: crypto.randomUUID(),
      jobTitle: 'Software Developer',
      company: 'StartupXYZ',
      location: 'San Francisco, CA',
      startDate: '2020-06',
      endDate: '2021-12',
      bulletPoints: [
        { id: crypto.randomUUID(), text: 'Developed responsive web applications using React and Node.js.' },
        { id: crypto.randomUUID(), text: 'Collaborated with design team to implement pixel-perfect UI components.' },
        { id: crypto.randomUUID(), text: 'Optimized database queries improving performance by 40%.' }
      ]
    }
  ],
  education: [
    {
      id: crypto.randomUUID(),
      institution: 'University of Technology',
      degree: 'Bachelor of Science',
      fieldOfStudy: 'Computer Science',
      graduationDate: '2020-05',
      details: 'Graduated Magna Cum Laude. Relevant coursework: Data Structures, Algorithms, Software Engineering, Database Systems.'
    }
  ],
  skills: ['JavaScript', 'React', 'Node.js', 'Python', 'AWS', 'Docker', 'PostgreSQL', 'Git', 'Agile/Scrum'],
  customSections: [],
});

// --- Setter Functions for Customizations ---

export function setLayout(layout: 'one-column' | 'two-column') {
    resumeData.setKey('layout', layout);
}

export function setFontFamily(font: string) {
    resumeData.setKey('fontFamily', font);
}

export function setFontSize(size: string) {
    resumeData.setKey('fontSize', size);
}

export function setLineSpacing(spacing: string) {
    resumeData.setKey('lineSpacing', spacing);
}

export function setBodyTextColor(color: string) {
    resumeData.setKey('bodyTextColor', color);
}

export function setHeadingColor(color: string | null) {
    resumeData.setKey('headingColor', color);
}

export function setMargins(marginSize: string) {
    resumeData.setKey('margins', marginSize);
}

export function setSectionSpacing(spacing: string) {
    resumeData.setKey('sectionSpacing', spacing);
}

export function setHeadingStyle(style: keyof ResumeData['headingStyles'], value: boolean | string) {
    const currentStyles = resumeData.get().headingStyles;
    resumeData.setKey('headingStyles', { ...currentStyles, [style]: value });
}

export function setContactInfoIcons(visible: boolean) {
    resumeData.setKey('contactInfoIcons', visible);
}

export function setSectionVisibility(section: keyof ResumeData['sectionVisibility'], visible: boolean) {
    const currentVisibility = resumeData.get().sectionVisibility;
    resumeData.setKey('sectionVisibility', { ...currentVisibility, [section]: visible });
}

export function setCustomSectionVisibility(id: string, visible: boolean) {
    const customSections = resumeData.get().customSections.map(section => 
        section.id === id ? { ...section, visible } : section
    );
    resumeData.setKey('customSections', customSections);
}

export function setDateFormat(format: string) {
    resumeData.setKey('dateFormat', format);
}

export function moveSection(direction: 'up' | 'down', sectionName: string) {
    const order = resumeData.get().sectionsOrder;
    const index = order.indexOf(sectionName);
    if (index === -1) return;

    if (direction === 'up' && index > 0) {
        const newOrder = [...order];
        [newOrder[index - 1], newOrder[index]] = [newOrder[index], newOrder[index - 1]];
        resumeData.setKey('sectionsOrder', newOrder);
    } else if (direction === 'down' && index < order.length - 1) {
        const newOrder = [...order];
        [newOrder[index + 1], newOrder[index]] = [newOrder[index], newOrder[index + 1]];
        resumeData.setKey('sectionsOrder', newOrder);
    }
}

export function resetStyles() {
    const currentData = resumeData.get();
    resumeData.set({
        ...currentData,
        ...defaultStyles,
    });
}


// --- Existing Functions ---

export function updateSection<T extends keyof ResumeData>(section: T, data: ResumeData[T]) {
  resumeData.setKey(section, data);
}

export function updateContactInfo(field: keyof ResumeData['contactInfo'], value: string) {
    const currentContactInfo = resumeData.get().contactInfo;
    resumeData.setKey('contactInfo', { ...currentContactInfo, [field]: value });
}

export function addItem(section: 'workExperience' | 'education' | 'customSections', data?: Partial<ResumeData[typeof section][number]>) {
  const currentSection = resumeData.get()[section];
  let newItem: any;

  switch (section) {
    case 'workExperience':
      newItem = { id: crypto.randomUUID(), jobTitle: '', company: '', location: '', startDate: '', endDate: '', bulletPoints: [], ...data };
      break;
    case 'education':
      newItem = { id: crypto.randomUUID(), institution: '', degree: '', fieldOfStudy: '', graduationDate: '', details: '', ...data };
      break;
    case 'customSections':
      newItem = { id: crypto.randomUUID(), title: 'New Section', content: '', visible: true, ...data };
      break;
  }
  
  resumeData.setKey(section, [...currentSection, newItem] as any);
}

export function removeItem(section: 'workExperience' | 'education' | 'customSections', id: string) {
  const currentSection = resumeData.get()[section] as { id: string }[];
  resumeData.setKey(section, currentSection.filter(item => item.id !== id) as any);
}

export async function saveResume() {
  const state = resumeData.get();
  const url = state.id ? `/api/resumes/${state.id}` : '/api/resumes';
  const method = state.id ? 'PUT' : 'POST';

  try {
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(state),
    });

    if (!response.ok) {
      throw new Error('Failed to save resume');
    }

    const result = await response.json();
    if (!state.id) {
      resumeData.setKey('id', result.id);
    }
    console.log('Resume saved successfully:', result);
  } catch (error) {
    console.error('Error saving resume:', error);
  }
}

export async function loadResume(id: string) {
  try {
    const response = await fetch(`/api/resumes/${id}`);
    if (!response.ok) {
      throw new Error('Failed to load resume');
    }
    const data = await response.json();
    resumeData.set(data);
    console.log('Resume loaded successfully:', data);
  } catch (error) {
    console.error('Error loading resume:', error);
  }
}

export function setResumeData(data: Partial<ResumeData>) {
  const currentData = resumeData.get();
  resumeData.set({ ...currentData, ...data });
}

export function clearResumeData() {
    const currentId = resumeData.get().id;
    resumeData.set({
        id: currentId,
        ...defaultStyles,
        selectedTemplate: 'reverse-chronological',
        sectionVisibility: {
            summary: true,
            workExperience: true,
            education: true,
            skills: true,
        },
        sectionsOrder: ['summary', 'workExperience', 'education', 'skills', 'customSections'],
        metadata: {
            name: 'Untitled Resume',
        },
        contactInfo: {
            fullName: '',
            email: '',
            phone: '',
            location: '',
            linkedin: '',
            portfolio: '',
        },
        summary: '',
        workExperience: [],
        education: [],
        skills: [],
        customSections: [],
    });
}

export function loadSampleData() {
    const currentId = resumeData.get().id;
    resumeData.set({
        id: currentId,
        ...defaultStyles,
        selectedTemplate: 'reverse-chronological',
        sectionVisibility: {
            summary: true,
            workExperience: true,
            education: true,
            skills: true,
        },
        sectionsOrder: ['summary', 'workExperience', 'education', 'skills', 'customSections'],
        metadata: {
            name: 'Sample Resume',
        },
        contactInfo: {
            fullName: 'John Doe',
            email: '<EMAIL>',
            phone: '+****************',
            location: 'New York, NY',
            linkedin: 'https://linkedin.com/in/johndoe',
            portfolio: 'https://johndoe.dev',
        },
        summary: 'Experienced software developer with 5+ years of expertise in full-stack development, specializing in React, Node.js, and cloud technologies. Passionate about creating scalable solutions and mentoring junior developers.',
        workExperience: [
            {
                id: crypto.randomUUID(),
                jobTitle: 'Senior Software Engineer',
                company: 'Tech Solutions Inc.',
                location: 'New York, NY',
                startDate: '2022-01',
                endDate: 'Present',
                bulletPoints: [
                    { id: crypto.randomUUID(), text: 'Led development of microservices architecture serving 1M+ users.' },
                    { id: crypto.randomUUID(), text: 'Implemented CI/CD pipelines reducing deployment time by 60%.' },
                    { id: crypto.randomUUID(), text: 'Mentored 3 junior developers and conducted code reviews.' }
                ]
            },
            {
                id: crypto.randomUUID(),
                jobTitle: 'Software Developer',
                company: 'StartupXYZ',
                location: 'San Francisco, CA',
                startDate: '2020-06',
                endDate: '2021-12',
                bulletPoints: [
                    { id: crypto.randomUUID(), text: 'Developed responsive web applications using React and Node.js.' },
                    { id: crypto.randomUUID(), text: 'Collaborated with design team to implement pixel-perfect UI components.' },
                    { id: crypto.randomUUID(), text: 'Optimized database queries improving performance by 40%.' }
                ]
            }
        ],
        education: [
            {
                id: crypto.randomUUID(),
                institution: 'University of Technology',
                degree: 'Bachelor of Science',
                fieldOfStudy: 'Computer Science',
                graduationDate: '2020-05',
                details: 'Graduated Magna Cum Laude. Relevant coursework: Data Structures, Algorithms, Software Engineering, Database Systems.'
            }
        ],
        skills: ['JavaScript', 'React', 'Node.js', 'Python', 'AWS', 'Docker', 'PostgreSQL', 'Git', 'Agile/Scrum'],
        customSections: [],
    });
}