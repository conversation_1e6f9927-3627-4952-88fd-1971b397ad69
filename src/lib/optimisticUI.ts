/**
 * Optimistic UI Utilities
 * 
 * This module provides utilities for implementing optimistic UI updates
 * that improve perceived performance by updating the UI before API calls complete.
 */

import { APIClient } from './apiClient';

/**
 * Options for optimistic updates
 */
export interface OptimisticUpdateOptions<T> {
  /**
   * The data to use for the optimistic update
   */
  optimisticData: T;
  
  /**
   * Function to update the UI with optimistic data
   */
  updateUI: (data: T) => void;
  
  /**
   * Function to revert the UI if the request fails
   */
  revertUI?: (error: Error) => void;
  
  /**
   * Function to update the UI with the actual data from the server
   */
  updateWithActualData?: (data: T) => void;
}

/**
 * Performs an API request with optimistic UI updates
 * @param endpoint The API endpoint to call
 * @param options Request options
 * @param optimisticOptions Optimistic update options
 * @returns The response data
 */
export async function performOptimisticRequest<T>(
  endpoint: string,
  options: {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    body?: any;
    headers?: Record<string, string>;
    requireAuth?: boolean;
  },
  optimisticOptions: OptimisticUpdateOptions<T>
): Promise<T> {
  const { optimisticData, updateUI, revertUI, updateWithActualData } = optimisticOptions;
  
  try {
    // Make the optimistic request
    const result = await APIClient.optimisticRequest<T>(endpoint, {
      ...options,
      optimisticData,
      onOptimisticUpdate: updateUI,
      onRollback: revertUI
    });
    
    // Update UI with actual data if needed
    if (updateWithActualData) {
      updateWithActualData(result);
    }
    
    return result;
  } catch (error) {
    // Error handling is done in optimisticRequest
    throw error;
  }
}

/**
 * Creates a skeleton loader object with the same shape as the expected data
 * @param template The template object with the expected shape
 * @returns A skeleton loader object with the same shape
 */
export function createSkeletonData<T>(template: T): T {
  if (Array.isArray(template)) {
    // For arrays, create a skeleton array of the same length
    return template.map(item => createSkeletonData(item)) as unknown as T;
  } else if (typeof template === 'object' && template !== null) {
    // For objects, create a skeleton object with the same properties
    const result: Record<string, any> = {};
    for (const key in template) {
      if (Object.prototype.hasOwnProperty.call(template, key)) {
        const value = (template as Record<string, any>)[key];
        if (typeof value === 'string') {
          result[key] = '█████████'; // Placeholder for strings
        } else if (typeof value === 'number') {
          result[key] = 0; // Placeholder for numbers
        } else if (typeof value === 'boolean') {
          result[key] = false; // Placeholder for booleans
        } else if (Array.isArray(value)) {
          result[key] = value.length > 0 ? [createSkeletonData(value[0])] : []; // Placeholder for arrays
        } else if (typeof value === 'object' && value !== null) {
          result[key] = createSkeletonData(value); // Recursively create skeleton for nested objects
        } else {
          result[key] = null; // Placeholder for other types
        }
      }
    }
    return result as T;
  } else {
    // For primitive types, return a default value
    return template;
  }
}

/**
 * Checks if data is a skeleton
 * @param data The data to check
 * @returns True if the data is a skeleton
 */
export function isSkeletonData(data: any): boolean {
  if (typeof data === 'string') {
    return data === '█████████';
  } else if (typeof data === 'object' && data !== null) {
    if (Array.isArray(data)) {
      return data.length > 0 && isSkeletonData(data[0]);
    } else {
      return Object.values(data).some(value => isSkeletonData(value));
    }
  }
  return false;
}
