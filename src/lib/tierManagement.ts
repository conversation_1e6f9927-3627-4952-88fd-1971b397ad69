import {
  doc,
  getDoc,
  updateDoc,
  setDoc,
  Timestamp,
  getFirestore,
  Firestore,
  collection,
  getDocs,
  increment
} from 'firebase/firestore'
import { firebaseInstance } from './firebase'
import type {
  SubscriptionTier,
  Feature, // Assuming Feature is keyof UserSubscription['featureUsage']
  FeatureUsageLimit
} from './subscriptionConfig' // Assuming Feature is defined here
import { SUBSCRIPTION_TIERS } from './subscriptionConfig'
import { addMonths, isAfter } from '../utils/dateUtils' // Assuming dateUtils is correctly implemented
import { getPreloadedUserTierProfile, getPreloadedFeatureUsage } from './firebasePreloader'

// Safely get Firestore instance
export interface FeatureUsageEntry {
  usageCount: number
  maxAllowedUsage: FeatureUsageLimit
  lastResetTimestamp?: Timestamp
  lastUpdateTimestamp?: Timestamp // Added for tracking individual updates
}

// 1. Modify the UserSubscription Interface
export interface UserSubscription {
  userId: string
  currentTier: SubscriptionTier
  featureUsage: {
    resumeGeneration: FeatureUsageEntry
    coverLetterGeneration: FeatureUsageEntry
    jobAnalysis: FeatureUsageEntry
    jobTrackers: FeatureUsageEntry
    jobApplications: FeatureUsageEntry
    interviewPrep: FeatureUsageEntry
    linkedinOptimization: FeatureUsageEntry
    // If 'aiCopilot' is a new distinct feature, it should be added here
    // e.g., aiCopilot?: FeatureUsageEntry;
  }
  subscriptionStartDate: Timestamp
  subscriptionEndDate?: Timestamp | null | undefined, // Key field for active status for paid tiers
  paymentStatus?: 'active' | 'expired' | 'pending' | 'paid' | 'cancelled' // Optional, informational
  razorpaySubscriptionId?: string | null
  updatedBy?: string
}

// Feature usage limits for different tiers (already present, ensure it's consistent)
// const FEATURE_LIMITS = { ... } // This seems unused if SUBSCRIPTION_TIERS is the source of truth

// Normalized feature key type
export type FeatureKey =
  | keyof UserSubscription['featureUsage']
  | 'coverLetter' // alias for coverLetterGeneration
  | 'interviewPrep' // direct map
  | 'linkedinOptimization' // direct map
  | 'linkedinOptimiser' // alias for linkedinOptimization
  | 'aiCopilot'; // This needs to map to a key in UserSubscription['featureUsage'] or be a new feature

export class TierManagementService {
  private static db: Firestore

  private static initDb (): void {
    if (!this.db) {
      this.db = firebaseInstance?.db || getFirestore()
    }
  }

  private static getFirestoreInstance (): Firestore {
    this.initDb()
    return this.db
  }

  // Helper to create a full feature usage object for a tier
  private static createTierFeatureUsage(tier: SubscriptionTier): UserSubscription['featureUsage'] {
    const tierConfig = SUBSCRIPTION_TIERS[tier];
    const now = Timestamp.now();
    const usage: UserSubscription['featureUsage'] = {
        resumeGeneration: { usageCount: 0, maxAllowedUsage: tierConfig.featureUsageLimits.resumeGeneration, lastResetTimestamp: now },
        coverLetterGeneration: { usageCount: 0, maxAllowedUsage: tierConfig.featureUsageLimits.coverLetterGeneration, lastResetTimestamp: now },
        jobAnalysis: { usageCount: 0, maxAllowedUsage: tierConfig.featureUsageLimits.jobAnalysis, lastResetTimestamp: now },
        jobTrackers: { usageCount: 0, maxAllowedUsage: tierConfig.featureUsageLimits.jobTrackers, lastResetTimestamp: now },
        jobApplications: { usageCount: 0, maxAllowedUsage: tierConfig.featureUsageLimits.jobApplications, lastResetTimestamp: now },
        interviewPrep: { usageCount: 0, maxAllowedUsage: tierConfig.featureUsageLimits.interviewPrep, lastResetTimestamp: now },
        linkedinOptimization: { usageCount: 0, maxAllowedUsage: tierConfig.featureUsageLimits.linkedinOptimization, lastResetTimestamp: now },
        // If 'aiCopilot' is a new feature, initialize it here:
        // aiCopilot: { usageCount: 0, maxAllowedUsage: tierConfig.featureUsageLimits.aiCopilot, lastResetTimestamp: now },
    };
    return usage;
  }


  // 2. Update initUserTierProfile
  static async initUserTierProfile (userId: string): Promise<UserSubscription> {
    const initialTier: SubscriptionTier = 'free'
    
    const initialProfile: UserSubscription = {
      userId,
      currentTier: initialTier,
      featureUsage: this.createTierFeatureUsage(initialTier),
      subscriptionStartDate: Timestamp.now(),
      subscriptionEndDate: null, // Free tier has no end date for the subscription itself
      // paymentStatus removed from critical logic
      updatedBy: 'system_init'
    }

    const db = this.getFirestoreInstance()
    await setDoc(doc(db, 'userSubscriptions', userId), initialProfile)
    return initialProfile
  }

  // 6. Adjust downgradeUserTier (incorporating user's snippet and ensuring full reset)
  static async downgradeUserTier (userId: string): Promise<UserSubscription> {
    const db = this.getFirestoreInstance()
    const defaultTier: SubscriptionTier = 'free'
    
    const downgradedProfileData: Partial<UserSubscription> = {
      currentTier: defaultTier,
      featureUsage: this.createTierFeatureUsage(defaultTier), // Reset all feature usage
      subscriptionStartDate: Timestamp.now(), // New start date for the free tier period
      subscriptionEndDate: null, // Explicitly null for free tier
      razorpaySubscriptionId: null, // Clear Razorpay ID
      // paymentStatus: 'expired', // Can be set for info, but not essential for logic
      updatedBy: 'system_downgrade'
    }

    const userSubscriptionRef = doc(db, 'userSubscriptions', userId);
    await updateDoc(userSubscriptionRef, downgradedProfileData);
    
    const updatedDocSnap = await getDoc(userSubscriptionRef);
    if (!updatedDocSnap.exists()) {
        throw new Error("User subscription document not found after downgrade attempt.");
    }
    return updatedDocSnap.data() as UserSubscription;
  }

  // 4. Implement checkSubscriptionStatusAndDowngrade (New Function)
  static async checkSubscriptionStatusAndDowngrade(userId: string): Promise<UserSubscription> {
    const db = this.getFirestoreInstance();
    const userSubscriptionRef = doc(db, 'userSubscriptions', userId);
    const userSubscriptionSnap = await getDoc(userSubscriptionRef);

    if (!userSubscriptionSnap.exists()) {
      // If profile doesn't exist, initialize it. Handles new users.
      return this.initUserTierProfile(userId);
    }

    let userData = userSubscriptionSnap.data() as UserSubscription;

    // If not 'free' tier and subscriptionEndDate has passed, then downgrade.
    if (userData.currentTier !== 'free' && userData.subscriptionEndDate) {
      const now = new Date();
      if (now > userData.subscriptionEndDate.toDate()) {
        // console.log(`Subscription expired for user ${userId}. Downgrading.`);
        userData = await this.downgradeUserTier(userId);
      }
    } else if (userData.currentTier !== 'free' && !userData.subscriptionEndDate) {
      // This is an edge case: a paid tier without an end date.
      // Depending on business rules, this could be a lifetime subscription or an error.
      // Current logic: if no end date, it's considered active indefinitely.
      // If strict checking is required (paid tiers MUST have an end date),
      // then this case might also trigger a downgrade or an alert.
      // console.warn(`User ${userId} is on paid tier ${userData.currentTier} but has no subscriptionEndDate.`);
    }
    return userData;
  }

  // 5. Integrate checkSubscriptionStatusAndDowngrade
  // Modified getUserTierProfile to be the main entry point for a valid profile
  static async getUserTierProfile (userId: string): Promise<UserSubscription> {
    try {
      // Step 1: Check subscription status and downgrade if necessary. Also handles init for new users.
      let userProfile = await this.checkSubscriptionStatusAndDowngrade(userId);

      // Preloading logic: If preloaded data is used, ensure it's also validated or accept that
      // `userProfile` from the DB check is the most current.
      // For simplicity and robustness, `userProfile` after `checkSubscriptionStatusAndDowngrade`
      // is prioritized. If preloading is critical, it needs careful synchronization.
      const preloadedProfile = getPreloadedUserTierProfile();
      if (preloadedProfile && preloadedProfile.userId === userId) {
          // Example: If preloaded is 'pro' and expired, but DB check already downgraded, `userProfile` is correct.
          // If preloaded is 'pro' and active, and `userProfile` matches, it's fine.
          // Generally, `userProfile` is now the more reliable source after the check.
          // Potentially update preloader cache if `userProfile` is different and more up-to-date.
      }
      
      // Step 2: Check and reset feature usage based on the (potentially updated) profile.
      userProfile = await this.checkAndResetFeatureUsageIfNeeded(userId, userProfile);

      return userProfile;
    } catch (error) {
      console.error('Error getting user tier profile:', error);
      // Fallback or re-throw. `checkSubscriptionStatusAndDowngrade` handles init if doc doesn't exist.
      // So, an error here is likely more serious.
      throw error;
    }
  }
  
  // Modified to accept and prioritize a pre-fetched, status-checked profile
  static async checkAndResetFeatureUsageIfNeeded (
    userId: string,
    currentUserProfile: UserSubscription // Expect a profile that has been status-checked
  ): Promise<UserSubscription> {
    try {
      let userData = currentUserProfile;

      // If somehow called without a profile (should not happen with new flow)
      if (!userData || userData.userId !== userId) {
          console.warn("checkAndResetFeatureUsageIfNeeded called unexpectedly without a valid profile. Fetching and checking status.");
          userData = await this.checkSubscriptionStatusAndDowngrade(userId); // Ensure status check
      }

      const currentTier = userData.currentTier;
      const tierConfig = SUBSCRIPTION_TIERS[currentTier];

      if (!tierConfig) {
          console.error(`Invalid tier ${currentTier} for user ${userId}. Cannot reset usage.`);
          // Potentially downgrade to free or return current data to prevent further errors.
          return userData; 
      }

      const billingCycle = tierConfig.billingCycle;
      const subscriptionStartDate = userData.subscriptionStartDate.toDate();
      
      let monthsToAdd = 1; // Default for monthly
      if (billingCycle === 'quarterly') {
        monthsToAdd = 3;
      } 
      // For 'free' tier, billingCycle might be 'N/A' or similar. Handle if necessary.
      // Typically, free tier usage might reset monthly regardless, or not at all in this manner.
      // The current SUBSCRIPTION_TIERS config should define this. Let's assume 'free' has a cycle too.

      const nextResetDate = addMonths(subscriptionStartDate, monthsToAdd);
      const now = new Date();

      if (isAfter(now, nextResetDate) && currentTier !== 'pro') { // Pro tier has Infinity limits, reset might not be standard
        const newSubscriptionStartDate = Timestamp.now();
        const resetFeatureUsage = this.createTierFeatureUsage(currentTier);
        
        // Update featureUsage and subscriptionStartDate (for the new cycle)
        (Object.keys(resetFeatureUsage) as Array<keyof UserSubscription['featureUsage']>).forEach(key => {
            resetFeatureUsage[key].lastResetTimestamp = newSubscriptionStartDate;
        });

        await updateDoc(doc(this.getFirestoreInstance(), 'userSubscriptions', userId), {
          featureUsage: resetFeatureUsage,
          subscriptionStartDate: newSubscriptionStartDate,
          updatedBy: 'system_usage_reset'
        });
        // console.log(`Reset feature usage for user ${userId} on tier ${currentTier}`);
        return { ...userData, featureUsage: resetFeatureUsage, subscriptionStartDate: newSubscriptionStartDate };
      }
      return userData;
    } catch (error) {
      console.error(`Error checking/resetting feature usage for ${userId}:`, error);
      // Return the profile we started with to minimize disruption, or rethrow if critical
      return currentUserProfile; 
    }
  }

  // 3. Update upgradeUserTier
  static async upgradeUserTier (
    userId: string,
    newTier: SubscriptionTier,
    razorpaySubscriptionId?: string
  ): Promise<UserSubscription> {
    try {
      const db = this.getFirestoreInstance();
      const userSubscriptionRef = doc(db, 'userSubscriptions', userId);
      const tierConfig = SUBSCRIPTION_TIERS[newTier];

      if (newTier === 'free') {
        // console.warn("upgradeUserTier called with 'free'. Downgrading instead.");
        return this.downgradeUserTier(userId);
      }

      const now = Timestamp.now();
      let subscriptionEndDate: Timestamp | undefined = undefined;

      let monthsToAdd = 1; // Default for monthly
      if (tierConfig.billingCycle === 'quarterly') monthsToAdd = 3;
      
      subscriptionEndDate = Timestamp.fromDate(addMonths(now.toDate(), monthsToAdd));
      
      const upgradedData: Partial<UserSubscription> = {
        currentTier: newTier,
        featureUsage: this.createTierFeatureUsage(newTier), // Reset usage to new tier limits
        subscriptionStartDate: now,
        subscriptionEndDate: subscriptionEndDate,
        razorpaySubscriptionId: razorpaySubscriptionId || null,
        // paymentStatus: 'paid', // Informational, if desired
        updatedBy: 'system_upgrade'
      };

      await updateDoc(userSubscriptionRef, upgradedData);

      const updatedDocSnap = await getDoc(userSubscriptionRef);
      if (!updatedDocSnap.exists()) {
          throw new Error("User subscription document not found after upgrade.");
      }
      return updatedDocSnap.data() as UserSubscription;

    } catch (error) {
      console.error(`❌ Tier Upgrade Failed for ${userId} to ${newTier}:`, error);
      throw error;
    }
  }
  
  // 7. Remove paymentStatus checks (example in trackFeatureUsage)
  static async trackFeatureUsage (
    userId: string,
    feature: keyof UserSubscription['featureUsage'] // Ensure 'Feature' type matches this
  ): Promise<boolean> {
    try {
      const userProfile = await this.getUserTierProfile(userId); // Gets definitive, status-checked profile

      if (!userProfile.featureUsage || !userProfile.featureUsage[feature]) {
        console.error(`Feature ${feature} misconfiguration for user ${userId} in tier ${userProfile.currentTier}.`);
        throw new Error(`Feature ${feature} is not configured for your account.`);
      }

      const usageEntry = userProfile.featureUsage[feature];
      const currentUsage = usageEntry.usageCount;
      const featureLimit = usageEntry.maxAllowedUsage; // Limits are now part of the profile's featureUsage

      if (currentUsage >= featureLimit && featureLimit !== Infinity) {
        throw new Error(
          `You have reached the maximum usage for ${feature} in your ${userProfile.currentTier} tier. Upgrade to continue.`
        );
      }

      const db = this.getFirestoreInstance();
      const userSubscriptionRef = doc(db, 'userSubscriptions', userId);
      await updateDoc(userSubscriptionRef, {
        [`featureUsage.${feature}.usageCount`]: increment(1),
        [`featureUsage.${feature}.lastUpdateTimestamp`]: Timestamp.now(),
        updatedBy: 'system_track_usage'
      });
      return true;
    } catch (error) {
      console.error(`Feature usage tracking error for ${userId}, feature ${feature}:`, error);
      throw error;
    }
  }

  static async getUserTierStatus (userId: string): Promise<{
    currentTier: SubscriptionTier
    featureUsage: UserSubscription['featureUsage'] // Simplified to return the whole block
    subscriptionEndDate?: Timestamp | null
  }> {
    const tierProfile = await this.getUserTierProfile(userId); // This handles all checks and updates

    return {
      currentTier: tierProfile.currentTier,
      featureUsage: tierProfile.featureUsage, // Return the full, current featureUsage object
      subscriptionEndDate: tierProfile.subscriptionEndDate
    };
  }

  // trackJobTracker, trackJobApplication, etc. should follow the pattern of trackFeatureUsage
  // by first calling getUserTierProfile, then using the returned profile.

  static async trackJobTracker (userId: string): Promise<boolean> {
    // This is a specific type of feature usage, can use the generic trackFeatureUsage
    return this.trackFeatureUsage(userId, 'jobTrackers');
  }

  static async trackJobApplication (userId: string): Promise<boolean> {
    return this.trackFeatureUsage(userId, 'jobApplications');
  }
  
  static async resetDeletedAccountUsage (userId: string): Promise<void> {
    try {
      const db = this.getFirestoreInstance()
      const userSubscriptionRef = doc(db, 'userSubscriptions', userId)
      const initialTier: SubscriptionTier = 'free'
      
      await setDoc(
        userSubscriptionRef,
        {
          userId,
          currentTier: initialTier,
          featureUsage: this.createTierFeatureUsage(initialTier),
          subscriptionStartDate: Timestamp.now(),
          subscriptionEndDate: null, // No end date for free/deleted
          // paymentStatus: 'expired', // Informational
          deletedAccountFlag: true, // Specific flag for deleted accounts
          updatedBy: 'system_account_deleted'
        },
        { merge: false } // Overwrite completely
      );

      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem(`userUsageTracking_${userId}`); // Be more specific if used
      }
    } catch (error) {
      console.error('Error resetting deleted account usage:', error)
      throw error
    }
  }

  // ... (checkAndResetFeatureUsage already refactored) ...

  static async resetAllUsersFeatureUsage (): Promise<void> {
    // This function iterates and calls checkAndResetFeatureUsage.
    // checkAndResetFeatureUsageIfNeeded now requires a profile.
    // So, this needs to fetch each profile first.
    try {
      const db = this.getFirestoreInstance();
      const userSubscriptionsRef = collection(db, 'userSubscriptions');
      const querySnapshot = await getDocs(userSubscriptionsRef);

      const resetPromises = querySnapshot.docs.map(async (userDoc) => {
        const userId = userDoc.id;
        // Get the full profile which includes status check and potential downgrade
        const userProfile = await this.getUserTierProfile(userId); 
        // checkAndResetFeatureUsageIfNeeded is already called within getUserTierProfile
        // So, just calling getUserTierProfile for each user effectively does the reset if needed.
        // If an explicit, separate reset is desired outside of getUserTierProfile's flow:
        // await this.checkAndResetFeatureUsageIfNeeded(userId, userProfile);
      });

      await Promise.all(resetPromises);
      // console.log('Completed subscription status check and feature usage reset for all users');
    } catch (error) {
      console.error('Error resetting feature usage for all users:', error);
    }
  }
  
  static async manuallyResetUserFeatureUsage (userId: string): Promise<void> {
    try {
      let userProfile = await this.getUserTierProfile(userId); // Get current, valid profile
      const currentTier = userProfile.currentTier;
      const newSubscriptionStartDate = Timestamp.now();
      const resetFeatureUsage = this.createTierFeatureUsage(currentTier);
      
      (Object.keys(resetFeatureUsage) as Array<keyof UserSubscription['featureUsage']>).forEach(key => {
            resetFeatureUsage[key].lastResetTimestamp = newSubscriptionStartDate;
      });

      await updateDoc(doc(this.getFirestoreInstance(), 'userSubscriptions', userId), {
        featureUsage: resetFeatureUsage,
        subscriptionStartDate: newSubscriptionStartDate, // Reset cycle start
        updatedBy: 'admin_manual_reset'
      });
      // console.log(`Manually reset feature usage for user ${userId}`);
    } catch (error) {
      console.error('Error manually resetting feature usage:', error);
      throw error;
    }
  }

  // initializeUserSubscriptionIfNeeded can be simplified or deprecated if initUserTierProfile
  // (called by checkSubscriptionStatusAndDowngrade) covers all initialization.
  // For now, keeping it but ensuring it aligns.
  static async initializeUserSubscriptionIfNeeded (userId: string): Promise<UserSubscription> {
    const db = this.getFirestoreInstance();
    const userSubscriptionRef = doc(db, 'userSubscriptions', userId);
    const userSubscriptionDoc = await getDoc(userSubscriptionRef);

    if (userSubscriptionDoc.exists()) {
      // If it exists, we should still run it through the status check
      return this.checkSubscriptionStatusAndDowngrade(userId);
    }
    // If it doesn't exist, initUserTierProfile will be called by checkSubscriptionStatusAndDowngrade
    return this.initUserTierProfile(userId);
  }

  static async getCurrentFeatureUsage (
    userId: string,
    feature: keyof UserSubscription['featureUsage']
  ): Promise<number> {
    try {
      const tierProfile = await this.getUserTierProfile(userId);
      if (!tierProfile.featureUsage || !tierProfile.featureUsage[feature]) {
        // console.warn(`Feature ${feature} not found for user ${userId} in getCurrentFeatureUsage.`);
        return 0;
      }
      return tierProfile.featureUsage[feature].usageCount || 0;
    } catch (error) {
      console.error(`Error getting current ${feature} usage for ${userId}:`, error);
      return 0; // Default to 0 on error
    }
  }

  static async getAllFeatureUsage (
    userId: string
  ): Promise<UserSubscription['featureUsage']> {
     // Preloading: Similar to getUserTierProfile, prioritize fresh data.
    const preloadedFeatureUsage = getPreloadedFeatureUsage();
    // This preloader might need to be user-specific if it's just one global cache.
    // if (preloadedFeatureUsage && preloadedFeatureUsage.userId === userId) { /* ... */ }


    try {
      const tierProfile = await this.getUserTierProfile(userId);
      return tierProfile.featureUsage;
    } catch (error) {
      console.error(`Error getting all feature usage for ${userId}:`, error);
      // Return empty usage map on error, matching original structure if possible
      const defaultTier: SubscriptionTier = 'free';
      return this.createTierFeatureUsage(defaultTier); // Return a default structure
    }
  }

  // Specific track methods can use the generic trackFeatureUsage
  static async trackResumeGeneration (userId: string): Promise<boolean> {
    return this.trackFeatureUsage(userId, 'resumeGeneration');
  }

  static async trackCoverLetterGeneration (userId: string): Promise<boolean> {
    return this.trackFeatureUsage(userId, 'coverLetterGeneration');
  }

  static async trackJobAnalysisRequests (userId: string): Promise<boolean> {
    return this.trackFeatureUsage(userId, 'jobAnalysis');
  }

  // trackJobTrackers and trackJobApplications already updated to use trackFeatureUsage

  static async trackInterviewPrep (userId: string): Promise<boolean> {
    return this.trackFeatureUsage(userId, 'interviewPrep');
  }

  static async trackLinkedinOptimization (userId: string): Promise<boolean> {
    return this.trackFeatureUsage(userId, 'linkedinOptimization');
  }

  static async checkFeatureAccess (
    userId: string,
    feature: FeatureKey // Uses the FeatureKey type from original code
  ): Promise<boolean> {
    try {
      const userProfile = await this.getUserTierProfile(userId);

      let featureUsageKey = feature as keyof UserSubscription['featureUsage'];

      // Normalize aliases
      if (feature === 'coverLetter') featureUsageKey = 'coverLetterGeneration';
      else if (feature === 'linkedinOptimiser') featureUsageKey = 'linkedinOptimization';
      // Handle 'aiCopilot' - if it's a new feature, it needs to be in UserSubscription.featureUsage
      // and SUBSCRIPTION_TIERS. If it's an alias, map it.
      // else if (feature === 'aiCopilot') featureUsageKey = 'someOtherFeatureUsageKey'; 
      // For now, assuming 'aiCopilot' directly maps if it's not an alias above.
      // This will fail if 'aiCopilot' is not a key in UserSubscription['featureUsage'].

      if (!userProfile.featureUsage || !userProfile.featureUsage[featureUsageKey]) {
        // console.warn(`Feature ${featureUsageKey} (from ${feature}) not found for user ${userId}. Denying access.`);
        return false; // Feature not configured in profile
      }
      
      const usageEntry = userProfile.featureUsage[featureUsageKey];
      const currentUsage = usageEntry.usageCount;
      const limit = usageEntry.maxAllowedUsage;

      if (limit === Infinity) return true;
      return currentUsage < limit;

    } catch (error) {
      console.error(`Error checking feature access for ${userId}, feature ${feature}:`, error);
      return false; // Default to no access on error
    }
  }

  static async upgradeToProSubscription (userId: string): Promise<void> {
    // This is a specific case of upgradeUserTier
    try {
        await this.upgradeUserTier(userId, 'pro'); // Assuming 'pro' is the target tier
    } catch (error) {
        console.error(`Error upgrading ${userId} to pro subscription:`, error);
        // Decide if to throw or handle
    }
  }

  static async updateSubscriptionTierAfterPayment (
    tierId: SubscriptionTier, // e.g., 'pro'
    userId: string,
    razorpaySubscriptionId?: string
  ): Promise<void> {
    // This is essentially an alias for upgradeUserTier if tierId is not 'free'
    if (tierId === 'free') {
      // console.warn(`updateSubscriptionTierAfterPayment called for 'free' tier for user ${userId}. Downgrading.`);
      await this.downgradeUserTier(userId);
    } else {
      await this.upgradeUserTier(userId, tierId, razorpaySubscriptionId);
    }
  }
  
  // This method seems redundant if specific track methods are used.
  // static async incrementFeatureUsage ( ... ) { /* ... */ }


  static async decrementJobTrackerUsage (userId: string): Promise<boolean> {
    try {
      const userProfile = await this.getUserTierProfile(userId); // Get current profile
      const jobTrackersUsage = userProfile.featureUsage.jobTrackers;

      if (jobTrackersUsage.usageCount > 0) {
        const db = this.getFirestoreInstance();
        await updateDoc(doc(db, 'userSubscriptions', userId), {
          'featureUsage.jobTrackers.usageCount': increment(-1), // Decrement
          'featureUsage.jobTrackers.lastUpdateTimestamp': Timestamp.now(),
          updatedBy: 'system_decrement_jobtracker'
        });
      }
      return true;
    } catch (error) {
      console.error(`Error decrementing job tracker usage for ${userId}:`, error);
      return false; // Don't block deletion
    }
  }
}