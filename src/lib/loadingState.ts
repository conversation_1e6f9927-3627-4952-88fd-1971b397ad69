/**
 * Sets the loading state of a button
 * @param id The ID of the button loader to update
 * @param isLoading Whether the button is in a loading state
 * @param loadingText Optional text to display while loading
 * @returns true if the button was found and updated, false otherwise
 */
export function setButtonLoading(
  id: string,
  isLoading: boolean,
  loadingText?: string
): boolean {
  if (window.buttonLoaderFunctions && window.buttonLoaderFunctions[id]) {
    window.buttonLoaderFunctions[id].setLoading(isLoading, loadingText);
    return true;
  }

  // Fallback for regular buttons without the ButtonLoader component
  const button = document.getElementById(id) as HTMLButtonElement;
  if (button) {
    // Store original text if not already stored
    if (isLoading && !button.dataset.originalText) {
      button.dataset.originalText = button.innerHTML;
    }

    // Update button state
    button.disabled = isLoading;

    if (isLoading) {
      button.classList.add('opacity-75', 'cursor-not-allowed');

      // Add spinner and update text if loading text is provided
      if (loadingText) {
        button.innerHTML = `
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          ${loadingText}
        `;
      }
    } else {
      button.classList.remove('opacity-75', 'cursor-not-allowed');

      // Restore original text
      if (button.dataset.originalText) {
        button.innerHTML = button.dataset.originalText;
        delete button.dataset.originalText;
      }
    }

    return true;
  }

  return false;
}

/**
 * Updates the progress of a progress bar
 * @param id The ID of the progress bar to update
 * @param progress The new progress value (0-100)
 * @returns true if the progress bar was found and updated, false otherwise
 */
export function updateProgress(id: string, progress: number): boolean {
  if (window.progressBarFunctions && window.progressBarFunctions[id]) {
    window.progressBarFunctions[id].updateProgress(progress);
    return true;
  }

  // Fallback for progress bars without the ProgressBar component
  const progressBar = document.getElementById(`${id}-bar`);
  const progressContainer = document.getElementById(id);
  const percentageLabel = document.getElementById(`${id}-percentage`);

  if (progressBar) {
    // Ensure progress is between 0 and 100
    const normalizedProgress = Math.min(Math.max(progress, 0), 100);

    // Update the width
    progressBar.style.width = `${normalizedProgress}%`;

    // Update the aria value
    if (progressContainer) {
      progressContainer.setAttribute('aria-valuenow', normalizedProgress.toString());
    }

    // Update percentage label if it exists
    if (percentageLabel) {
      percentageLabel.textContent = `${Math.round(normalizedProgress)}%`;
    }

    return true;
  }

  return false;
}

/**
 * Sets a progress bar to indeterminate state
 * @param id The ID of the progress bar to update
 * @param isIndeterminate Whether the progress bar should be in indeterminate state
 * @returns true if the progress bar was found and updated, false otherwise
 */
export function setProgressIndeterminate(id: string, isIndeterminate: boolean): boolean {
  if (window.progressBarFunctions && window.progressBarFunctions[id]) {
    window.progressBarFunctions[id].setIndeterminate(isIndeterminate);
    return true;
  }

  // Fallback for progress bars without the ProgressBar component
  const progressBar = document.getElementById(`${id}-bar`);
  const progressContainer = document.getElementById(id);

  if (progressBar) {
    if (isIndeterminate) {
      progressBar.style.width = '40%';
      progressBar.classList.add('animate-progress-indeterminate');

      if (progressContainer) {
        progressContainer.removeAttribute('aria-valuenow');
      }
    } else {
      progressBar.classList.remove('animate-progress-indeterminate');
      // Reset to 0% or another value
      progressBar.style.width = '0%';

      if (progressContainer) {
        progressContainer.setAttribute('aria-valuenow', '0');
      }
    }

    return true;
  }

  return false;
}

/**
 * Shows a skeleton loader by ID
 * @param containerId The ID of the container to show the skeleton loader in
 * @param hideContent Whether to hide the existing content
 * @returns true if the container was found, false otherwise
 */
export function showSkeletonLoader(containerId: string, hideContent: boolean = true): boolean {
  const container = document.getElementById(containerId);

  if (container) {
    // Store original content if not already stored and hideContent is true
    if (hideContent && !container.dataset.originalContent) {
      container.dataset.originalContent = container.innerHTML;
    }

    // Add skeleton loader class
    container.classList.add('skeleton-loading');

    return true;
  }

  return false;
}

/**
 * Hides a skeleton loader by ID and restores original content
 * @param containerId The ID of the container to hide the skeleton loader from
 * @returns true if the container was found, false otherwise
 */
export function hideSkeletonLoader(containerId: string): boolean {
  const container = document.getElementById(containerId);

  if (container) {
    // Remove skeleton loader class
    container.classList.remove('skeleton-loading');

    // Restore original content if it exists
    if (container.dataset.originalContent) {
      container.innerHTML = container.dataset.originalContent;
      delete container.dataset.originalContent;
    }

    return true;
  }

  return false;
}

// Extend the Window interface to include our custom properties
declare global {
  interface Window {
    buttonLoaderFunctions?: {
      [key: string]: {
        setLoading: (isLoading: boolean, loadingText?: string) => void;
      };
    };
    progressBarFunctions?: {
      [key: string]: {
        updateProgress: (progress: number) => void;
        setIndeterminate: (isIndeterminate: boolean) => void;
      };
    };
  }
}
