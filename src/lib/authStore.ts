import { atom } from "nanostores";
import type { User } from "firebase/auth";

// Create an atom for the auth state
export const authStore = atom<{
  user: User | null;
  loading: boolean;
  subscriptionExpired: boolean;
}>({
  user: null,
  loading: true,
  subscriptionExpired: false,
});

// Function to get current authenticated user
export function getAuthenticatedUser(): User | null {
  return authStore.get().user;
}

// Function to check if user is authenticated
export async function isAuthenticated(): Promise<boolean> {
  try {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      // Server-side: we can't reliably check auth state without the token
      // This should be handled by middleware instead
      return false;
    }
    
    const currentState = authStore.get();
    
    // If still loading, wait a bit for auth state to resolve
    if (currentState.loading) {
      return new Promise((resolve) => {
        const unsubscribe = authStore.subscribe((state) => {
          if (!state.loading) {
            unsubscribe();
            resolve(!!state.user);
          }
        });
        
        // Timeout after 3 seconds
        setTimeout(() => {
          unsubscribe();
          resolve(false);
        }, 3000);
      });
    }
    
    return !!currentState.user;
  } catch (error) {
    console.error('Error checking authentication status:', error);
    return false;
  }
}
