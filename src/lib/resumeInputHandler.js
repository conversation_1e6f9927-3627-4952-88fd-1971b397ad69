/**
 * Utility module for handling resume input functionality (file upload, import, manual entry)
 * This centralizes the resume input logic to make it more maintainable and consistent
 */

import { authService } from "./auth";
import { PersistentDocumentService } from "./persistentDocumentService";

/**
 * Sets up all resume input functionality
 * @param {Object} options - Configuration options
 * @returns {Object} - Methods for interacting with the resume input
 */
export function setupResumeInput(options = {}) {
  // Default options that can be overridden
  const {
    // Element IDs
    resumeFileInputId = "resumeFileInput",
    resumeFileUploadButtonId = "resumeFileUploadButton",
    importResumeButtonId = "importResumeButton",
    enterManuallyButtonId = "enterManuallyButton",
    resumeContentTextareaId = "resumeContent",
    resumeOptionsSelectorId = "resumeOptionsSelector",
    resumeFileAddedUIId = "resumeFileAddedUI",
    manualEntryContainerId = "manualEntryContainer",
    resumeFileNameId = "resumeFileName",
    cancelManualEntryId = "cancelManualEntry",
    changeResumeSourceId = "changeResumeSource",

    // API endpoints
    uploadEndpoint = "/.netlify/functions/upload-resume",

    // Callbacks
    onResumeContentChanged = null,
    onError = null,
  } = options;

  // Cache DOM elements
  const resumeFileInput = document.getElementById(resumeFileInputId);
  const resumeFileUploadButton = document.getElementById(resumeFileUploadButtonId);
  const importResumeButton = document.getElementById(importResumeButtonId);
  const enterManuallyButton = document.getElementById(enterManuallyButtonId);
  const resumeContentTextarea = document.getElementById(resumeContentTextareaId);
  const resumeOptionsSelector = document.getElementById(resumeOptionsSelectorId);
  const resumeFileAddedUI = document.getElementById(resumeFileAddedUIId);
  const manualEntryContainer = document.getElementById(manualEntryContainerId);
  const resumeFileName = document.getElementById(resumeFileNameId);
  const cancelManualEntry = document.getElementById(cancelManualEntryId);
  const changeResumeSource = document.getElementById(changeResumeSourceId);

  // Supported file types for resume upload
  const supportedTypes = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "text/plain",
    "text/rtf",
    "application/rtf",
  ];

  /**
   * Shows an error message
   * @param {string} title - Error title
   * @param {string} message - Error message
   */
  function handleError(title, message) {
    console.error(`${title}: ${message}`);

    if (typeof onError === 'function') {
      // Use provided error handler if available
      onError(title, message);
    } else {
      // Default to importing the error handling utility
      import("./errorHandling").then(({ showErrorModal }) => {
        showErrorModal(
          "resumeErrorModal",
          title,
          message
        );
      }).catch(err => {
        console.error("Failed to import error handling utility:", err);
        alert(`${title}: ${message}`);
      });
    }
  }

  /**
   * Updates the UI after resume content is set
   * @param {string} source - Source name to display
   */
  function updateUIAfterContentSet(source) {
    if (resumeOptionsSelector) resumeOptionsSelector.classList.add("hidden");
    if (manualEntryContainer) manualEntryContainer.classList.remove("hidden");
    if (resumeFileAddedUI) resumeFileAddedUI.classList.add("hidden");

    if (typeof onResumeContentChanged === 'function') {
      onResumeContentChanged(resumeContentTextarea?.value || "");
    }
  }

  /**
   * Sets up file upload functionality
   */
  function setupFileUpload() {
    if (!resumeFileUploadButton || !resumeFileInput) return;

    // Trigger file input when upload button is clicked
    resumeFileUploadButton.addEventListener("click", (e) => {
      e.preventDefault();
      resumeFileInput.click();
    });

    // Handle file selection
    resumeFileInput.addEventListener("change", async (event) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validate file type
      if (!supportedTypes.includes(file.type)) {
        handleError(
          "Unsupported File Type",
          "Please upload a PDF, DOC, DOCX, TXT, or RTF file."
        );
        resumeFileInput.value = "";
        return;
      }

      try {
        // Check authentication
        const user = await authService.getCurrentUser();
        if (!user) {
          handleError(
            "Authentication Required",
            "Please log in to upload a resume."
          );
          resumeFileInput.value = "";
          return;
        }

        const idToken = await user.getIdToken();

        // Convert file to base64
        const fileReader = new FileReader();
        fileReader.readAsDataURL(file);

        fileReader.onload = async () => {
          const base64File = fileReader.result;
          const base64Data = base64File.split(",")[1]; // Remove data URL prefix

          try {
            // Send to server-side API
            const response = await fetch(uploadEndpoint, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${idToken}`,
              },
              body: JSON.stringify({
                fileBase64: base64Data,
                fileName: file.name,
                fileType: file.type,
              }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.error || "Unknown server error");
            }

            if (result.success) {
              // Populate resume content textarea
              if (resumeContentTextarea) {
                resumeContentTextarea.value = result.data.text;
                resumeContentTextarea.dispatchEvent(new Event("input"));
              }

              // Update UI
              if (resumeFileName) resumeFileName.textContent = file.name;
              if (resumeFileAddedUI) resumeFileAddedUI.classList.remove("hidden");
              if (resumeOptionsSelector) resumeOptionsSelector.classList.add("hidden");
              if (manualEntryContainer) manualEntryContainer.classList.add("hidden");

              if (typeof onResumeContentChanged === 'function') {
                onResumeContentChanged(resumeContentTextarea?.value || "");
              }
            } else {
              throw new Error(result.error || "Failed to process resume file.");
            }
          } catch (error) {
            handleError(
              "Resume Upload Error",
              error instanceof Error ? error.message : "An unknown error occurred during upload."
            );
            resumeFileInput.value = "";
          }
        };

        fileReader.onerror = () => {
          handleError(
            "File Reading Error",
            "Failed to read the selected file."
          );
          resumeFileInput.value = "";
        };
      } catch (error) {
        handleError(
          "Resume Upload Error",
          error instanceof Error ? error.message : "An unknown error occurred."
        );
        resumeFileInput.value = "";
      }
    });
  }

  /**
   * Sets up resume import functionality
   */
  function setupResumeImport() {
    if (!importResumeButton) return;

    importResumeButton.addEventListener("click", async () => {
      try {
        const user = await authService.getCurrentUser();
        if (!user) {
          handleError(
            "Authentication Required",
            "Please log in to import resumes."
          );
          return;
        }

        // Create a modal dynamically
        const modal = document.createElement("div");
        modal.innerHTML = `
          <div class="p-4 fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
              <div class="bg-white dark:bg-gray-900 rounded-2xl p-6 max-w-md w-full max-h-[80vh] overflow-y-auto">
                  <div class="flex justify-between items-center mb-4">
                      <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Resume Manager</h2>
                      <button id="closeImportModal" class="text-gray-500 hover:text-gray-900 dark:hover:text-white"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg></button>
                  </div>
                  <div class="mb-4"><button id="addNewResumeButton" class="w-full inline-flex items-center justify-center px-4 py-3 bg-black text-white rounded-xl hover:bg-primary-600 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /></svg>Create New Resume</button></div>
                  <div class="border-t border-gray-200 dark:border-gray-700 my-4"></div>
                  <div id="resumeList" class="space-y-3"></div>
              </div>
          </div>`;
        document.body.appendChild(modal);

        const closeModalButton = modal.querySelector("#closeImportModal");
        const addNewResumeButton = modal.querySelector("#addNewResumeButton");
        const resumeListContainer = modal.querySelector("#resumeList");

        closeModalButton?.addEventListener("click", () => {
          document.body.removeChild(modal);
        });

        // Add new resume functionality
        addNewResumeButton?.addEventListener("click", () => {
          // Navigate to Dashboard's Resume Manager section
          window.location.href = "/dashboard#addResumeBtn";
        });

        const resumes = await PersistentDocumentService.loadAllResumes();

        if (!resumeListContainer) return; // Should not happen

        if (resumes.length === 0) {
          resumeListContainer.innerHTML = `<div class="text-center text-gray-500 dark:text-gray-400 py-6"><p>No resumes found. Create a new resume to get started.</p></div>`;
        } else {
          resumeListContainer.innerHTML = resumes
            .map(
              (resume, index) => `
            <div class="bg-gray-100 dark:bg-gray-800 rounded-xl p-4 hover:bg-gray-200 dark:hover:bg-gray-700 cursor-pointer transition-colors resume-item" data-resume-index="${index}">
                <div class="flex justify-between items-center">
                    <div><h3 class="text-lg font-semibold text-gray-900 dark:text-white">${resume.name || `Resume ${index + 1}`}</h3><p class="text-sm text-gray-500 dark:text-gray-400">Last updated: ${new Date(resume.lastUpdated).toLocaleDateString()}</p></div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                </div>
            </div>`
            )
            .join("");

          // Add click event to resume items
          modal.querySelectorAll(".resume-item").forEach((item) => {
            item.addEventListener("click", () => {
              const index = Number(item.getAttribute("data-resume-index"));
              const selectedResume = resumes[index];

              // Populate resume content textarea
              if (resumeContentTextarea) {
                resumeContentTextarea.value = selectedResume.content || "";

                // Show the file added UI and hide other elements
                updateUIAfterContentSet(selectedResume.name || "Imported Resume");

                if (resumeFileAddedUI && resumeFileName) {
                  resumeFileAddedUI.classList.remove("hidden");
                  resumeOptionsSelector?.classList.add("hidden");
                  manualEntryContainer?.classList.add("hidden");
                  resumeFileName.textContent = selectedResume.name || "Imported Resume";
                }
              }

              document.body.removeChild(modal);
            });
          });
        }
      } catch (error) {
        handleError(
          "Error Loading Resumes",
          "Failed to load resumes. Please try again later."
        );
      }
    });
  }

  /**
   * Sets up manual entry functionality
   */
  function setupManualEntry() {
    if (!enterManuallyButton) return;

    // Show manual entry textarea
    enterManuallyButton.addEventListener("click", () => {
      if (resumeOptionsSelector) resumeOptionsSelector.classList.add("hidden");
      if (manualEntryContainer) manualEntryContainer.classList.remove("hidden");
      if (resumeFileAddedUI) resumeFileAddedUI.classList.add("hidden");
      if (resumeContentTextarea) resumeContentTextarea.focus();
    });

    // Cancel manual entry
    if (cancelManualEntry) {
      cancelManualEntry.addEventListener("click", () => {
        if (resumeOptionsSelector) resumeOptionsSelector.classList.remove("hidden");
        if (manualEntryContainer) manualEntryContainer.classList.add("hidden");
      });
    }

    // Change resume source
    if (changeResumeSource) {
      changeResumeSource.addEventListener("click", () => {
        if (resumeOptionsSelector) resumeOptionsSelector.classList.remove("hidden");
        if (resumeFileAddedUI) resumeFileAddedUI.classList.add("hidden");
        if (manualEntryContainer) manualEntryContainer.classList.add("hidden");
        if (resumeFileInput) resumeFileInput.value = "";
        if (resumeContentTextarea) resumeContentTextarea.value = "";

        if (typeof onResumeContentChanged === 'function') {
          onResumeContentChanged("");
        }
      });
    }
  }

  // Initialize all functionality
  setupFileUpload();
  setupResumeImport();
  setupManualEntry();

  // Return public methods
  return {
    /**
     * Gets the current resume content
     * @returns {string} The resume content
     */
    getResumeContent: () => resumeContentTextarea?.value || "",

    /**
     * Sets the resume content
     * @param {string} content - The content to set
     * @param {string} source - Source name to display
     */
    setResumeContent: (content, source = "Resume") => {
      if (resumeContentTextarea) {
        resumeContentTextarea.value = content;
        updateUIAfterContentSet(source);
      }
    },

    /**
     * Clears the resume content and resets the UI
     */
    clearResumeContent: () => {
      if (resumeContentTextarea) resumeContentTextarea.value = "";
      if (resumeOptionsSelector) resumeOptionsSelector.classList.remove("hidden");
      if (resumeFileAddedUI) resumeFileAddedUI.classList.add("hidden");
      if (manualEntryContainer) manualEntryContainer.classList.add("hidden");
      if (resumeFileInput) resumeFileInput.value = "";

      if (typeof onResumeContentChanged === 'function') {
        onResumeContentChanged("");
      }
    },


  };
}
