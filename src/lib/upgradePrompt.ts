/**
 * Upgrade Prompt Utility
 *
 * This utility provides functions to programmatically show and hide the upgrade prompt modal.
 * It works with the UpgradePrompt.astro component and provides a consistent way to
 * handle upgrade prompts across the application.
 */

declare global {
  interface Window {
    upgradePromptFunctions?: {
      [key: string]: {
        show: () => void;
        hide: () => void;
      };
    };
  }
}

/**
 * Shows the upgrade prompt modal with the specified ID
 * @param id The ID of the upgrade prompt to show
 * @returns true if the modal was found and shown, false otherwise
 */
export function showUpgradePrompt(id: string = 'upgradePrompt'): boolean {
  if (window.upgradePromptFunctions && window.upgradePromptFunctions[id]) {
    window.upgradePromptFunctions[id].show();
    return true;
  }

  // If the modal doesn't exist yet, create it dynamically
  if (!document.getElementById(id)) {
    createDynamicUpgradePrompt(id);
    return true;
  }

  return false;
}

/**
 * Hides the upgrade prompt modal with the specified ID
 * @param id The ID of the upgrade prompt to hide
 * @returns true if the modal was found and hidden, false otherwise
 */
export function hideUpgradePrompt(id: string = 'upgradePrompt'): boolean {
  if (window.upgradePromptFunctions && window.upgradePromptFunctions[id]) {
    window.upgradePromptFunctions[id].hide();
    return true;
  }
  return false;
}

/**
 * Creates a dynamic upgrade prompt modal if one doesn't exist
 * This is useful for components that don't include the UpgradePrompt component in their markup
 * @param id The ID to use for the new upgrade prompt
 * @param featureName Optional feature name to customize the message
 * @param customMessage Optional custom message to display
 */
function createDynamicUpgradePrompt(
  id: string = 'upgradePrompt',
  featureName: string = 'feature',
  customMessage?: string
): void {
  const message = customMessage || `You've reached the maximum number of ${featureName} for your current plan.`;

  const modalHTML = `
    <div
      id="${id}"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4 transition-all duration-300 opacity-0 pointer-events-none"
    >
      <div
        class="bg-white dark:bg-gray-900 rounded-2xl p-8 max-w-md w-full text-center shadow-xl transform transition-all duration-300 scale-100"
        id="${id}-content"
      >
        <!-- Usage Limit Icon -->
        <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/30 mb-4">
          <svg class="h-6 w-6 text-yellow-600 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
          </svg>
        </div>

        <h2 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
          Usage Limit Reached
        </h2>

        <p class="text-gray-600 dark:text-gray-400 mb-6">
          ${message}
        </p>

        <div class="flex flex-col sm:flex-row justify-center gap-3">
          <button
            id="${id}-upgrade-button"
            class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-2.5 rounded-full bg-black dark:bg-white text-white dark:text-black font-semibold text-sm hover:bg-gray-800 dark:hover:bg-gray-100 transition duration-300 ease-in-out"
          >
            Upgrade to Pro
          </button>

          <button
            id="${id}-close-button"
            class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-2.5 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 font-semibold text-sm hover:bg-gray-300 dark:hover:bg-gray-600 transition duration-300 ease-in-out"
          >
            Maybe Later
          </button>
        </div>
      </div>
    </div>
  `;

  // Create container and append to body
  const container = document.createElement('div');
  container.innerHTML = modalHTML;
  document.body.appendChild(container);

  // Get modal elements
  const modal = document.getElementById(id);
  const upgradeButton = document.getElementById(`${id}-upgrade-button`);
  const closeButton = document.getElementById(`${id}-close-button`);

  // Function to hide the modal
  function hideModal() {
    if (modal) {
      modal.classList.remove('opacity-100', 'pointer-events-auto');
      modal.classList.add('opacity-0', 'pointer-events-none');
    }
  }

  // Function to show the modal
  function showModal() {
    if (modal) {
      modal.classList.remove('opacity-0', 'pointer-events-none');
      modal.classList.add('opacity-100', 'pointer-events-auto');
    }
  }

  // Handle upgrade button click
  upgradeButton?.addEventListener('click', () => {
    window.location.href = '/pricing';
  });

  // Handle close button click
  closeButton?.addEventListener('click', () => {
    hideModal();
  });

  // Close modal when clicking outside
  modal?.addEventListener('click', (e) => {
    if (e.target === modal) {
      hideModal();
    }
  });

  // Close on escape key
  const escapeHandler = (e: KeyboardEvent) => {
    if (e.key === 'Escape' && modal && !modal.classList.contains('opacity-0')) {
      hideModal();
    }
  };

  document.addEventListener('keydown', escapeHandler);

  // Register functions
  if (window.upgradePromptFunctions === undefined) {
    window.upgradePromptFunctions = {};
  }

  window.upgradePromptFunctions[id] = {
    show: showModal,
    hide: hideModal
  };

  // Show the modal immediately
  showModal();
}

/**
 * Checks feature access and shows upgrade prompt if needed
 * @param userId User ID to check access for
 * @param feature Feature to check access for
 * @param featureName Human-readable feature name for the prompt
 * @param customMessage Optional custom message to display
 * @returns Promise resolving to true if user has access, false otherwise
 */
export async function checkFeatureAccessAndPrompt(
  userId: string,
  feature: string, // Keep as string for backward compatibility
  featureName: string,
  customMessage?: string
): Promise<boolean> {
  // Dynamically import TierManagementService to avoid circular dependencies
  const { TierManagementService } = await import('./tierManagement');

  try {
    // Use type assertion to convert string to FeatureKey
    const hasAccess = await TierManagementService.checkFeatureAccess(userId, feature as any);

    if (!hasAccess) {
      // Show upgrade prompt
      const promptId = `${feature}UpgradePrompt`;
      if (!showUpgradePrompt(promptId)) {
        createDynamicUpgradePrompt(promptId, featureName, customMessage);
      }
      return false;
    }

    return true;
  } catch (error) {
    console.error(`Error checking access for ${feature}:`, error);
    return false;
  }
}
