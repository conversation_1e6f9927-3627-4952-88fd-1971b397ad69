/**
 * Modal Transitions Utility
 *
 * This utility provides consistent transition animations for modals and tool components
 * throughout the application.
 */

/**
 * Opens a modal with smooth transition animation
 * @param modalElement The modal container element
 * @param contentElement The modal content element that will be animated
 * @param options Additional options for the animation
 */
export function openModalWithTransition(
  modalElement: HTMLElement | null,
  contentElement: HTMLElement | null,
  options: {
    backdropElement?: HTMLElement | null;
    duration?: number;
    contentTransform?: string;
    addBodyOverflowHidden?: boolean;
  } = {}
) {
  if (!modalElement || !contentElement) return;

  const {
    backdropElement = null,
    duration = 200,
    contentTransform = 'scale(0.95) translateY(-10px)',
    addBodyOverflowHidden = true
  } = options;

  // First make the modal visible but still transparent
  modalElement.classList.remove('invisible', 'opacity-0', 'pointer-events-none', 'hidden');
  if (modalElement.classList.contains('hidden')) {
    modalElement.classList.remove('hidden');
    modalElement.classList.add('flex');
  }

  // Add overflow hidden to body if requested
  if (addBodyOverflowHidden) {
    document.body.classList.add('overflow-hidden');
  }

  // Force a reflow to ensure the transition works
  void modalElement.offsetWidth;

  // Fade in the modal
  modalElement.classList.add('opacity-100');

  // Fade in backdrop if provided
  if (backdropElement) {
    backdropElement.classList.add('opacity-100');
  }

  // Set initial transform for content
  contentElement.style.transition = `transform ${duration}ms ease-out, opacity ${duration}ms ease-out`;
  contentElement.style.transform = contentTransform;
  contentElement.style.opacity = '0';

  // Force a reflow
  void contentElement.offsetWidth;

  // Animate in the content
  setTimeout(() => {
    contentElement.style.transform = 'scale(1) translateY(0)';
    contentElement.style.opacity = '1';
  }, 10);
}

/**
 * Closes a modal with smooth transition animation
 * @param modalElement The modal container element
 * @param contentElement The modal content element that will be animated
 * @param options Additional options for the animation
 * @returns Promise that resolves when the animation is complete
 */
export function closeModalWithTransition(
  modalElement: HTMLElement | null,
  contentElement: HTMLElement | null,
  options: {
    backdropElement?: HTMLElement | null;
    duration?: number;
    contentTransform?: string;
    removeBodyOverflowHidden?: boolean;
    onComplete?: () => void;
  } = {}
): Promise<void> {
  return new Promise((resolve) => {
    if (!modalElement || !contentElement) {
      resolve();
      return;
    }

    const {
      backdropElement = null,
      duration = 200,
      contentTransform = 'scale(0.95) translateY(10px)',
      removeBodyOverflowHidden = true,
      onComplete
    } = options;

    // Animate out the content
    contentElement.style.transition = `transform ${duration}ms ease-in, opacity ${duration}ms ease-in`;
    contentElement.style.transform = contentTransform;
    contentElement.style.opacity = '0';

    // Fade out backdrop if provided
    if (backdropElement) {
      backdropElement.classList.remove('opacity-100');
    }

    // Wait for the animation to complete
    setTimeout(() => {
      // Hide the modal
      modalElement.classList.add('invisible', 'opacity-0', 'pointer-events-none');
      if (modalElement.classList.contains('flex')) {
        modalElement.classList.remove('flex');
        modalElement.classList.add('hidden');
      }

      // Remove overflow hidden from body if requested
      if (removeBodyOverflowHidden) {
        document.body.classList.remove('overflow-hidden');
      }

      // Execute onComplete callback if provided
      if (onComplete) {
        onComplete();
      }

      resolve();
    }, duration);
  });
}
