import { authService } from "./auth";
import type { User } from "firebase/auth";
import { APIClient } from "./apiClient"; // Import APIClient

/**
 * Interface for resume data
 */
export interface ResumeData {
  id?: string;
  name?: string;
  content: string;
  fileName?: string;
  fileType?: string;
  createdAt?: string;
  updatedAt?: string;
  customInstructions?: string; // Added customInstructions
}

/**
 * Options for the ResumeInputHandler
 */
export interface ResumeInputHandlerOptions {
  componentId: string;
  fileInputId: string;
  fileUploadButtonId: string;
  fileAddedUIId: string;
  fileNameElementId: string;
  changeSourceButtonId: string;
  optionsSelectorId: string;
  manualEntryContainerId: string;
  enterManuallyButtonId: string;
  cancelManualEntryButtonId: string;
  resumeContentTextareaId: string;
  importResumeButtonId: string;
  onResumeLoaded?: (resumeData: ResumeData) => void;
  onError?: (title: string, message: string) => void;
  onResumeContentChanged?: (content: string) => void;
  userId?: string | null;
  idToken?: string | null;
}

/**
 * Setup resume input handler with the given options
 * @param options Configuration options
 * @returns The resume input handler instance
 */
export function setupResumeInput(options: Partial<ResumeInputHandlerOptions>): ResumeInputHandler {
  // Default options
  const defaultOptions: ResumeInputHandlerOptions = {
    componentId: "default",
    fileInputId: "resumeFileInput",
    fileUploadButtonId: "resumeFileUploadButton",
    fileAddedUIId: "resumeFileAddedUI",
    fileNameElementId: "resumeFileName",
    changeSourceButtonId: "changeResumeSource",
    optionsSelectorId: "optionsSelector", // Corrected ID
    manualEntryContainerId: "manualEntryContainer",
    enterManuallyButtonId: "enterManuallyButton",
    cancelManualEntryButtonId: "cancelManualEntry",
    resumeContentTextareaId: "resumeContent",
    importResumeButtonId: "importResumeButton",
  };

  // Merge options with defaults
  const mergedOptions = { ...defaultOptions, ...options };

  // Create and initialize the handler
  const handler = new ResumeInputHandler(mergedOptions);
  handler.initialize().catch(error => {
    console.error("Failed to initialize resume input handler:", error);
    if (mergedOptions.onError) {
      mergedOptions.onError("Initialization Error", "Failed to initialize resume input handler");
    }
  });

  return handler;
}

/**
 * Handles resume input functionality across components
 */
export class ResumeInputHandler {
  private componentId: string;
  private fileInputId: string;
  private fileUploadButtonId: string;
  private fileAddedUIId: string;
  private fileNameElementId: string;
  private changeSourceButtonId: string;
  private optionsSelectorId: string;
  private manualEntryContainerId: string;
  private enterManuallyButtonId: string;
  private cancelManualEntryButtonId: string;
  private resumeContentTextareaId: string;
  private importResumeButtonId: string;
  private onResumeLoaded?: (resumeData: ResumeData) => void;
  private onError?: (title: string, message: string) => void;

  private resumeData: ResumeData = { content: "" };
  private currentUser: User | null = null;

  /**
   * Create a new ResumeInputHandler
   * @param options Configuration options
   */
  constructor(options: ResumeInputHandlerOptions) {
    this.componentId = options.componentId;
    this.fileInputId = options.fileInputId;
    this.fileUploadButtonId = options.fileUploadButtonId;
    this.fileAddedUIId = options.fileAddedUIId;
    this.fileNameElementId = options.fileNameElementId;
    this.changeSourceButtonId = options.changeSourceButtonId;
    this.optionsSelectorId = options.optionsSelectorId;
    this.manualEntryContainerId = options.manualEntryContainerId;
    this.enterManuallyButtonId = options.enterManuallyButtonId;
    this.cancelManualEntryButtonId = options.cancelManualEntryButtonId;
    this.resumeContentTextareaId = options.resumeContentTextareaId;
    this.importResumeButtonId = options.importResumeButtonId;
    this.onResumeLoaded = options.onResumeLoaded;
    this.onError = options.onError;
  }

  /**
   * Initialize the resume input handler
   */
  async initialize(): Promise<void> {
    try {
      // Get current user
      this.currentUser = await authService.getCurrentUser();

      // Setup event listeners
      this.setupResumeUpload();
      this.setupManualEntry();
      this.setupResumeImport();
      this.setupResumeFormControls();
    } catch (error) {
      console.error("Error initializing resume input handler:", error);
      this.handleError("Initialization Error", "Failed to initialize resume input handler");
    }
  }

  /**
   * Setup resume file upload functionality
   */
  private setupResumeUpload(): void {
    const fileUploadButton = document.getElementById(this.fileUploadButtonId);
    let fileInput = document.getElementById(this.fileInputId) as HTMLInputElement | null;

    // If the file input doesn't exist, create it dynamically
    if (!fileInput) {
      fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.id = this.fileInputId;
      fileInput.className = 'hidden';
      fileInput.accept = '.pdf,.doc,.docx,.txt,.rtf';
      document.body.appendChild(fileInput);
    }

    if (fileUploadButton && fileInput) {
      // Remove any existing event listeners to prevent duplicates
      const newFileInput = fileInput.cloneNode(true) as HTMLInputElement;
      if (fileInput.parentNode) {
        fileInput.parentNode.replaceChild(newFileInput, fileInput);
      }
      fileInput = newFileInput;

      fileUploadButton.addEventListener("click", (e) => {
        e.preventDefault();
        fileInput?.click();
      });

      fileInput.addEventListener("change", async (event) => {
        const file = (event.target as HTMLInputElement).files?.[0];
        if (!file) return;

        try {
          // Check file type
          const supportedTypes = ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "text/plain"];
          if (!supportedTypes.includes(file.type)) {
            this.handleError(
              "Unsupported File Type",
              "Please upload a PDF, DOCX, or TXT file."
            );
            fileInput!.value = "";
            return;
          }

          // Get custom instructions value
          const customInstructionsEl = document.getElementById("customInstructions") as HTMLTextAreaElement | null;
          const customInstructions = customInstructionsEl?.value || undefined;


          // Read file content (will be string for TXT, File for PDF/DOCX)
          const fileContent = await this.readFileContent(file);

          if (typeof fileContent === 'string') {
            // --- Handle TXT file ---
            this.resumeData = {
              content: fileContent,
              fileName: file.name,
              fileType: file.type,
              customInstructions: customInstructions // Include custom instructions
            };

            // Show success UI
            this.showFileAddedUI(file.name);

            // Update the resume content textarea
             const resumeContentTextarea = document.getElementById(this.resumeContentTextareaId) as HTMLTextAreaElement | null;
             if (resumeContentTextarea) {
               resumeContentTextarea.value = this.resumeData.content;
             }


            // Call onResumeLoaded callback if provided
            if (this.onResumeLoaded) {
              this.onResumeLoaded(this.resumeData);
            }
          } else {
            // --- Handle PDF/DOCX file - Initiate Backend Upload ---
            // Show loading state for upload (requires UI element)
            // For now, we'll just log and proceed with the API call

            try {
              // Convert File to Base64
              const base64Content = await this.fileToBase64(fileContent);

              // Make authenticated API call to upload-resume
              const uploadResponse = await this.callUploadResumeAPI(base64Content, file.type, customInstructions); // Pass customInstructions

              if (uploadResponse.success && uploadResponse.data?.text) {
                // Update resume data with cleaned text from backend
                this.resumeData = {
                  content: uploadResponse.data.text,
                  fileName: file.name, // Keep original file name
                  fileType: file.type, // Keep original file type
                  customInstructions: customInstructions // Include custom instructions
                };

                // Show success UI
                this.showFileAddedUI(file.name);

                // Update the resume content textarea with the cleaned text
                const resumeContentTextarea = document.getElementById(this.resumeContentTextareaId) as HTMLTextAreaElement | null;
                if (resumeContentTextarea) {
                  resumeContentTextarea.value = this.resumeData.content;
                }


                // Call onResumeLoaded callback if provided
                if (this.onResumeLoaded) {
                  this.onResumeLoaded(this.resumeData);
                }

              } else {
                 // Handle API error response
                 const errorMessage = uploadResponse.error || "Unknown error during file upload processing.";
                 this.handleError("Upload Processing Error", errorMessage);
                 fileInput!.value = ""; // Clear file input
                 this.resumeData = { content: "" }; // Clear resume data
              }

            } catch (apiError) {
              console.error("Error calling upload-resume API:", apiError);
              this.handleError(
                "Upload API Error",
                "Failed to process file with backend. Please try again."
              );
              fileInput!.value = ""; // Clear file input
              this.resumeData = { content: "" }; // Clear resume data
            } finally {
               // Hide loading state for upload (requires UI element)
            }
          }
        } catch (error) {
          console.error("Error processing resume file:", error);
          this.handleError(
            "File Processing Error",
            "Failed to process the resume file. Please try again."
          );
          fileInput!.value = "";
          this.resumeData = { content: "" }; // Clear resume data on error
        }
      });
    }
  }

  /**
   * Convert File object to Base64 string
   * @param file The File object
   * @returns The Base64 string
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        // The result is a data URL (e.g., "data:image/png;base64,iVBORw0KGgo...")
        // We need to extract the base64 part after the comma
        const base64String = reader.result as string;
        resolve(base64String.split(',')[1]);
      };
      reader.onerror = (error) => {
        reject(error);
      };
      reader.readAsDataURL(file);
    });
  }

  /**
   * Call the upload-resume backend API
   * @param fileBase64 The file content as a Base64 string
   * @param fileType The file MIME type
   * @param customInstructions Optional custom instructions
   * @returns The API response data
   */
  private async callUploadResumeAPI(fileBase64: string, fileType: string, customInstructions?: string): Promise<{ success: boolean; data?: { text: string }; error?: string }> {
     try {
       // Ensure user is authenticated before making the call
       const user = await authService.getCurrentUser();
       if (!user) {
         throw new Error("Authentication required to upload and process files.");
       }
       const idToken = await user.getIdToken();

       const response = await fetch('/.netlify/functions/upload-resume', {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json',
           'Authorization': `Bearer ${idToken}` // Include the ID token
         },
         body: JSON.stringify({
           fileBase64,
           fileType,
           customInstructions
         })
       });

       const responseData = await response.json();

       if (!response.ok) {
         // Handle non-OK responses from the backend function
         const errorMessage = responseData.error || `API request failed with status ${response.status}`;
         throw new Error(errorMessage);
       }

       return responseData; // Should contain { success: true, data: { text: string } }

     } catch (error) {
       console.error("Error in callUploadResumeAPI:", error);
       // Return a structured error object
       return {
         success: false,
         error: error instanceof Error ? error.message : "Unknown API error"
       };
     }
  }

  /**
   * Setup manual resume entry functionality
   */
  private setupManualEntry(): void {
    const enterManuallyButton = document.getElementById(this.enterManuallyButtonId);
    const cancelManualEntry = document.getElementById(this.cancelManualEntryButtonId);
    const manualEntryContainer = document.getElementById(this.manualEntryContainerId);
    const optionsSelector = document.getElementById(this.optionsSelectorId);
    const resumeContentTextarea = document.getElementById(this.resumeContentTextareaId) as HTMLTextAreaElement | null;

    if (enterManuallyButton && manualEntryContainer && optionsSelector) {
      enterManuallyButton.addEventListener("click", () => {
        optionsSelector.classList.add("hidden");
        manualEntryContainer.classList.remove("hidden");
      });
    }

    if (cancelManualEntry && manualEntryContainer && optionsSelector) {
      cancelManualEntry.addEventListener("click", () => {
        manualEntryContainer.classList.add("hidden");
        optionsSelector.classList.remove("hidden");
      });
    }

    if (resumeContentTextarea) {
      resumeContentTextarea.addEventListener("input", () => {
        this.resumeData = {
          content: resumeContentTextarea.value,
          fileName: "Manual Entry",
          fileType: "text/plain"
        };

        // Call onResumeLoaded callback if provided
        if (this.onResumeLoaded && resumeContentTextarea.value.trim().length > 0) {
          this.onResumeLoaded(this.resumeData);
        }
      });
    }
  }

  /**
   * Setup resume import functionality
   */
  private setupResumeImport(): void {
    const importResumeButton = document.getElementById(this.importResumeButtonId);

    if (importResumeButton) {
      importResumeButton.addEventListener("click", async () => {
        try {
          if (!this.currentUser) {
            this.handleError(
              "Authentication Required",
              "Please log in to import resumes from your account."
            );
            return;
          }

          // Open resume manager modal
          const { showResumeManagerModal } = await import("./resumeManager/index");
          showResumeManagerModal(this.componentId + "ResumeManagerModal", (selectedResume: any) => {
            if (selectedResume && selectedResume.content) {
              this.resumeData = selectedResume;

              // Show success UI
              this.showFileAddedUI(selectedResume.name || "Imported Resume");

              // Call onResumeLoaded callback if provided
              if (this.onResumeLoaded) {
                this.onResumeLoaded(this.resumeData);
              }
            }
          });
        } catch (error) {
          console.error("Error importing resume:", error);
          this.handleError(
            "Import Error",
            "Failed to open resume manager. Please try again."
          );
        }
      });
    }
  }

  /**
   * Setup resume form controls
   */
  private setupResumeFormControls(): void {
    const changeSourceButton = document.getElementById(this.changeSourceButtonId);
    const fileAddedUI = document.getElementById(this.fileAddedUIId);
    const optionsSelector = document.getElementById(this.optionsSelectorId);
    const fileInput = document.getElementById(this.fileInputId) as HTMLInputElement | null;

    if (changeSourceButton && fileAddedUI && optionsSelector) {
      changeSourceButton.addEventListener("click", () => {
        optionsSelector.classList.remove("hidden");
        fileAddedUI.classList.add("hidden");
        if (fileInput) fileInput.value = "";
        this.resumeData = { content: "" };
      });
    }
  }

  /**
   * Show file added UI
   * @param fileName The name of the file to display
   */
  private showFileAddedUI(fileName: string): void {
    const fileAddedUI = document.getElementById(this.fileAddedUIId);
    const optionsSelector = document.getElementById(this.optionsSelectorId);
    const manualEntryContainer = document.getElementById(this.manualEntryContainerId);
    const fileNameElement = document.getElementById(this.fileNameElementId);

    if (fileAddedUI && optionsSelector) {
      optionsSelector.classList.add("hidden");
      fileAddedUI.classList.remove("hidden");

      if (manualEntryContainer) {
        manualEntryContainer.classList.add("hidden");
      }

      if (fileNameElement) {
        fileNameElement.textContent = fileName || "Resume added successfully";
      }
    }
  }

  /**
   * Read file content
   * @param file The file to read
   * @returns The file content as a string for text files, or the File object for binary files.
   */
  private async readFileContent(file: File): Promise<string | File> {
    return new Promise((resolve, reject) => {
      if (file.type === "text/plain") {
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          resolve(content);
        };
        reader.onerror = () => {
          reject(new Error("Failed to read text file"));
        };
        reader.readAsText(file);
      } else {
        // For binary files (PDF, DOCX), return the File object to be processed by the backend upload
        resolve(file);
      }
    });
  }

  /**
   * Handle error
   * @param title The error title
   * @param message The error message
   */
  private handleError(title: string, message: string): void {
    console.error(`${title}: ${message}`);

    if (this.onError) {
      this.onError(title, message);
    } else {
      // Fallback to alert if no error handler is provided
      alert(`${title}: ${message}`);
    }
  }

  /**
   * Get the current resume data
   * @returns The current resume data
   */
  getResumeData(): ResumeData {
    return this.resumeData;
  }

  /**
   * Get the current resume content
   * @returns The current resume content as a string
   */
  getResumeContent(): string {
    return this.resumeData.content || "";
  }

  /**
   * Clear the resume data
   */
  clearResumeData(): void {
    this.resumeData = { content: "" };

    const fileInput = document.getElementById(this.fileInputId) as HTMLInputElement | null;
    const resumeContentTextarea = document.getElementById(this.resumeContentTextareaId) as HTMLTextAreaElement | null;
    const fileAddedUI = document.getElementById(this.fileAddedUIId);
    const optionsSelector = document.getElementById(this.optionsSelectorId);

    if (fileInput) fileInput.value = "";
    if (resumeContentTextarea) resumeContentTextarea.value = "";

    if (fileAddedUI && optionsSelector) {
      fileAddedUI.classList.add("hidden");
      optionsSelector.classList.remove("hidden");
    }
  }

  /**
   * Clear the resume content
   */
  clearResumeContent(): void {
    this.clearResumeData();
  }

  /**
   * Set the resume content
   * @param content The content to set
   */
  setResumeContent(content: string): void {
    this.resumeData.content = content;

    const resumeContentTextarea = document.getElementById(this.resumeContentTextareaId) as HTMLTextAreaElement | null;
    if (resumeContentTextarea) {
      resumeContentTextarea.value = content;
    }

    // Call onResumeContentChanged callback if provided
    if (this.onResumeLoaded) {
      this.onResumeLoaded(this.resumeData);
    }
  }

  /**
   * Trigger file upload dialog
   */
  triggerFileUpload(): void {
    const fileInput = document.getElementById(this.fileInputId) as HTMLInputElement | null;
    if (fileInput) {
      fileInput.click();
    }
  }

  /**
   * Show import modal
   * @param options Options for the import modal
   */
  showImportModal(options: { onResumeSelected: (resumeName: string, resumeContent: string) => void }): void {
    import("./resumeManager/index").then(({ showResumeManagerModal }) => {
      showResumeManagerModal(this.componentId + "ResumeManagerModal", (selectedResume: any) => {
        if (selectedResume && selectedResume.content) {
          this.resumeData = selectedResume;

          // Call the callback
          options.onResumeSelected(
            selectedResume.name || "Imported Resume",
            selectedResume.content
          );
        }
      });
    }).catch(error => {
      console.error("Failed to import resume manager:", error);
      this.handleError(
        "Import Error",
        "Failed to open resume manager. Please try again."
      );
    });
  }
}
