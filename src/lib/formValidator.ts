/**
 * Centralized form validation utility for consistent validation across components
 */
export class FormValidator {
  /**
   * Validates a text input or textarea
   * @param input The input element to validate
   * @param options Validation options
   * @returns Whether the input is valid
   */
  static validateInput(
    input: HTMLInputElement | HTMLTextAreaElement,
    options: {
      minLength?: number;
      maxLength?: number;
      required?: boolean;
      pattern?: RegExp;
      customValidator?: (value: string) => boolean;
      errorMessages?: {
        tooShort?: string;
        tooLong?: string;
        required?: string;
        pattern?: string;
        custom?: string;
      }
    } = {}
  ): boolean {
    const {
      minLength = 0,
      maxLength = Infinity,
      required = true,
      pattern,
      customValidator,
      errorMessages = {}
    } = options;
    
    const value = input.value.trim();
    
    // Check if required
    if (required && value.length === 0) {
      this.showError(input, errorMessages.required || 'This field is required');
      return false;
    }
    
    // Check min length
    if (value.length > 0 && value.length < minLength) {
      this.showError(input, errorMessages.tooShort || `Must be at least ${minLength} characters`);
      return false;
    }
    
    // Check max length
    if (value.length > maxLength) {
      this.showError(input, errorMessages.tooLong || `Must be at most ${maxLength} characters`);
      return false;
    }
    
    // Check pattern
    if (pattern && value.length > 0 && !pattern.test(value)) {
      this.showError(input, errorMessages.pattern || 'Invalid format');
      return false;
    }
    
    // Check custom validator
    if (customValidator && value.length > 0 && !customValidator(value)) {
      this.showError(input, errorMessages.custom || 'Invalid input');
      return false;
    }
    
    // Clear error if valid
    this.clearError(input);
    return true;
  }
  
  /**
   * Shows an error message for an input
   */
  static showError(input: HTMLInputElement | HTMLTextAreaElement, message: string): void {
    // Add error class to input
    input.classList.add('border-red-500');
    
    // Find or create error message element
    let errorEl = input.nextElementSibling as HTMLElement | null;
    if (!errorEl || !errorEl.classList.contains('validation-error')) {
      errorEl = document.createElement('div');
      errorEl.classList.add('validation-error', 'text-red-500', 'text-sm', 'mt-1');
      input.parentNode?.insertBefore(errorEl, input.nextSibling);
    }
    
    errorEl.textContent = message;
    errorEl.style.display = 'block';
  }
  
  /**
   * Clears error message for an input
   */
  static clearError(input: HTMLInputElement | HTMLTextAreaElement): void {
    // Remove error class from input
    input.classList.remove('border-red-500');
    
    // Hide error message
    const errorEl = input.nextElementSibling as HTMLElement | null;
    if (errorEl && errorEl.classList.contains('validation-error')) {
      errorEl.textContent = '';
      errorEl.style.display = 'none';
    }
  }
  
  /**
   * Validates a form with multiple inputs
   * @param inputs Array of input elements to validate
   * @param options Validation options for each input
   * @returns Whether all inputs are valid
   */
  static validateForm(
    inputs: (HTMLInputElement | HTMLTextAreaElement)[],
    options: Record<string, any> = {}
  ): boolean {
    let isValid = true;
    
    inputs.forEach((input) => {
      const inputOptions = options[input.id] || {};
      if (!this.validateInput(input, inputOptions)) {
        isValid = false;
        input.focus(); // Focus the first invalid field
      }
    });
    
    return isValid;
  }
}
