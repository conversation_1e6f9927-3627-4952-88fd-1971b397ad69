/**
 * Resume Manager Utility
 * 
 * This utility provides functions to programmatically show the resume manager modal
 * and handle resume selection.
 */

import { PersistentDocumentService } from "./persistentDocumentService";

/**
 * Resume data interface
 */
export interface Resume {
  id: string;
  name: string;
  content: string;
  lastUpdated: number;
  userId?: string;
  createdAt: number;
}

/**
 * Shows the resume manager modal
 * @param modalId The ID for the modal
 * @param onResumeSelected Callback function called when a resume is selected
 */
export function showResumeManagerModal(
  modalId: string,
  onResumeSelected: (selectedResume: Resume | null) => void
): void {
  // Create a custom event to trigger the resume manager modal
  const event = new CustomEvent('showResumeManager', {
    detail: {
      modalId,
      onResumeSelected
    }
  });
  
  // Dispatch the event
  document.dispatchEvent(event);

  // If the event handler isn't set up yet, we'll create a simple modal
  // This is a fallback in case the ResumeManager component isn't loaded
  const fallbackTimeout = setTimeout(() => {
    createDynamicResumeManager(modalId, onResumeSelected);
  }, 100);

  // Listen for the 'resumeManagerShown' event to cancel the fallback
  const cancelFallback = () => {
    clearTimeout(fallbackTimeout);
    document.removeEventListener('resumeManagerShown', cancelFallback);
  };
  document.addEventListener('resumeManagerShown', cancelFallback, { once: true }); // Add { once: true }
}

/**
 * Creates a dynamic resume manager if the component isn't loaded
 * @param modalId The ID for the modal
 * @param onResumeSelected Callback function called when a resume is selected
 */
async function createDynamicResumeManager(
  modalId: string,
  onResumeSelected: (selectedResume: Resume | null) => void
): Promise<void> {
  // Check if the modal already exists
  if (document.getElementById(modalId)) {
    return;
  }
  
  try {
    // Load resumes from the service
    const resumes = await PersistentDocumentService.loadAllResumes();
    
    // Create a simple modal to display resumes
    const modalHTML = `
      <div id="${modalId}" class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">Select a Resume</h2>
            <button id="${modalId}-close" class="text-gray-500 hover:text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div id="${modalId}-resumes" class="max-h-60 overflow-y-auto">
            ${resumes.length === 0 
              ? '<p class="text-center py-4">No resumes found. Please create a resume first.</p>' 
              : resumes.map(resume => `
                <div class="resume-item p-3 border border-gray-200 dark:border-gray-700 rounded-lg mb-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700" data-id="${resume.id}">
                  <h3 class="font-medium">${resume.name}</h3>
                  <p class="text-sm text-gray-500 truncate">${resume.content.substring(0, 100)}${resume.content.length > 100 ? '...' : ''}</p>
                </div>
              `).join('')}
          </div>
          <div class="mt-4 flex justify-end">
            <button id="${modalId}-cancel" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg mr-2">Cancel</button>
          </div>
        </div>
      </div>
    `;
    
    // Create container and append to body
    const container = document.createElement('div');
    container.innerHTML = modalHTML;
    document.body.appendChild(container);
    
    // Add event listeners
    const modal = document.getElementById(modalId);
    const closeButton = document.getElementById(`${modalId}-close`);
    const cancelButton = document.getElementById(`${modalId}-cancel`);
    const resumesContainer = document.getElementById(`${modalId}-resumes`);
    
    // Close modal function
    const closeModal = () => {
      if (modal) {
        modal.remove();
      }
    };
    
    // Close on close button click
    closeButton?.addEventListener('click', () => {
      closeModal();
      onResumeSelected(null);
    });
    
    // Close on cancel button click
    cancelButton?.addEventListener('click', () => {
      closeModal();
      onResumeSelected(null);
    });
    
    // Handle resume selection
    resumesContainer?.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const resumeItem = target.closest('.resume-item') as HTMLElement;
      
      if (resumeItem) {
        const resumeId = resumeItem.getAttribute('data-id');
        if (resumeId) {
          const selectedResume = resumes.find(r => r.id === resumeId);
          if (selectedResume) {
            closeModal();
            onResumeSelected(selectedResume);
          }
        }
      }
    });
  } catch (error) {
    console.error('Failed to create dynamic resume manager:', error);
    // Show error message
    alert('Failed to load resumes. Please try again.');
  }
}
