/**
 * Centralized loading state management utility for consistent loading indicators across components
 */
export class LoadingManager {
  private componentId: string;
  
  /**
   * Create a new LoadingManager instance
   * @param componentId The ID prefix for the component (e.g., 'jobAnalysis', 'interviewPrep')
   */
  constructor(componentId: string) {
    this.componentId = componentId;
  }
  
  /**
   * Set a button's loading state
   * @param buttonId The ID of the button element or the button loader component
   * @param isLoading Whether the button should be in loading state
   * @param loadingText Optional text to display while loading
   */
  async setButtonLoading(buttonId: string, isLoading: boolean, loadingText?: string): Promise<void> {
    try {
      const { setButtonLoading } = await import("./loadingState");
      setButtonLoading(buttonId, isLoading, loadingText);
    } catch (err) {
      console.error("Failed to import loading state utility:", err);
      
      // Fallback to direct DOM manipulation
      const button = document.getElementById(buttonId) as HTMLButtonElement | null;
      if (button) {
        button.disabled = isLoading;
        if (isLoading) {
          button.dataset.originalText = button.innerHTML;
          button.innerHTML = loadingText ? 
            `<svg class="animate-spin -ml-1 mr-2 h-4 w-4 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>${loadingText}` : 
            button.dataset.originalText || '';
        } else if (button.dataset.originalText) {
          button.innerHTML = button.dataset.originalText;
        }
      }
    }
  }
  
  /**
   * Show a loader element
   * @param containerId The ID of the loader container element
   */
  showLoader(containerId: string): void {
    const container = document.getElementById(containerId);
    if (container) {
      container.classList.remove('hidden');
      container.classList.add('flex');
    }
  }
  
  /**
   * Hide a loader element
   * @param containerId The ID of the loader container element
   */
  hideLoader(containerId: string): void {
    const container = document.getElementById(containerId);
    if (container) {
      container.classList.add('hidden');
      container.classList.remove('flex');
    }
  }
  
  /**
   * Toggle the visibility of skeletal loader
   * @param visible Whether the skeletal loader should be visible
   */
  toggleSkeletalLoader(visible: boolean): void {
    const skeletalLoader = document.getElementById(`${this.componentId}SkeletalLoader`);
    if (skeletalLoader) {
      if (visible) {
        skeletalLoader.classList.remove('hidden');
      } else {
        skeletalLoader.classList.add('hidden');
      }
    }
  }
  
  /**
   * Toggle the visibility of the results container
   * @param visible Whether the results container should be visible
   * @param scrollIntoView Whether to scroll the results into view
   */
  toggleResultsContainer(visible: boolean, scrollIntoView: boolean = false): void {
    const resultsContainer = document.getElementById(`${this.componentId}ResultContainer`);
    if (resultsContainer) {
      if (visible) {
        resultsContainer.classList.remove('hidden');
        if (scrollIntoView) {
          resultsContainer.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      } else {
        resultsContainer.classList.add('hidden');
      }
    }
  }
}
