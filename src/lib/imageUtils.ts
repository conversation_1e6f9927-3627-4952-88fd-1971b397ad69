/**
 * Utility functions for handling image loading with fallbacks
 */
import { getAuth, updateProfile } from "firebase/auth";
import { doc, getFirestore, updateDoc } from "firebase/firestore";

export interface ImageLoadOptions {
  timeout?: number;
  retries?: number;
  fallbackUrl?: string;
}

/**
 * Loads an image with error handling and timeout
 * @param src - Image source URL
 * @param options - Loading options
 * @returns Promise that resolves with the loaded image or rejects with error
 */
export function loadImageWithFallback(
  src: string, 
  options: ImageLoadOptions = {}
): Promise<HTMLImageElement> {
  const { timeout = 5000, retries = 1 } = options;
  
  return new Promise((resolve, reject) => {
    let attempts = 0;
    
    const tryLoad = () => {
      attempts++;
      const img = new Image();
      let timeoutId: NodeJS.Timeout;
      
      const cleanup = () => {
        if (timeoutId) clearTimeout(timeoutId);
        img.onload = null;
        img.onerror = null;
      };
      
      img.onload = () => {
        cleanup();
        resolve(img);
      };
      
      img.onerror = () => {
        cleanup();
        if (attempts < retries) {
          console.warn(`Image load attempt ${attempts} failed, retrying...`);
          setTimeout(tryLoad, 1000); // Wait 1 second before retry
        } else {
          reject(new Error(`Failed to load image after ${attempts} attempts`));
        }
      };
      
      // Set timeout
      timeoutId = setTimeout(() => {
        cleanup();
        if (attempts < retries) {
          console.warn(`Image load attempt ${attempts} timed out, retrying...`);
          setTimeout(tryLoad, 1000);
        } else {
          reject(new Error(`Image loading timed out after ${timeout}ms`));
        }
      }, timeout);
      
      // Start loading
      img.src = src;
    };
    
    tryLoad();
  });
}

/**
 * Validates if a URL is a valid Google profile image URL
 * @param url - URL to validate
 * @returns boolean indicating if URL is valid
 */
export function isValidGooglePhotoURL(url: string): boolean {
  if (!url) return false;
  
  try {
    const urlObj = new URL(url);
    // Check if it's a valid Google profile image URL
    return urlObj.hostname.includes('googleusercontent.com') || 
           urlObj.hostname.includes('googleapis.com') ||
           urlObj.hostname.includes('google.com');
  } catch {
    return false;
  }
}

/**
 * Transforms a Google profile image URL to make it more reliable
 * - Removes size restrictions to get the original image
 * - Adds a cache-busting parameter
 * - Ensures the URL uses HTTPS
 * 
 * @param url - Original Google profile URL
 * @returns Transformed URL that's more likely to load successfully
 */
export function transformGooglePhotoURL(url: string, useProxy: boolean = false): string {
  if (!url) return url;
  
  try {
    // Check if it's a Google profile image
    if (!isValidGooglePhotoURL(url)) return url;
    
    const urlObj = new URL(url);
    
    // Force HTTPS
    urlObj.protocol = 'https:';
    
    // Remove size restrictions for googleusercontent.com URLs
    if (urlObj.hostname.includes('googleusercontent.com')) {
      // Remove any size parameters (s64, s128, etc.)
      urlObj.pathname = urlObj.pathname.replace(/=s\d+-c/, '=s512-c');
      
      // Add cache busting parameter
      urlObj.searchParams.set('cb', Date.now().toString());
    }
    
    const transformedUrl = urlObj.toString();
    
    // Use a CORS proxy if requested
    if (useProxy) {
      // Use a reliable CORS proxy service
      // Note: In production, you should use your own proxy or a paid service
      return `https://corsproxy.io/?${encodeURIComponent(transformedUrl)}`;
    }
    
    return transformedUrl;
  } catch {
    return url; // Return original if transformation fails
  }
}

/**
 * Creates a fallback avatar element with user's initials
 * @param displayName - User's display name
 * @param size - Avatar size ('mobile' | 'desktop')
 * @param isPro - Whether user has pro subscription
 * @returns HTML string for fallback avatar
 */
export function createFallbackAvatar(
  displayName: string, 
  size: 'mobile' | 'desktop' = 'desktop',
  isPro: boolean = false
): string {
  const firstLetter = displayName.charAt(0).toUpperCase();
  const sizeClasses = size === 'mobile' ? 'w-12 h-12 text-xl' : 'h-8 w-8 text-lg';
  const proRing = isPro ? ' ring-2 ring-[var(--color-purple)] ring-offset-2 ring-offset-white dark:ring-offset-gray-900' : '';
  
  return `
    <div class="user-avatar flex items-center justify-center rounded-full ${sizeClasses} bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white${proRing}">
      <span class="font-semibold">${firstLetter}</span>
    </div>
  `;
}

/**
 * Creates a loading avatar element
 * @param size - Avatar size ('mobile' | 'desktop')
 * @returns HTML string for loading avatar
 */
export function createLoadingAvatar(size: 'mobile' | 'desktop' = 'desktop'): string {
  const sizeClasses = size === 'mobile' ? 'w-12 h-12' : 'h-8 w-8';
  
  return `
    <div class="user-avatar flex items-center justify-center rounded-full ${sizeClasses}">
      <div class="w-full h-full rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
    </div>
  `;
}

/**
 * Updates the user's photoURL in Firebase Auth and Firestore if it's invalid
 * @param userId - User ID
 * @param currentPhotoURL - Current photo URL that's invalid
 */
export async function clearInvalidUserPhotoURL(userId: string): Promise<void> {
  try {
    const auth = getAuth();
    const currentUser = auth.currentUser;
    
    if (currentUser && currentUser.uid === userId) {
      // Update Firebase Auth profile
      await updateProfile(currentUser, {
        photoURL: null
      });
      
      // Update Firestore document
      const db = getFirestore();
      const userRef = doc(db, "users", userId);
      await updateDoc(userRef, {
        photoURL: ""
      });
      
      console.log("Cleared invalid photoURL for user:", userId);
    }
  } catch (error) {
    console.error("Failed to clear invalid photoURL:", error);
  }
}