/**
 * Centralized constants for feature names used across the application
 * This ensures consistency in feature access checks and usage tracking
 */

export const FEATURE_NAMES = {
  // Feature names for access checks (used with ComponentInitializer)
  RESUME_GENERATION: 'resumeGeneration',
  COVER_LETTER: 'coverLetterGeneration',
  JOB_ANALYSIS: 'jobAnalysis',
  INTERVIEW_PREP: 'interviewPrep',
  LINKEDIN_OPTIMIZATION: 'linkedinOptimization',

  // Feature keys for usage tracking (used with TierManagementService)
  RESUME_GENERATION_KEY: 'resumeGeneration',
  COVER_LETTER_KEY: 'coverLetterGeneration',
  JOB_ANALYSIS_KEY: 'jobAnalysis',
  INTERVIEW_PREP_KEY: 'interviewPrep',
  LINKEDIN_OPTIMIZATION_KEY: 'linkedinOptimization',
};

/**
 * Maps component IDs to their corresponding feature names and upgrade prompt IDs
 */
export const COMPONENT_FEATURE_MAP = {
  'resumeGenerator': {
    featureName: FEATURE_NAMES.RESUME_GENERATION,
    upgradePromptId: 'resumeUpgradePrompt',
    usageTrackingKey: FEATURE_NAMES.RESUME_GENERATION_KEY
  },
  'coverLetter': {
    featureName: FEATURE_NAMES.COVER_LETTER,
    upgradePromptId: 'coverLetterUpgradePrompt',
    usageTrackingKey: FEATURE_NAMES.COVER_LETTER_KEY
  },
  'jobAnalysis': {
    featureName: FEATURE_NAMES.JOB_ANALYSIS,
    upgradePromptId: 'jobAnalysisUpgradePrompt',
    usageTrackingKey: FEATURE_NAMES.JOB_ANALYSIS_KEY
  },
  'interviewPrep': {
    featureName: FEATURE_NAMES.INTERVIEW_PREP,
    upgradePromptId: 'interviewPrepUpgradePrompt',
    usageTrackingKey: FEATURE_NAMES.INTERVIEW_PREP_KEY
  },
  'linkedinOptimiser': {
    featureName: FEATURE_NAMES.LINKEDIN_OPTIMIZATION,
    upgradePromptId: 'linkedinOptimizationUpgradePrompt',
    usageTrackingKey: FEATURE_NAMES.LINKEDIN_OPTIMIZATION_KEY
  }
};

/**
 * Define the valid component IDs as a type
 */
export type ComponentId = keyof typeof COMPONENT_FEATURE_MAP;

/**
 * Get feature information based on component ID
 * @param componentId The component ID
 * @returns Feature information or undefined if not found
 */
export function getFeatureInfo(componentId: string) {
  // Type assertion to tell TypeScript that we're checking if the key exists
  return COMPONENT_FEATURE_MAP[componentId as ComponentId];
}
