/**
 * Centralized error handling utility for consistent error management across components
 */
export class ErrorHandler {
  private componentId: string;
  
  /**
   * Create a new ErrorHandler instance
   * @param componentId The ID prefix for the component (e.g., 'jobAnalysis', 'interviewPrep')
   */
  constructor(componentId: string) {
    this.componentId = componentId;
  }
  
  /**
   * Show an error modal with the given title and message
   * @param title The error title
   * @param message The error message
   * @param options Additional options for the error modal
   */
  async showError(title: string, message: string, options?: any): Promise<void> {
    try {
      const { showErrorModal } = await import("./errorHandling");
      showErrorModal(`${this.componentId}ErrorModal`, title, message, options);
    } catch (err) {
      console.error("Failed to import error handling utility:", err);
      alert(`${title}: ${message}`);
    }
  }
  
  /**
   * Create standardized error HTML for inline display
   * @param message The error message
   * @param details Optional technical details (e.g., stack trace)
   * @returns HTML string for the error
   */
  createErrorHTML(message: string, details?: string): string {
    return `
      <div class="max-w-3xl mx-auto bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800/50 p-6 rounded-2xl shadow-md text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/50 mb-4">
            <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg>
          </div>
          <h2 class="text-xl font-semibold mb-3 text-red-800 dark:text-red-300">
              Analysis Error
          </h2>
          <p class="text-red-700 dark:text-red-400 mb-4 text-sm">
              ${message} Please check your input or try again.
          </p>
          ${
            details
              ? `
          <details class="text-left text-xs text-red-600 dark:text-red-400/80 max-w-full mx-auto mt-4">
              <summary class="cursor-pointer font-medium hover:text-red-800 dark:hover:text-red-200">Technical Details</summary>
              <pre class="mt-2 p-3 bg-red-100 dark:bg-red-900/40 rounded-lg overflow-x-auto whitespace-pre-wrap break-words"><code>${details}</code></pre>
          </details>
          `
              : ""
          }
      </div>
    `;
  }
}
