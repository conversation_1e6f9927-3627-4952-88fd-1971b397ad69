export type FeatureUsageLimit = number

export type Feature =
  | 'resumeGeneration'
  | 'coverLetterGeneration'
  | 'jobAnalysis'
  | 'jobTrackers'
  | 'jobApplications'
  | 'interviewPrep'
  | 'linkedinOptimization'

export type FeatureUsageLimits = {
  [key in Feature]: FeatureUsageLimit
}

export type SubscriptionTierConfig = {
  name: string
  maxFeatureUses: number
  price: number
  billingCycle: 'monthly' | 'quarterly' | 'annual'
  features: string[]
  featureUsageLimits: FeatureUsageLimits
}

export const SUBSCRIPTION_TIERS = {
  free: {
    name: 'Free',
    price: 0,
    billingCycle: 'monthly',
    features: [
      '5 Resume Generation',
      '5 Cover Letter Generation',
      '5 Job research Requests',
      'Unlimited Job Application Tracking',
      '5 Interview Prep Sessions',
      '5 LinkedIn Optimizations'
    ],
    featureUsageLimits: {
      resumeGeneration: 5,
      coverLetterGeneration: 5,
      jobAnalysis: 5,
      jobTrackers: 5,
      jobApplications: 5,
      interviewPrep: 5,
      linkedinOptimization: 5
    }
  },
  pro: {
    name: 'Pro',
    price: 59900,
    billingCycle: 'monthly',
    features: [
      '300 Resume Generation',
      '300 Cover Letter Generation',
      '300 Job research Requests',
      '300 Job Application Tracking',
      '300 Interview Prep Sessions'
    ],
    featureUsageLimits: {
      resumeGeneration: 300,
      coverLetterGeneration: 300,
      jobAnalysis: 300,
      jobTrackers: 300,
      jobApplications: 300,
      interviewPrep: 300,
      linkedinOptimization: 300
    }
  },
  pro_quarterly: {
    name: 'Pro (Quarterly)',
    price: 149900, // ₹1499
    billingCycle: 'quarterly',
    features: [
      '300 Resume Generation',
      '300 Cover Letter Generation',
      '300 Job research Requests',
      '300 Job Application Tracking',
      '300 Interview Prep Sessions',
      '300 LinkedIn Profile Analysis',
      'Priority Support'
    ],
    featureUsageLimits: {
      resumeGeneration: 300,
      coverLetterGeneration: 300,
      jobAnalysis: 300,
      jobTrackers: 300,
      jobApplications: 300,
      interviewPrep: 300,
      linkedinOptimization: 300
    }
  }
} as const

export type SubscriptionTier = keyof typeof SUBSCRIPTION_TIERS
