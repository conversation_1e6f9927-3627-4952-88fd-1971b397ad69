import { authService } from "./auth";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "./errorHandler";
import { TierManagementService } from "./tierManagement";
import { getFeatureInfo } from "./featureConstants";

/**
 * Centralized component initializer for consistent component initialization across tools
 */
export class ComponentInitializer {
  private componentId: string;
  private errorHandler: ErrorHandler;

  /**
   * Create a new ComponentInitializer instance
   * @param componentId The ID prefix for the component (e.g., 'jobAnalysis', 'interviewPrep')
   */
  constructor(componentId: string) {
    this.componentId = componentId;
    this.errorHandler = new ErrorHandler(componentId);
  }

  /**
   * Checks if the user is authenticated
   * @returns Authentication status and user ID
   */
  async checkAuth(): Promise<{ isAuthenticated: boolean; userId: string | null }> {
    try {
      const user = await authService.getCurrentUser();
      return {
        isAuthenticated: !!user,
        userId: user?.uid || null
      };
    } catch (error) {
      await this.errorHandler.showError(
        "Authentication Error",
        "Failed to check authentication status"
      );
      return {
        isAuthenticated: false,
        userId: null
      };
    }
  }

  /**
   * Checks if the user has access to a feature
   * @param featureNameOverride Optional override for the feature name (if not using the mapped feature)
   * @returns Access status, user ID, usage info, and whether usage limit is reached
   */
  async checkFeatureAccess(featureNameOverride?: string): Promise<{
    canAccess: boolean;
    userId: string | null;
    usageInfo?: { current: number, limit: number };
    usageLimitReached?: boolean;
  }> {
    try {
      const { isAuthenticated, userId } = await this.checkAuth();

      if (!isAuthenticated || !userId) {
        // Show authentication required message
        const resultContainer = document.getElementById(`${this.componentId}ResultContainer`);
        if (resultContainer) {
          resultContainer.innerHTML = this.createAuthRequiredHTML();
          resultContainer.classList.remove('hidden');
        }
        return { canAccess: false, userId: null };
      }

      // Get feature name from mapping or use override
      const featureInfo = getFeatureInfo(this.componentId);
      const featureName = featureNameOverride || (featureInfo ? featureInfo.featureName : this.componentId);

      // Check if user has access to the feature
      const hasAccess = await TierManagementService.checkFeatureAccess(userId, featureName as any);

      // Get usage information
      const usageInfo = await this.getFeatureUsageInfo(userId, featureName);

      // Check if usage limit is reached
      const usageLimitReached = usageInfo ? usageInfo.current >= usageInfo.limit : false;

      if (!hasAccess) {
        // Show upgrade prompt using the mapped ID or fallback to componentId + "UpgradePrompt"
        const upgradePromptId = featureInfo ? featureInfo.upgradePromptId : `${this.componentId}UpgradePrompt`;

        import("../lib/upgradePrompt").then(({ showUpgradePrompt }) => {
          showUpgradePrompt(upgradePromptId);
        }).catch(err => {
          console.error("Failed to import upgrade prompt utility:", err);
        });

        return { canAccess: false, userId, usageInfo, usageLimitReached };
      }

      // If usage limit is reached, show upgrade prompt
      if (usageLimitReached) {
        const upgradePromptId = featureInfo ? featureInfo.upgradePromptId : `${this.componentId}UpgradePrompt`;

        import("../lib/upgradePrompt").then(({ showUpgradePrompt }) => {
          showUpgradePrompt(upgradePromptId);
        }).catch(err => {
          console.error("Failed to import upgrade prompt utility:", err);
        });
      }

      return {
        canAccess: true,
        userId,
        usageInfo,
        usageLimitReached
      };
    } catch (error) {
      await this.errorHandler.showError(
        "Access Check Error",
        "Failed to check feature access"
      );
      return {
        canAccess: false,
        userId: null
      };
    }
  }

  /**
   * Gets usage information for a feature
   * @param userId User ID
   * @param featureName Feature name
   * @returns Usage information (current and limit)
   */
  private async getFeatureUsageInfo(userId: string, featureName: string): Promise<{ current: number, limit: number } | undefined> {
    try {
      const tierStatus = await TierManagementService.getUserTierStatus(userId);

      // Map feature name to the correct key in tierStatus.featureUsage
      let featureKey: keyof typeof tierStatus.featureUsage;

      // Handle special cases
      if (featureName === 'coverLetter') {
        featureKey = 'coverLetterGeneration';
      } else if (featureName === 'linkedinOptimiser') {
        featureKey = 'linkedinOptimization';
      } else {
        featureKey = featureName as keyof typeof tierStatus.featureUsage;
      }

      // Check if the feature exists in the user's tier status
      if (tierStatus.featureUsage[featureKey]) {
        return {
          current: tierStatus.featureUsage[featureKey].usageCount,
          limit: tierStatus.featureUsage[featureKey].maxAllowedUsage
        };
      }

      return undefined;
    } catch (error) {
      console.error("Error getting feature usage info:", error);
      return undefined;
    }
  }

  /**
   * Tracks usage for the current component's feature
   * @param userId User ID
   * @returns Whether tracking was successful
   */
  async trackFeatureUsage(userId: string): Promise<boolean> {
    try {
      if (!userId) {
        console.warn(`Cannot track usage for ${this.componentId}: No user ID provided`);
        return false;
      }

      const featureInfo = getFeatureInfo(this.componentId);
      const usageTrackingKey = featureInfo ? featureInfo.usageTrackingKey : this.componentId;

      // Attempt to track feature usage
      try {
        await TierManagementService.trackFeatureUsage(userId, usageTrackingKey as any);
        return true;
      } catch (trackingError) {
        // Log the error but don't show it to the user - this is a non-critical operation
        console.error(`Error tracking usage for ${this.componentId}:`, trackingError);

        // Still return true since the main operation (generating content) was successful
        // We just couldn't track it properly
        return true;
      }
    } catch (error) {
      console.error(`Error in trackFeatureUsage for ${this.componentId}:`, error);
      // Don't show error to user for usage tracking issues
      return false;
    }
  }

  /**
   * Creates HTML for authentication required message
   * @returns HTML string for authentication required message
   */
  private createAuthRequiredHTML(): string {
    return `
      <div class="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800/50 p-6 rounded-2xl shadow-sm text-center max-w-2xl mx-auto">
          <h2 class="text-xl font-semibold mb-3 text-yellow-800 dark:text-yellow-300">
              Authentication Required
          </h2>
          <p class="text-yellow-700 dark:text-yellow-400">
              Please <a href="/login" class="font-medium underline hover:text-yellow-900 dark:hover:text-yellow-200">log in</a> or <a href="/register" class="font-medium underline hover:text-yellow-900 dark:hover:text-yellow-200">sign up</a> to use this feature.
          </p>
      </div>
    `;
  }


}
