/**
 * Utility functions for data handling and manipulation
 */
export class DataUtils {
  /**
   * Safely access nested properties in an object
   * @param obj The object to access
   * @param path The dot-notation path to the property
   * @param defaultValue The default value to return if the property doesn't exist
   * @returns The property value or the default value
   */
  static safeGet(obj: any, path: string, defaultValue: any = "N/A"): any {
    const keys = path.split(".");
    let result = obj;
    
    for (const key of keys) {
      if (
        result === null ||
        result === undefined ||
        typeof result !== "object"
      ) {
        return defaultValue;
      }
      result = result[key];
    }
    
    // Handle cases where the final value might be null/undefined or empty array/string
    if (result === null || result === undefined) return defaultValue;
    if (Array.isArray(result) && result.length === 0) return defaultValue;
    if (typeof result === "string" && result.trim() === "") return defaultValue;
    
    return result;
  }
  
  /**
   * Check if a value is empty or N/A
   * @param value The value to check
   * @returns Whether the value is empty or N/A
   */
  static isEmpty(value: any): boolean {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.trim() === '' || value.toLowerCase() === 'n/a';
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
  }
  
  /**
   * Format a value for display
   * @param value The value to format
   * @param defaultValue The default value to return if the value is empty
   * @returns The formatted value
   */
  static formatValue(value: any, defaultValue: string = "N/A"): string {
    if (this.isEmpty(value)) return defaultValue;
    if (Array.isArray(value)) return value.join(", ");
    return String(value);
  }

  /**
   * Deeply merges a source object into a target object.
   * This is used for progressive updates where new data chunks
   * overwrite or append to existing data.
   * @param target The target object to merge into.
   * @param source The source object to merge from.
   * @returns The merged object.
   */
  static deepMerge<T extends Record<string, any>>(target: T, source: Partial<T>): T {
    const output = { ...target };

    if (target && typeof target === 'object' && source && typeof source === 'object') {
      for (const key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key]) && target[key] && typeof target[key] === 'object' && !Array.isArray(target[key])) {
            // If both are objects (and not arrays), deep merge
            output[key] = this.deepMerge(target[key] as any, source[key] as any) as any;
          } else if (Array.isArray(source[key]) && Array.isArray(target[key])) {
            // If both are arrays, concatenate unique values
            output[key] = Array.from(new Set([...(target[key] as any[]), ...(source[key] as any[])])) as any;
          } else {
            // Otherwise, directly assign (overwrite)
            output[key] = source[key] as any;
          }
        }
      }
    }
    return output;
  }
}
