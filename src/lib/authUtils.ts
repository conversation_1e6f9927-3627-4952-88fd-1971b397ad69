import type { User } from 'firebase/auth';
import { getIdTokenResult } from 'firebase/auth';
import { initializeFirebase } from './firebase';

// Helper function to get environment variable with enhanced logging
function getEnvVar(key: string): string | undefined {
    // console.log(`Attempting to retrieve environment variable: ${key}`); // Removed for prod
    
    // Check Astro/Vite import.meta.env first
    if (import.meta.env) {
        // console.log('Checking import.meta.env'); // Removed for prod
        const value = import.meta.env[key];
        // console.log(`import.meta.env.${key}:`, value ? 'FOUND' : 'NOT FOUND'); // Removed for prod
        if (value) return value as string;
    }
    
    // If running in Node.js environment, try process.env
    if (typeof process !== 'undefined' && process.env) {
        // console.log('Checking process.env'); // Removed for prod
        const value = process.env[key];
        // console.log(`process.env.${key}:`, value ? 'FOUND' : 'NOT FOUND'); // Removed for prod
        if (value) return value;
    }
    
    // Log all available environment variables for debugging
    // console.warn(`Environment variable ${key} not found. Available variables:`); // Removed for prod
    // console.log('import.meta.env:', JSON.stringify(import.meta.env, null, 2)); // Removed for prod
    // if (typeof process !== 'undefined') { // Removed for prod
    //     console.log('process.env:', JSON.stringify(process.env, null, 2));
    // }
    
    return undefined;
}

// Removed validateAuthToken as client-side validation is insecure.

// Get current user with timeout
export async function getCurrentUserWithTimeout(timeoutMs = 5000): Promise<User | null> {
    const firebaseInstance = initializeFirebase();
    
    // if (!firebaseInstance) { // Removed console.warn for prod
    //     console.warn('Firebase not initialized in getCurrentUserWithTimeout');
    if (!firebaseInstance) {
        // console.warn('Firebase not initialized in getCurrentUserWithTimeout'); // Removed for prod
        return null;
    }

    const { auth } = firebaseInstance;

    return new Promise((resolve) => {
        // Set a timeout to prevent hanging
        const timeoutId = setTimeout(() => {
            // console.warn('getCurrentUserWithTimeout timed out'); // Removed for prod
            resolve(null);
        }, timeoutMs);

        // Use onAuthStateChanged to get the current user
        const unsubscribe = auth.onAuthStateChanged((user: User | null) => { // Added type
            clearTimeout(timeoutId);
            unsubscribe();
            resolve(user);
        }, (error: Error) => { // Added type
            clearTimeout(timeoutId);
            console.error('Error getting current user:', error); // Keep error log
            resolve(null);
        });
    });
}

// Removed clearAuthCookie as it is now handled by the secure /logout endpoint.
