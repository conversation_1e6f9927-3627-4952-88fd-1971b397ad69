import { authService } from "./auth";

/**
 * Centralized API client for consistent API calls across components
 * with support for optimistic updates
 */
export class APIClient {
  // Cache for optimistic responses
  private static cache: Record<string, any> = {};

  /**
   * Gets cached data for an endpoint if available
   * @param cacheKey The cache key for the data
   * @returns The cached data or null if not found
   */
  static getCachedData<T>(cacheKey: string): T | null {
    return this.cache[cacheKey] || null;
  }

  /**
   * Sets cached data for an endpoint
   * @param cacheKey The cache key for the data
   * @param data The data to cache
   */
  static setCachedData<T>(cacheKey: string, data: T): void {
    this.cache[cacheKey] = data;
  }

  /**
   * Clears cached data for an endpoint
   * @param cacheKey The cache key for the data to clear
   */
  static clearCachedData(cacheKey: string): void {
    delete this.cache[cacheKey];
  }
  /**
   * Makes an authenticated API request
   * @param endpoint The API endpoint to call
   * @param options Request options
   * @returns The response data
   */
  static async request<T = any>(
    endpoint: string,
    options: {
      method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
      body?: any;
      headers?: Record<string, string>;
      requireAuth?: boolean;
      useCache?: boolean;
      cacheKey?: string;
      optimisticData?: T;
      onOptimisticUpdate?: (data: T) => void;
    } = {}
  ): Promise<T> {
    const {
      method = 'GET',
      body,
      headers = {},
      requireAuth = true,
      useCache = false,
      cacheKey = `${endpoint}:${JSON.stringify(body || {})}`,
      optimisticData,
      onOptimisticUpdate
    } = options;

    // Check cache first if enabled
    if (useCache && method === 'GET') {
      const cachedData = this.getCachedData<T>(cacheKey);
      if (cachedData) {
        // Return cached data immediately
        return cachedData;
      }
    }

    // Apply optimistic update if provided
    if (optimisticData && onOptimisticUpdate) {
      // Call the optimistic update callback immediately
      onOptimisticUpdate(optimisticData);
    }

    // Set up headers
    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...headers
    };

    // Add authentication if required
    if (requireAuth) {
      const user = await authService.getCurrentUser();
      if (!user) {
        throw new Error('Authentication required');
      }

      const token = await user.getIdToken();
      requestHeaders['Authorization'] = `Bearer ${token}`;
    }

    try {
      // Make the request
      const response = await fetch(endpoint, {
        method,
        headers: requestHeaders,
        body: body ? JSON.stringify(body) : undefined
      });

      // Handle non-OK responses
      if (!response.ok) {
        let errorMessage = `Request failed with status ${response.status}`;

        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.message || errorMessage;
        } catch (e) {
          // If response is not JSON, try to get text
          try {
            const errorText = await response.text();
            if (errorText) {
              errorMessage = errorText;
            }
          } catch (textError) {
            // Ignore text parsing error
          }
        }

        throw new Error(errorMessage);
      }

      // Parse response
      let responseData: T;
      try {
        responseData = await response.json();
      } catch (e) {
        // Return empty object if response is not JSON
        responseData = {} as T;
      }

      // Cache the response if caching is enabled
      if (useCache) {
        this.setCachedData(cacheKey, responseData);
      }

      return responseData;
    } catch (error) {
      // If there was an optimistic update, we might want to revert it here
      // This would require additional logic in the onOptimisticUpdate callback
      throw error;
    }
  }

  /**
   * Makes a job research API request
   * @param data The job research request data
   * @returns The job research response data
   */
  static async analyzeJob<T = any>(data: {
    companyName: string;
    jobPosition: string;
  }): Promise<T> {
    return this.request<T>('/.netlify/functions/jobAnalysis', {
      method: 'POST',
      body: data
    });
  }

  /**
   * Makes a LinkedIn optimization API request
   * @param data The LinkedIn optimization request data
   * @returns The LinkedIn optimization response data
   */
  static async optimizeLinkedIn<T = any>(data: {
    linkedinContent: string;
    resumeContent: string;
    progressive?: boolean;
  }): Promise<T> {
    return this.request<T>('/.netlify/functions/linkedin-optimize', {
      method: 'POST',
      body: data
    });
  }

  /**
   * Makes a streaming cover letter generation API request
   * @param data The cover letter generation request data
   * @returns The response object for streaming
   */
  static async generateCoverLetterStream(data: {
    jobDescription: string;
    resumeContent: string;
    template?: string;
    customInstructions?: string;
  }): Promise<Response> {
    // Set up headers
    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    // Add authentication
    const user = await authService.getCurrentUser();
    if (!user) {
      throw new Error('Authentication required');
    }
    const token = await user.getIdToken();
    requestHeaders['Authorization'] = `Bearer ${token}`;

    // Make the request
    const response = await fetch('/.netlify/functions/generate-cover-letter', {
      method: 'POST',
      headers: requestHeaders,
      body: JSON.stringify(data)
    });

    // Handle non-OK responses
    if (!response.ok) {
      let errorMessage = `Request failed with status ${response.status}`;

      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorData.message || errorMessage;
      } catch (e) {
        // If response is not JSON, try to get text
        try {
          const errorText = await response.text();
          if (errorText) {
            errorMessage = errorText;
          }
        } catch (textError) {
          // Ignore text parsing error
        }
      }

      throw new Error(errorMessage);
    }

    return response;
  }

  /**
   * Makes a streaming resume generation API request
   * @param data The resume generation request data
   * @returns The response object for streaming
   */
  static async generateResumeStream(data: {
    jobDescription: string;
    resumeContent: string;
    customInstructions?: string;
  }): Promise<Response> {
    // Use the generic request method with streaming support
    // Note: The generic request method doesn't directly support streaming Response objects.
    // We need to modify it or handle streaming outside the generic method.
    // For now, we'll keep the direct fetch but add authentication.

    // Set up headers
    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    // Add authentication
    const user = await authService.getCurrentUser();
    if (!user) {
      throw new Error('Authentication required');
    }
    const token = await user.getIdToken();
    requestHeaders['Authorization'] = `Bearer ${token}`;


    // Make the request
    const response = await fetch('/.netlify/functions/generate-resume', {
      method: 'POST',
      headers: requestHeaders,
      body: JSON.stringify(data)
    });

    // Handle non-OK responses
    if (!response.ok) {
      let errorMessage = `Request failed with status ${response.status}`;

      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorData.message || errorMessage;
      } catch (e) {
        // If response is not JSON, try to get text
        try {
          const errorText = await response.text();
          if (errorText) {
            errorMessage = errorText;
          }
        } catch (textError) {
          // Ignore text parsing error
        }
      }

      throw new Error(errorMessage);
    }

    return response;
  }

  /**
   * Makes an optimistic API request that updates the UI immediately
   * @param endpoint The API endpoint to call
   * @param options Request options including optimistic data
   * @returns The actual response data
   */
  static async optimisticRequest<T = any>(
    endpoint: string,
    options: {
      method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
      body?: any;
      headers?: Record<string, string>;
      requireAuth?: boolean;
      optimisticData: T;
      onOptimisticUpdate: (data: T) => void;
      onRollback?: (error: Error) => void;
    }
  ): Promise<T> {
    const { onOptimisticUpdate, optimisticData, onRollback } = options;

    try {
      // Immediately update UI with optimistic data
      onOptimisticUpdate(optimisticData);

      // Make the actual request
      const result = await this.request<T>(endpoint, {
        ...options,
        // Don't call onOptimisticUpdate again from the request method
        onOptimisticUpdate: undefined,
        optimisticData: undefined
      });

      return result;
    } catch (error) {
      // If the request fails, call the rollback function if provided
      if (onRollback && error instanceof Error) {
        onRollback(error);
      }
      throw error;
    }
  }
}
