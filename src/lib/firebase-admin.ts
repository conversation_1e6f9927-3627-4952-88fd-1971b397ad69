import { initializeApp, cert, getApps } from 'firebase-admin/app';
import { getAuth, type Auth } from 'firebase-admin/auth';

let adminAuth: Auth | null = null;

function getAdminAuth() {
  // Check if we're in a server environment
  if (typeof process === 'undefined' || typeof window !== 'undefined') {
    throw new Error('Firebase Admin can only be used in server-side environment');
  }

  if (adminAuth) {
    return adminAuth;
  }

  if (getApps().length > 0) {
    const app = getApps()[0];
    adminAuth = getAuth(app);
    return adminAuth;
  }

  try {
    // Use the service account credentials directly
    const serviceAccount = {
      projectId: "astro-ai-professional",
      privateKey: "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDVRDPlofLlj9eT\nChZ25RvKYQD4UuP007hnt7AN1R1uyCAcyYDXv7Rg6X4I6riKjAgIzKXRBTbzpMlu\n1JtDjXsMq7qojmoQC+mMmK0N036MPwC64154qaj9kl4hdcvLEbngcQHizEv7LUJY\n7Wk4bvOQkR43RtI4EKzaP5Ff2OkBM3y9sB/J5HfL2NKus2jFS6hynXi0BXvvTFw\nv8ZljvufMJU9oHdXPyZwFXToJZ/D095xOAN3lq2gVqINUbEJAkh0ZymuxxoFxY+1\nr8slvIp1cvQjQvTpbcZUVQ9gtLReFEhkbyHZxLx9KIqtc8WY2mk7cUA7HTmg92uJ\nW6WMDAkJAgMBAAECggEAAeC81vLSsP1WO++MB3E73S/DbeXxJ0RcSvNOpK285mJm\nMDBNvWiZ21VNZ64lOjJ8NiA5ZA8oXuOzZd6C1pWp90oFhcTJdiZCtAfnbmTpIZQP\nvhvwxqRFmyQx+b6AERC3hI+xYTu+PgiV0IwceEaU33HmlcyQ0bbiA3lc052vUdme\nwisYMh2avSHl31asXBhlL/RSnnqsYoxTV7zcLZAC2NKbp4GQcc8t3ZVbDTSDnlmZ\nP1BRNgSYRJs+xvgArYTLYQ30OdKK/e1AU94mYeJR9B7QaTM5kdqp74Y86TUY3VFs\nlkn9XH8Xw0CEUwDRarTjYgVSqZBeXnsxGfvnGj0LEQKBgQD3d6q2w4IsOHUuI3gm\nWHgowFl4i5zaHlGNTHt8va4bXxVhF/Sb4aga8FagxKpv0fitTBU0h/kXtFShlnYv\nQdmW6absNqjwIf1VtK3vk0OKsOJHVHXZPgZuIme88sLgjL+LesrLXvS45kBpn0kx\nT+G03oB7vThqqO6+HqaVY4ZHEQKBgQDcnqblQIrbEkE7NCCNHB7BV4+WuF3ZDGIv\nYxINYWSA/a7+X+NDwRVA/lrxqA9jmP4Bh0eYaTgosR957m6fb3IzpOMvAbVO+ny7\ncVoden57tRjlUXmIdCASKhCHiyRg/e46G0J50eda83FwYEtU2Rmrf3CxHI/JKy2V\nN5nMO8FSeQKBgQCxhlEbBynM2zuEu8EnkPOyGDqW9WCyQGm0eZdkpxXNYOqPWoYJ\nhVn46Uzby/mQegpxF1Fm+9w/mwfTW4Kw6AZV8ovijFuo2FlKMHFOmlAroDTTgs1/\n6LToMRpGO8h9GbsXLiFov1fDhLnUi4YQMAP0omF9YwKj0Uhjbs2GAX2EYQKBgHzL\nJV7CToky30Es8EjBokf9TpST/0n6LkPdx2VYNPCeDvGGrdgyjOIxvqG5TdMpxGx6\n6C23PvQNlN901pBLCw9ZcsSxM6zUFeA1cyL/mjJBsy5sEtHz1fxQXP8+3roaiTnH\nF6gu6q5QzFcjsRjyoQmYUddtb4v8Z5FjXRTgrC8JAoGAKW3VTAosqEDlpqdcvlSN\nTPMkA1zc0nWymI+PoqYjsuF5X8zJHxFSp479veKW+cF+HyI7kL9zBaBZZnObARbX\n9p+lefCARa1pmz44PvV805RRi76Tn7YbIFv3YhfUEGEe0h2CPvm5K54Xoe5qwULk\nVxEVoYnpTbp8Xmyx+Wc7F4c=\n-----END PRIVATE KEY-----\n",
      clientEmail: "<EMAIL>"
    };
    
    const app = initializeApp({
      credential: cert(serviceAccount),
      projectId: "astro-ai-professional"
    });
    adminAuth = getAuth(app);
    return adminAuth;
  } catch (error) {
    console.error('Firebase Admin initialization error:', error);
    throw new Error('Firebase Admin initialization failed.');
  }
}


export const verifyIdToken = async (token: string) => {
  try {
    const auth = getAdminAuth();
    const decodedToken = await auth.verifyIdToken(token);
    return decodedToken;
  } catch (error) {
    console.error('Error verifying ID token:', error);
    return null;
  }
};

export const verifySessionCookie = async (sessionCookie: string) => {
  try {
    const auth = getAdminAuth();
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true);
    return decodedClaims;
  } catch (error) {
    console.error('Error verifying session cookie:', error);
    return null;
  }
};

export const getUserById = async (uid: string) => {
  try {
    const auth = getAdminAuth();
    const user = await auth.getUser(uid);
    return user;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return null;
  }
};
